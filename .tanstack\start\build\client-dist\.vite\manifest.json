{"../../~start/default-client-entry.tsx": {"file": "assets/main-Dj4JwwOF.js", "name": "main", "src": "../../~start/default-client-entry.tsx", "isEntry": true, "dynamicImports": ["src/routes/user.tsx?tsr-split=component", "src/routes/index.tsx?tsr-split=component", "src/routes/model/index.tsx?tsr-split=component", "src/routes/dashboard/index.tsx?tsr-split=component", "src/routes/ai/index.tsx?tsr-split=component", "src/routes/policy/service.tsx?tsr-split=component", "src/routes/policy/privacy.tsx?tsr-split=component", "src/routes/dashboard/user.tsx?tsr-split=component", "src/routes/auth/login.tsx?tsr-split=component", "src/routes/auth/dingtalk/callback.tsx?tsr-split=component"], "assets": ["assets/app-CwN8iJw8.css"]}, "D:/project/sinochem-agent/src/styles/app.css": {"file": "assets/app-CwN8iJw8.css", "src": "D:/project/sinochem-agent/src/styles/app.css"}, "_arrow-left-DlwdbL14.js": {"file": "assets/arrow-left-DlwdbL14.js", "name": "arrow-left", "imports": ["_createLucideIcon-C4-NQwlF.js"]}, "_arrow-right-BSmZkYW_.js": {"file": "assets/arrow-right-BSmZkYW_.js", "name": "arrow-right", "imports": ["_createLucideIcon-C4-NQwlF.js"]}, "_badge-Cavd9fBu.js": {"file": "assets/badge-Cavd9fBu.js", "name": "badge", "imports": ["../../~start/default-client-entry.tsx", "_index-_TRYHs0w.js"]}, "_button-D6FdW_Gn.js": {"file": "assets/button-D6FdW_Gn.js", "name": "button", "imports": ["../../~start/default-client-entry.tsx", "_index-_TRYHs0w.js"]}, "_card-BANwwG0l.js": {"file": "assets/card-BANwwG0l.js", "name": "card", "imports": ["../../~start/default-client-entry.tsx", "_index-_TRYHs0w.js"]}, "_createLucideIcon-C4-NQwlF.js": {"file": "assets/createLucideIcon-C4-NQwlF.js", "name": "createLucideIcon", "imports": ["../../~start/default-client-entry.tsx"]}, "_index-BDXR_-XV.js": {"file": "assets/index-BDXR_-XV.js", "name": "index", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_index-DRz5BQNA.js"]}, "_index-DRz5BQNA.js": {"file": "assets/index-DRz5BQNA.js", "name": "index"}, "_index-_TRYHs0w.js": {"file": "assets/index-_TRYHs0w.js", "name": "index"}, "_use-auth-BMRtWsEy.js": {"file": "assets/use-auth-BMRtWsEy.js", "name": "use-auth", "imports": ["../../~start/default-client-entry.tsx"]}, "_user-CJm5FPel.js": {"file": "assets/user-CJm5FPel.js", "name": "user", "imports": ["../../~start/default-client-entry.tsx", "_index-_TRYHs0w.js", "_button-D6FdW_Gn.js", "_createLucideIcon-C4-NQwlF.js"]}, "_user-ZXGCsiFi.js": {"file": "assets/user-ZXGCsiFi.js", "name": "user", "imports": ["../../~start/default-client-entry.tsx"]}, "_word-f0viOaJw.js": {"file": "assets/word-f0viOaJw.js", "name": "word", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_index-DRz5BQNA.js"]}, "node_modules/pdfjs-dist/build/pdf.mjs": {"file": "assets/pdf-CtA8PhPd.js", "name": "pdf", "src": "node_modules/pdfjs-dist/build/pdf.mjs", "isDynamicEntry": true}, "src/routes/ai/index.tsx?tsr-split=component": {"file": "assets/index-BndGZ-m4.js", "name": "index", "src": "src/routes/ai/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_index-_TRYHs0w.js", "_createLucideIcon-C4-NQwlF.js", "_user-CJm5FPel.js", "_arrow-right-BSmZkYW_.js", "_use-auth-BMRtWsEy.js", "_user-ZXGCsiFi.js", "_button-D6FdW_Gn.js", "_badge-Cavd9fBu.js"], "dynamicImports": ["_index-BDXR_-XV.js", "node_modules/pdfjs-dist/build/pdf.mjs", "_word-f0viOaJw.js"]}, "src/routes/auth/dingtalk/callback.tsx?tsr-split=component": {"file": "assets/callback-CoSvRuGU.js", "name": "callback", "src": "src/routes/auth/dingtalk/callback.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_use-auth-BMRtWsEy.js", "_user-ZXGCsiFi.js"]}, "src/routes/auth/login.tsx?tsr-split=component": {"file": "assets/login-B1SBvF3w.js", "name": "login", "src": "src/routes/auth/login.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_use-auth-BMRtWsEy.js", "_createLucideIcon-C4-NQwlF.js"]}, "src/routes/dashboard/index.tsx?tsr-split=component": {"file": "assets/index-CisFCKpq.js", "name": "index", "src": "src/routes/dashboard/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_card-BANwwG0l.js", "_badge-Cavd9fBu.js", "_user-ZXGCsiFi.js", "_index-_TRYHs0w.js"]}, "src/routes/dashboard/user.tsx?tsr-split=component": {"file": "assets/user-BFXINhBZ.js", "name": "user", "src": "src/routes/dashboard/user.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_card-BANwwG0l.js", "_button-D6FdW_Gn.js", "_badge-Cavd9fBu.js", "_user-ZXGCsiFi.js", "_index-_TRYHs0w.js"]}, "src/routes/index.tsx?tsr-split=component": {"file": "assets/index-BUgWAHMK.js", "name": "index", "src": "src/routes/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_button-D6FdW_Gn.js", "_badge-Cavd9fBu.js", "_index-_TRYHs0w.js", "_arrow-right-BSmZkYW_.js", "_use-auth-BMRtWsEy.js", "_createLucideIcon-C4-NQwlF.js"]}, "src/routes/model/index.tsx?tsr-split=component": {"file": "assets/index-BD1XoLML.js", "name": "index", "src": "src/routes/model/index.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_card-BANwwG0l.js", "_badge-Cavd9fBu.js", "_button-D6FdW_Gn.js", "_arrow-left-DlwdbL14.js", "_createLucideIcon-C4-NQwlF.js", "_index-_TRYHs0w.js"]}, "src/routes/policy/privacy.tsx?tsr-split=component": {"file": "assets/privacy-DRcutkEb.js", "name": "privacy", "src": "src/routes/policy/privacy.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_card-BANwwG0l.js", "_button-D6FdW_Gn.js", "_arrow-left-DlwdbL14.js", "_index-_TRYHs0w.js", "_createLucideIcon-C4-NQwlF.js"]}, "src/routes/policy/service.tsx?tsr-split=component": {"file": "assets/service-B6n29Tay.js", "name": "service", "src": "src/routes/policy/service.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_card-BANwwG0l.js", "_button-D6FdW_Gn.js", "_arrow-left-DlwdbL14.js", "_index-_TRYHs0w.js", "_createLucideIcon-C4-NQwlF.js"]}, "src/routes/user.tsx?tsr-split=component": {"file": "assets/user-BTD1RgvB.js", "name": "user", "src": "src/routes/user.tsx?tsr-split=component", "isDynamicEntry": true, "imports": ["../../~start/default-client-entry.tsx", "_user-CJm5FPel.js", "_card-BANwwG0l.js", "_button-D6FdW_Gn.js", "_user-ZXGCsiFi.js", "_use-auth-BMRtWsEy.js", "_arrow-left-DlwdbL14.js", "_createLucideIcon-C4-NQwlF.js", "_index-_TRYHs0w.js"]}}
/**
 * @fileoverview 认证状态管理 Hook
 * @description 提供用户认证状态管理功能，包括登录、登出、认证状态检查等
 */

import { useState, useEffect } from 'react'
import type { User } from '@/db/schema'

/**
 * 认证状态管理 Hook
 * @description 提供登录状态检查、用户信息管理等功能
 * @returns {Object} 包含用户信息、加载状态、认证状态及操作方法的对象
 * @returns {User | null} returns.user - 当前用户信息
 * @returns {boolean} returns.isLoading - 是否正在加载用户信息
 * @returns {boolean} returns.isAuthenticated - 用户是否已认证
 * @returns {Function} returns.login - 用户登录函数
 * @returns {Function} returns.logout - 用户登出函数
 * @returns {Function} returns.getAuthToken - 获取认证令牌函数
 * @returns {Function} returns.checkAuthStatus - 检查认证状态函数
 */
export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  /**
   * 检查用户登录状态
   * @description 从 localStorage 中获取用户信息和认证令牌，验证用户是否已登录
   */
  const checkAuthStatus = () => {
    try {
      // 检查 localStorage 中的用户信息
      const storedUser = localStorage.getItem('user')
      const authToken = localStorage.getItem('authToken')
      
      if (storedUser && authToken) {
        const userData = JSON.parse(storedUser)
        setUser(userData)
        setIsAuthenticated(true)
      } else {
        setUser(null)
        setIsAuthenticated(false)
      }
    } catch (error) {
      console.error('检查认证状态失败:', error)
      setUser(null)
      setIsAuthenticated(false)
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 用户登录
   * @description 保存用户信息和认证令牌到 localStorage，并更新状态
   * @param {User} userData - 用户信息对象
   * @param {string} token - 认证令牌
   * @throws {Error} 登录信息保存失败时抛出错误
   */
  const login = (userData: User, token: string) => {
    try {
      localStorage.setItem('user', JSON.stringify(userData))
      localStorage.setItem('authToken', token)
      setUser(userData)
      setIsAuthenticated(true)
    } catch (error) {
      console.error('登录失败:', error)
      throw new Error('登录信息保存失败')
    }
  }

  /**
   * 用户登出
   * @description 清除 localStorage 中的用户信息和认证令牌，并更新状态
   */
  const logout = () => {
    try {
      localStorage.removeItem('user')
      localStorage.removeItem('authToken')
      setUser(null)
      setIsAuthenticated(false)
    } catch (error) {
      console.error('登出失败:', error)
    }
  }

  /**
   * 获取认证令牌
   * @description 从 localStorage 中获取认证令牌
   * @returns {string | null} 返回认证令牌，如果不存在则返回 null
   */
  const getAuthToken = (): string | null => {
    try {
      return localStorage.getItem('authToken')
    } catch (error) {
      console.error('获取认证令牌失败:', error)
      return null
    }
  }

  // 组件挂载时检查认证状态
  useEffect(() => {
    checkAuthStatus()
  }, [])

  return {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    getAuthToken,
    checkAuthStatus
  }
}
import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'

// 动态导入阿里云SDK
const RPCClient = require('@alicloud/pop-core').RPCClient

/**
 * GetPhoneWithToken API - 一键登录取号（H5能力专用）
 * 调用阿里云GetPhoneWithToken接口，通过spToken换取用户完整手机号
 */
export const ServerRoute = createServerFileRoute('/api/auth/get-phone-with-token')
  .methods({
    POST: async ({ request }) => {
      try {
        const body = await request.json()
        const { spToken } = body

        console.log('GetPhoneWithToken请求参数:', { spToken: spToken?.substring(0, 20) + '...' })

        // 验证必需参数
        if (!spToken) {
          return json(
            { error: 'Missing required parameter: spToken' },
            { status: 400 }
          )
        }

        // 阿里云API配置
        const accessKeyId = process.env.VITE_ALIYUN_ACCESS_KEY_ID
        const accessKeySecret = process.env.VITE_ALIYUN_ACCESS_KEY_SECRET

        if (!accessKeyId || !accessKeySecret || accessKeyId === 'your_access_key_id') {
          return json(
            {
              error: 'Aliyun credentials not configured',
              message: '请在.env文件中配置正确的阿里云AccessKey'
            },
            { status: 500 }
          )
        }

        // 创建阿里云客户端
        const client = new RPCClient({
          accessKeyId: accessKeyId,
          accessKeySecret: accessKeySecret,
          endpoint: 'https://dypns.cn-hangzhou.aliyuncs.com',
          apiVersion: '2017-05-25'
        })

        // 构建请求参数
        const params = {
          SpToken: spToken
        }

        console.log('调用阿里云GetPhoneWithToken API')

        // 调用阿里云API
        const result = await client.request('GetPhoneWithToken', params, {
          method: 'POST'
        })

        console.log('阿里云GetPhoneWithToken API响应:', result)

        // 返回成功响应
        return json({
          code: result.Code,
          message: result.Message,
          requestId: result.RequestId,
          data: result.Data
        })

      } catch (error) {
        console.error('GetPhoneWithToken API代理错误:', error)
        return json(
          {
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : '服务器内部错误',
            details: error instanceof Error ? error.stack : undefined
          },
          { status: 500 }
        )
      }
    },

    // 处理预检请求
    OPTIONS: async () => {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      })
    }
  })

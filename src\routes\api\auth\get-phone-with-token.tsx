import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'

/**
 * GetPhoneWithToken API - 一键登录取号（H5能力专用）
 * 调用阿里云GetPhoneWithToken接口，通过spToken换取用户完整手机号
 */
export const ServerRoute = createServerFileRoute('/api/auth/get-phone-with-token')
  .methods({
    POST: async ({ request }) => {
      try {
        const body = await request.json()
        const { spToken } = body

        // 验证必需参数
        if (!spToken) {
          return json(
            { error: 'Missing required parameter: spToken' },
            { status: 400 }
          )
        }

        // 阿里云API配置
        const accessKeyId = process.env.VITE_ALIYUN_ACCESS_KEY_ID
        const accessKeySecret = process.env.VITE_ALIYUN_ACCESS_KEY_SECRET
        const endpoint = 'https://dypns.cn-hangzhou.aliyuncs.com'

        if (!accessKeyId || !accessKeySecret) {
          return json(
            { error: '<PERSON><PERSON> credentials not configured' },
            { status: 500 }
          )
        }

        // 构建请求参数
        const params = new URLSearchParams({
          Action: 'GetPhoneWithToken',
          Version: '2017-05-25',
          RegionId: 'cn-hangzhou',
          SpToken: spToken
        })

        // 生成签名
        const timestamp = new Date().toISOString()
        const nonce = Math.random().toString(36).substring(2, 15)
        
        // 简化的签名实现（实际项目中应使用阿里云SDK）
        const stringToSign = `POST&%2F&${encodeURIComponent(params.toString())}`
        
        // 调用阿里云API
        const response = await fetch(`${endpoint}/?${params.toString()}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Bearer ${accessKeyId}`, // 简化实现
          }
        })

        const data = await response.json()

        if (!response.ok) {
          console.error('阿里云GetPhoneWithToken API调用失败:', data)
          return json(
            { 
              error: 'Aliyun API Error',
              message: data.Message || '获取手机号失败',
              code: data.Code
            },
            { status: response.status }
          )
        }

        // 返回成功响应
        return json({
          code: data.Code,
          message: data.Message,
          requestId: data.RequestId,
          data: data.Data
        })

      } catch (error) {
        console.error('GetPhoneWithToken API代理错误:', error)
        return json(
          { 
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : '服务器内部错误'
          },
          { status: 500 }
        )
      }
    },

    // 处理预检请求
    OPTIONS: async () => {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      })
    }
  })

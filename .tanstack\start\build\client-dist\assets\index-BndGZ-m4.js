const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-BDXR_-XV.js","assets/main-Dj4JwwOF.js","assets/index-DRz5BQNA.js","assets/word-f0viOaJw.js"])))=>i.map(i=>d[i]);
import{g as ff,r as f,j as l,R as Ae,a as Aa,b as hf,c as pf,u as Pa,_ as lr,d as mf}from"./main-Dj4JwwOF.js";import{t as gf,a as ws,c as j,b as vf}from"./index-_TRYHs0w.js";import{c as re}from"./createLucideIcon-C4-NQwlF.js";import{L as Fr,u as Ke,b as Oe,P as J,d as Ea,c as Ze,e as yf,S as xf,A as Ra,a as Ma,U as bf,f as wf}from"./user-CJm5FPel.js";import{A as Cf}from"./arrow-right-BSmZkYW_.js";import{c as Sf,p as Tf,u as Pe,a as Da}from"./use-auth-BMRtWsEy.js";import{d as ue,e as X,f as pe,m as He,s as ur,h as Af,i as Cs,u as Pf,j as Ef}from"./user-ZXGCsiFi.js";import{u as ee,c as Rn,a as Na,B as ie,S as Jt,b as ja,d as Ia}from"./button-D6FdW_Gn.js";import{B as Rf}from"./badge-Cavd9fBu.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mf=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],ka=re("check",Mf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Df=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Oa=re("chevron-down",Df);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nf=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],La=re("chevron-right",Nf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jf=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],If=re("chevron-up",jf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kf=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],Of=re("circle",kf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lf=[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]],_f=re("copy",Lf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],Ff=re("download",Vf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bf=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],$f=re("file-text",Bf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],zf=re("log-out",Uf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wf=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Hf=re("menu",Wf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kf=[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]],Gf=re("message-square",Kf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yf=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]],Xf=re("panel-left-close",Yf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qf=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m14 9 3 3-3 3",key:"8010ee"}]],Zf=re("panel-left-open",qf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jf=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Qf=re("panel-left",Jf);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eh=[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]],th=re("paperclip",eh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nh=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],rh=re("pen-line",nh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sh=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],oh=re("plus",sh);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ih=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],ah=re("trash-2",ih);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ch=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Ss=re("x",ch);var dr,Oo;function lh(){return Oo||(Oo=1,dr=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,s,o;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(s=r;s--!==0;)if(!e(t[s],n[s]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(o=Object.keys(t),r=o.length,r!==Object.keys(n).length)return!1;for(s=r;s--!==0;)if(!Object.prototype.hasOwnProperty.call(n,o[s]))return!1;for(s=r;s--!==0;){var i=o[s];if(!e(t[i],n[i]))return!1}return!0}return t!==t&&n!==n}),dr}var uh=lh();const dh=ff(uh),Ts=f.createContext({});function As(e){const t=f.useRef(null);return t.current===null&&(t.current=e()),t.current}const Ps=typeof window<"u",_a=Ps?f.useLayoutEffect:f.useEffect,Un=f.createContext(null);function Es(e,t){e.indexOf(t)===-1&&e.push(t)}function Rs(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const Le=(e,t,n)=>n>t?t:n<e?e:n;let Ms=()=>{};const _e={},Va=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e);function Fa(e){return typeof e=="object"&&e!==null}const Ba=e=>/^0[^.\s]+$/u.test(e);function Ds(e){let t;return()=>(t===void 0&&(t=e()),t)}const xe=e=>e,fh=(e,t)=>n=>t(e(n)),Qt=(...e)=>e.reduce(fh),$t=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r};class Ns{constructor(){this.subscriptions=[]}add(t){return Es(this.subscriptions,t),()=>Rs(this.subscriptions,t)}notify(t,n,r){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,n,r);else for(let o=0;o<s;o++){const i=this.subscriptions[o];i&&i(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Me=e=>e*1e3,De=e=>e/1e3;function $a(e,t){return t?e*(1e3/t):0}const Ua=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,hh=1e-7,ph=12;function mh(e,t,n,r,s){let o,i,a=0;do i=t+(n-t)/2,o=Ua(i,r,s)-e,o>0?n=i:t=i;while(Math.abs(o)>hh&&++a<ph);return i}function en(e,t,n,r){if(e===t&&n===r)return xe;const s=o=>mh(o,0,1,e,n);return o=>o===0||o===1?o:Ua(s(o),t,r)}const za=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Wa=e=>t=>1-e(1-t),Ha=en(.33,1.53,.69,.99),js=Wa(Ha),Ka=za(js),Ga=e=>(e*=2)<1?.5*js(e):.5*(2-Math.pow(2,-10*(e-1))),Is=e=>1-Math.sin(Math.acos(e)),Ya=Wa(Is),Xa=za(Is),gh=en(.42,0,1,1),vh=en(0,0,.58,1),qa=en(.42,0,.58,1),yh=e=>Array.isArray(e)&&typeof e[0]!="number",Za=e=>Array.isArray(e)&&typeof e[0]=="number",xh={linear:xe,easeIn:gh,easeInOut:qa,easeOut:vh,circIn:Is,circInOut:Xa,circOut:Ya,backIn:js,backInOut:Ka,backOut:Ha,anticipate:Ga},bh=e=>typeof e=="string",Lo=e=>{if(Za(e)){Ms(e.length===4);const[t,n,r,s]=e;return en(t,n,r,s)}else if(bh(e))return xh[e];return e},un=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function wh(e,t){let n=new Set,r=new Set,s=!1,o=!1;const i=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1};function c(u){i.has(u)&&(d.schedule(u),e()),u(a)}const d={schedule:(u,h=!1,p=!1)=>{const g=p&&s?n:r;return h&&i.add(u),g.has(u)||g.add(u),u},cancel:u=>{r.delete(u),i.delete(u)},process:u=>{if(a=u,s){o=!0;return}s=!0,[n,r]=[r,n],n.forEach(c),n.clear(),s=!1,o&&(o=!1,d.process(u))}};return d}const Ch=40;function Ja(e,t){let n=!1,r=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,i=un.reduce((w,C)=>(w[C]=wh(o),w),{}),{setup:a,read:c,resolveKeyframes:d,preUpdate:u,update:h,preRender:p,render:m,postRender:g}=i,v=()=>{const w=_e.useManualTiming?s.timestamp:performance.now();n=!1,_e.useManualTiming||(s.delta=r?1e3/60:Math.max(Math.min(w-s.timestamp,Ch),1)),s.timestamp=w,s.isProcessing=!0,a.process(s),c.process(s),d.process(s),u.process(s),h.process(s),p.process(s),m.process(s),g.process(s),s.isProcessing=!1,n&&t&&(r=!1,e(v))},y=()=>{n=!0,r=!0,s.isProcessing||e(v)};return{schedule:un.reduce((w,C)=>{const S=i[C];return w[C]=(A,T=!1,E=!1)=>(n||y(),S.schedule(A,T,E)),w},{}),cancel:w=>{for(let C=0;C<un.length;C++)i[un[C]].cancel(w)},state:s,steps:i}}const{schedule:Y,cancel:Ge,state:oe,steps:fr}=Ja(typeof requestAnimationFrame<"u"?requestAnimationFrame:xe,!0);let wn;function Sh(){wn=void 0}const fe={now:()=>(wn===void 0&&fe.set(oe.isProcessing||_e.useManualTiming?oe.timestamp:performance.now()),wn),set:e=>{wn=e,queueMicrotask(Sh)}},Qa=e=>t=>typeof t=="string"&&t.startsWith(e),ks=Qa("--"),Th=Qa("var(--"),Os=e=>Th(e)?Ah.test(e.split("/*")[0].trim()):!1,Ah=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Pt={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Ut={...Pt,transform:e=>Le(0,1,e)},dn={...Pt,default:1},Lt=e=>Math.round(e*1e5)/1e5,Ls=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Ph(e){return e==null}const Eh=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,_s=(e,t)=>n=>!!(typeof n=="string"&&Eh.test(n)&&n.startsWith(e)||t&&!Ph(n)&&Object.prototype.hasOwnProperty.call(n,t)),ec=(e,t,n)=>r=>{if(typeof r!="string")return r;const[s,o,i,a]=r.match(Ls);return{[e]:parseFloat(s),[t]:parseFloat(o),[n]:parseFloat(i),alpha:a!==void 0?parseFloat(a):1}},Rh=e=>Le(0,255,e),hr={...Pt,transform:e=>Math.round(Rh(e))},tt={test:_s("rgb","red"),parse:ec("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+hr.transform(e)+", "+hr.transform(t)+", "+hr.transform(n)+", "+Lt(Ut.transform(r))+")"};function Mh(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const Br={test:_s("#"),parse:Mh,transform:tt.transform},tn=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),ze=tn("deg"),Ne=tn("%"),F=tn("px"),Dh=tn("vh"),Nh=tn("vw"),_o={...Ne,parse:e=>Ne.parse(e)/100,transform:e=>Ne.transform(e*100)},ht={test:_s("hsl","hue"),parse:ec("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+Ne.transform(Lt(t))+", "+Ne.transform(Lt(n))+", "+Lt(Ut.transform(r))+")"},te={test:e=>tt.test(e)||Br.test(e)||ht.test(e),parse:e=>tt.test(e)?tt.parse(e):ht.test(e)?ht.parse(e):Br.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?tt.transform(e):ht.transform(e),getAnimatableNone:e=>{const t=te.parse(e);return t.alpha=0,te.transform(t)}},jh=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Ih(e){return isNaN(e)&&typeof e=="string"&&(e.match(Ls)?.length||0)+(e.match(jh)?.length||0)>0}const tc="number",nc="color",kh="var",Oh="var(",Vo="${}",Lh=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function zt(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let o=0;const a=t.replace(Lh,c=>(te.test(c)?(r.color.push(o),s.push(nc),n.push(te.parse(c))):c.startsWith(Oh)?(r.var.push(o),s.push(kh),n.push(c)):(r.number.push(o),s.push(tc),n.push(parseFloat(c))),++o,Vo)).split(Vo);return{values:n,split:a,indexes:r,types:s}}function rc(e){return zt(e).values}function sc(e){const{split:t,types:n}=zt(e),r=t.length;return s=>{let o="";for(let i=0;i<r;i++)if(o+=t[i],s[i]!==void 0){const a=n[i];a===tc?o+=Lt(s[i]):a===nc?o+=te.transform(s[i]):o+=s[i]}return o}}const _h=e=>typeof e=="number"?0:te.test(e)?te.getAnimatableNone(e):e;function Vh(e){const t=rc(e);return sc(e)(t.map(_h))}const Ye={test:Ih,parse:rc,createTransformer:sc,getAnimatableNone:Vh};function pr(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Fh({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let s=0,o=0,i=0;if(!t)s=o=i=n;else{const a=n<.5?n*(1+t):n+t-n*t,c=2*n-a;s=pr(c,a,e+1/3),o=pr(c,a,e),i=pr(c,a,e-1/3)}return{red:Math.round(s*255),green:Math.round(o*255),blue:Math.round(i*255),alpha:r}}function Mn(e,t){return n=>n>0?t:e}const q=(e,t,n)=>e+(t-e)*n,mr=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},Bh=[Br,tt,ht],$h=e=>Bh.find(t=>t.test(e));function Fo(e){const t=$h(e);if(!t)return!1;let n=t.parse(e);return t===ht&&(n=Fh(n)),n}const Bo=(e,t)=>{const n=Fo(e),r=Fo(t);if(!n||!r)return Mn(e,t);const s={...n};return o=>(s.red=mr(n.red,r.red,o),s.green=mr(n.green,r.green,o),s.blue=mr(n.blue,r.blue,o),s.alpha=q(n.alpha,r.alpha,o),tt.transform(s))},$r=new Set(["none","hidden"]);function Uh(e,t){return $r.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function zh(e,t){return n=>q(e,t,n)}function Vs(e){return typeof e=="number"?zh:typeof e=="string"?Os(e)?Mn:te.test(e)?Bo:Kh:Array.isArray(e)?oc:typeof e=="object"?te.test(e)?Bo:Wh:Mn}function oc(e,t){const n=[...e],r=n.length,s=e.map((o,i)=>Vs(o)(o,t[i]));return o=>{for(let i=0;i<r;i++)n[i]=s[i](o);return n}}function Wh(e,t){const n={...e,...t},r={};for(const s in n)e[s]!==void 0&&t[s]!==void 0&&(r[s]=Vs(e[s])(e[s],t[s]));return s=>{for(const o in r)n[o]=r[o](s);return n}}function Hh(e,t){const n=[],r={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],i=e.indexes[o][r[o]],a=e.values[i]??0;n[s]=a,r[o]++}return n}const Kh=(e,t)=>{const n=Ye.createTransformer(t),r=zt(e),s=zt(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?$r.has(e)&&!s.values.length||$r.has(t)&&!r.values.length?Uh(e,t):Qt(oc(Hh(r,s),s.values),n):Mn(e,t)};function ic(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?q(e,t,n):Vs(e)(e,t)}const Gh=e=>{const t=({timestamp:n})=>e(n);return{start:(n=!0)=>Y.update(t,n),stop:()=>Ge(t),now:()=>oe.isProcessing?oe.timestamp:fe.now()}},ac=(e,t,n=10)=>{let r="";const s=Math.max(Math.round(t/n),2);for(let o=0;o<s;o++)r+=Math.round(e(o/(s-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},Dn=2e4;function Fs(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Dn;)t+=n,r=e.next(t);return t>=Dn?1/0:t}function Yh(e,t=100,n){const r=n({...e,keyframes:[0,t]}),s=Math.min(Fs(r),Dn);return{type:"keyframes",ease:o=>r.next(s*o).value/t,duration:De(s)}}const Xh=5;function cc(e,t,n){const r=Math.max(t-Xh,0);return $a(n-e(r),t-r)}const Z={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},gr=.001;function qh({duration:e=Z.duration,bounce:t=Z.bounce,velocity:n=Z.velocity,mass:r=Z.mass}){let s,o,i=1-t;i=Le(Z.minDamping,Z.maxDamping,i),e=Le(Z.minDuration,Z.maxDuration,De(e)),i<1?(s=d=>{const u=d*i,h=u*e,p=u-n,m=Ur(d,i),g=Math.exp(-h);return gr-p/m*g},o=d=>{const h=d*i*e,p=h*n+n,m=Math.pow(i,2)*Math.pow(d,2)*e,g=Math.exp(-h),v=Ur(Math.pow(d,2),i);return(-s(d)+gr>0?-1:1)*((p-m)*g)/v}):(s=d=>{const u=Math.exp(-d*e),h=(d-n)*e+1;return-gr+u*h},o=d=>{const u=Math.exp(-d*e),h=(n-d)*(e*e);return u*h});const a=5/e,c=Jh(s,o,a);if(e=Me(e),isNaN(c))return{stiffness:Z.stiffness,damping:Z.damping,duration:e};{const d=Math.pow(c,2)*r;return{stiffness:d,damping:i*2*Math.sqrt(r*d),duration:e}}}const Zh=12;function Jh(e,t,n){let r=n;for(let s=1;s<Zh;s++)r=r-e(r)/t(r);return r}function Ur(e,t){return e*Math.sqrt(1-t*t)}const Qh=["duration","bounce"],ep=["stiffness","damping","mass"];function $o(e,t){return t.some(n=>e[n]!==void 0)}function tp(e){let t={velocity:Z.velocity,stiffness:Z.stiffness,damping:Z.damping,mass:Z.mass,isResolvedFromDuration:!1,...e};if(!$o(e,ep)&&$o(e,Qh))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),s=r*r,o=2*Le(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:Z.mass,stiffness:s,damping:o}}else{const n=qh(e);t={...t,...n,mass:Z.mass},t.isResolvedFromDuration=!0}return t}function Nn(e=Z.visualDuration,t=Z.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:s}=n;const o=n.keyframes[0],i=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:c,damping:d,mass:u,duration:h,velocity:p,isResolvedFromDuration:m}=tp({...n,velocity:-De(n.velocity||0)}),g=p||0,v=d/(2*Math.sqrt(c*u)),y=i-o,x=De(Math.sqrt(c/u)),b=Math.abs(y)<5;r||(r=b?Z.restSpeed.granular:Z.restSpeed.default),s||(s=b?Z.restDelta.granular:Z.restDelta.default);let w;if(v<1){const S=Ur(x,v);w=A=>{const T=Math.exp(-v*x*A);return i-T*((g+v*x*y)/S*Math.sin(S*A)+y*Math.cos(S*A))}}else if(v===1)w=S=>i-Math.exp(-x*S)*(y+(g+x*y)*S);else{const S=x*Math.sqrt(v*v-1);w=A=>{const T=Math.exp(-v*x*A),E=Math.min(S*A,300);return i-T*((g+v*x*y)*Math.sinh(E)+S*y*Math.cosh(E))/S}}const C={calculatedDuration:m&&h||null,next:S=>{const A=w(S);if(m)a.done=S>=h;else{let T=S===0?g:0;v<1&&(T=S===0?Me(g):cc(w,S,A));const E=Math.abs(T)<=r,P=Math.abs(i-A)<=s;a.done=E&&P}return a.value=a.done?i:A,a},toString:()=>{const S=Math.min(Fs(C),Dn),A=ac(T=>C.next(S*T).value,S,30);return S+"ms "+A},toTransition:()=>{}};return C}Nn.applyToOptions=e=>{const t=Yh(e,100,Nn);return e.ease=t.ease,e.duration=Me(t.duration),e.type="keyframes",e};function zr({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:i,min:a,max:c,restDelta:d=.5,restSpeed:u}){const h=e[0],p={done:!1,value:h},m=E=>a!==void 0&&E<a||c!==void 0&&E>c,g=E=>a===void 0?c:c===void 0||Math.abs(a-E)<Math.abs(c-E)?a:c;let v=n*t;const y=h+v,x=i===void 0?y:i(y);x!==y&&(v=x-h);const b=E=>-v*Math.exp(-E/r),w=E=>x+b(E),C=E=>{const P=b(E),D=w(E);p.done=Math.abs(P)<=d,p.value=p.done?x:D};let S,A;const T=E=>{m(p.value)&&(S=E,A=Nn({keyframes:[p.value,g(p.value)],velocity:cc(w,E,p.value),damping:s,stiffness:o,restDelta:d,restSpeed:u}))};return T(0),{calculatedDuration:null,next:E=>{let P=!1;return!A&&S===void 0&&(P=!0,C(E),T(E)),S!==void 0&&E>=S?A.next(E-S):(!P&&C(E),p)}}}function np(e,t,n){const r=[],s=n||_e.mix||ic,o=e.length-1;for(let i=0;i<o;i++){let a=s(e[i],e[i+1]);if(t){const c=Array.isArray(t)?t[i]||xe:t;a=Qt(c,a)}r.push(a)}return r}function rp(e,t,{clamp:n=!0,ease:r,mixer:s}={}){const o=e.length;if(Ms(o===t.length),o===1)return()=>t[0];if(o===2&&t[0]===t[1])return()=>t[1];const i=e[0]===e[1];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=np(t,r,s),c=a.length,d=u=>{if(i&&u<e[0])return t[0];let h=0;if(c>1)for(;h<e.length-2&&!(u<e[h+1]);h++);const p=$t(e[h],e[h+1],u);return a[h](p)};return n?u=>d(Le(e[0],e[o-1],u)):d}function sp(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=$t(0,t,r);e.push(q(n,1,s))}}function op(e){const t=[0];return sp(t,e.length-1),t}function ip(e,t){return e.map(n=>n*t)}function ap(e,t){return e.map(()=>t||qa).splice(0,e.length-1)}function _t({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const s=yh(r)?r.map(Lo):Lo(r),o={done:!1,value:t[0]},i=ip(n&&n.length===t.length?n:op(t),e),a=rp(i,t,{ease:Array.isArray(s)?s:ap(t,s)});return{calculatedDuration:e,next:c=>(o.value=a(c),o.done=c>=e,o)}}const cp=e=>e!==null;function Bs(e,{repeat:t,repeatType:n="loop"},r,s=1){const o=e.filter(cp),a=s<0||t&&n!=="loop"&&t%2===1?0:o.length-1;return!a||r===void 0?o[a]:r}const lp={decay:zr,inertia:zr,tween:_t,keyframes:_t,spring:Nn};function lc(e){typeof e.type=="string"&&(e.type=lp[e.type])}class $s{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,n){return this.finished.then(t,n)}}const up=e=>e/100;class Us extends $s{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:n}=this.options;n&&n.updatedAt!==fe.now()&&this.tick(fe.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),t.autoplay===!1&&this.pause()}initAnimation(){const{options:t}=this;lc(t);const{type:n=_t,repeat:r=0,repeatDelay:s=0,repeatType:o,velocity:i=0}=t;let{keyframes:a}=t;const c=n||_t;c!==_t&&typeof a[0]!="number"&&(this.mixKeyframes=Qt(up,ic(a[0],a[1])),a=[0,100]);const d=c({...t,keyframes:a});o==="mirror"&&(this.mirroredGenerator=c({...t,keyframes:[...a].reverse(),velocity:-i})),d.calculatedDuration===null&&(d.calculatedDuration=Fs(d));const{calculatedDuration:u}=d;this.calculatedDuration=u,this.resolvedDuration=u+s,this.totalDuration=this.resolvedDuration*(r+1)-s,this.generator=d}updateTime(t){const n=Math.round(t-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(t,n=!1){const{generator:r,totalDuration:s,mixKeyframes:o,mirroredGenerator:i,resolvedDuration:a,calculatedDuration:c}=this;if(this.startTime===null)return r.next(0);const{delay:d=0,keyframes:u,repeat:h,repeatType:p,repeatDelay:m,type:g,onUpdate:v,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),n?this.currentTime=t:this.updateTime(t);const x=this.currentTime-d*(this.playbackSpeed>=0?1:-1),b=this.playbackSpeed>=0?x<0:x>s;this.currentTime=Math.max(x,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=s);let w=this.currentTime,C=r;if(h){const E=Math.min(this.currentTime,s)/a;let P=Math.floor(E),D=E%1;!D&&E>=1&&(D=1),D===1&&P--,P=Math.min(P,h+1),!!(P%2)&&(p==="reverse"?(D=1-D,m&&(D-=m/a)):p==="mirror"&&(C=i)),w=Le(0,1,D)*a}const S=b?{done:!1,value:u[0]}:C.next(w);o&&(S.value=o(S.value));let{done:A}=S;!b&&c!==null&&(A=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);const T=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&A);return T&&g!==zr&&(S.value=Bs(u,this.options,y,this.speed)),v&&v(S.value),T&&this.finish(),S}then(t,n){return this.finished.then(t,n)}get duration(){return De(this.calculatedDuration)}get time(){return De(this.currentTime)}set time(t){t=Me(t),this.currentTime=t,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(fe.now());const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=De(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Gh,startTime:n}=this.options;this.driver||(this.driver=t(s=>this.tick(s))),this.options.onPlay?.();const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=n??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(fe.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function dp(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}const nt=e=>e*180/Math.PI,Wr=e=>{const t=nt(Math.atan2(e[1],e[0]));return Hr(t)},fp={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:Wr,rotateZ:Wr,skewX:e=>nt(Math.atan(e[1])),skewY:e=>nt(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},Hr=e=>(e=e%360,e<0&&(e+=360),e),Uo=Wr,zo=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),Wo=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),hp={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:zo,scaleY:Wo,scale:e=>(zo(e)+Wo(e))/2,rotateX:e=>Hr(nt(Math.atan2(e[6],e[5]))),rotateY:e=>Hr(nt(Math.atan2(-e[2],e[0]))),rotateZ:Uo,rotate:Uo,skewX:e=>nt(Math.atan(e[4])),skewY:e=>nt(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function Kr(e){return e.includes("scale")?1:0}function Gr(e,t){if(!e||e==="none")return Kr(t);const n=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,s;if(n)r=hp,s=n;else{const a=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=fp,s=a}if(!s)return Kr(t);const o=r[t],i=s[1].split(",").map(mp);return typeof o=="function"?o(i):i[o]}const pp=(e,t)=>{const{transform:n="none"}=getComputedStyle(e);return Gr(n,t)};function mp(e){return parseFloat(e.trim())}const Et=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Rt=new Set(Et),Ho=e=>e===Pt||e===F,gp=new Set(["x","y","z"]),vp=Et.filter(e=>!gp.has(e));function yp(e){const t=[];return vp.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const rt={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>Gr(t,"x"),y:(e,{transform:t})=>Gr(t,"y")};rt.translateX=rt.x;rt.translateY=rt.y;const st=new Set;let Yr=!1,Xr=!1,qr=!1;function uc(){if(Xr){const e=Array.from(st).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const s=yp(r);s.length&&(n.set(r,s),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const s=n.get(r);s&&s.forEach(([o,i])=>{r.getValue(o)?.set(i)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Xr=!1,Yr=!1,st.forEach(e=>e.complete(qr)),st.clear()}function dc(){st.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Xr=!0)})}function xp(){qr=!0,dc(),uc(),qr=!1}class zs{constructor(t,n,r,s,o,i=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=s,this.element=o,this.isAsync=i}scheduleResolve(){this.state="scheduled",this.isAsync?(st.add(this),Yr||(Yr=!0,Y.read(dc),Y.resolveKeyframes(uc))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:s}=this;if(t[0]===null){const o=s?.get(),i=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const a=r.readValue(n,i);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=i),s&&o===void 0&&s.set(t[0])}dp(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),st.delete(this)}cancel(){this.state==="scheduled"&&(st.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const bp=e=>e.startsWith("--");function wp(e,t,n){bp(t)?e.style.setProperty(t,n):e.style[t]=n}const Cp=Ds(()=>window.ScrollTimeline!==void 0),Sp={};function Tp(e,t){const n=Ds(e);return()=>Sp[t]??n()}const fc=Tp(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),It=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Ko={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:It([0,.65,.55,1]),circOut:It([.55,0,1,.45]),backIn:It([.31,.01,.66,-.59]),backOut:It([.33,1.53,.69,.99])};function hc(e,t){if(e)return typeof e=="function"?fc()?ac(e,t):"ease-out":Za(e)?It(e):Array.isArray(e)?e.map(n=>hc(n,t)||Ko.easeOut):Ko[e]}function Ap(e,t,n,{delay:r=0,duration:s=300,repeat:o=0,repeatType:i="loop",ease:a="easeOut",times:c}={},d=void 0){const u={[t]:n};c&&(u.offset=c);const h=hc(a,s);Array.isArray(h)&&(u.easing=h);const p={delay:r,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:i==="reverse"?"alternate":"normal"};return d&&(p.pseudoElement=d),e.animate(u,p)}function pc(e){return typeof e=="function"&&"applyToOptions"in e}function Pp({type:e,...t}){return pc(e)&&fc()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}class Ep extends $s{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:n,name:r,keyframes:s,pseudoElement:o,allowFlatten:i=!1,finalKeyframe:a,onComplete:c}=t;this.isPseudoElement=!!o,this.allowFlatten=i,this.options=t,Ms(typeof t.type!="string");const d=Pp(t);this.animation=Ap(n,r,s,d,o),d.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const u=Bs(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(u):wp(n,r,u),this.animation.cancel()}c?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;t==="idle"||t==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return De(Number(t))}get time(){return De(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=Me(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:n}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Cp()?(this.animation.timeline=t,xe):n(this)}}const mc={anticipate:Ga,backInOut:Ka,circInOut:Xa};function Rp(e){return e in mc}function Mp(e){typeof e.ease=="string"&&Rp(e.ease)&&(e.ease=mc[e.ease])}const Go=10;class Dp extends Ep{constructor(t){Mp(t),lc(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:n,onUpdate:r,onComplete:s,element:o,...i}=this.options;if(!n)return;if(t!==void 0){n.set(t);return}const a=new Us({...i,autoplay:!1}),c=Me(this.finishedTime??this.time);n.setWithVelocity(a.sample(c-Go).value,a.sample(c).value,Go),a.stop()}}const Yo=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Ye.test(e)||e==="0")&&!e.startsWith("url("));function Np(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function jp(e,t,n,r){const s=e[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const o=e[e.length-1],i=Yo(s,t),a=Yo(o,t);return!i||!a?!1:Np(e)||(n==="spring"||pc(n))&&r}function Zr(e){e.duration=0,e.type}const Ip=new Set(["opacity","clipPath","filter","transform"]),kp=Ds(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function Op(e){const{motionValue:t,name:n,repeatDelay:r,repeatType:s,damping:o,type:i}=e;if(!(t?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:c,transformTemplate:d}=t.owner.getProps();return kp()&&n&&Ip.has(n)&&(n!=="transform"||!d)&&!c&&!r&&s!=="mirror"&&o!==0&&i!=="inertia"}const Lp=40;class _p extends $s{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:i="loop",keyframes:a,name:c,motionValue:d,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=fe.now();const p={autoplay:t,delay:n,type:r,repeat:s,repeatDelay:o,repeatType:i,name:c,motionValue:d,element:u,...h},m=u?.KeyframeResolver||zs;this.keyframeResolver=new m(a,(g,v,y)=>this.onKeyframesResolved(g,v,p,!y),c,d,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,n,r,s){this.keyframeResolver=void 0;const{name:o,type:i,velocity:a,delay:c,isHandoff:d,onUpdate:u}=r;this.resolvedAt=fe.now(),jp(t,o,i,a)||((_e.instantAnimations||!c)&&u?.(Bs(t,r,n)),t[0]=t[t.length-1],Zr(r),r.repeat=0);const p={startTime:s?this.resolvedAt?this.resolvedAt-this.createdAt>Lp?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...r,keyframes:t},m=!d&&Op(p)?new Dp({...p,element:p.motionValue.owner.current}):new Us(p);m.finished.then(()=>this.notifyFinished()).catch(xe),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(t,n){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),xp()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const Vp=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Fp(e){const t=Vp.exec(e);if(!t)return[,];const[,n,r,s]=t;return[`--${n??r}`,s]}function gc(e,t,n=1){const[r,s]=Fp(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const i=o.trim();return Va(i)?parseFloat(i):i}return Os(s)?gc(s,t,n+1):s}function Ws(e,t){return e?.[t]??e?.default??e}const vc=new Set(["width","height","top","left","right","bottom",...Et]),Bp={test:e=>e==="auto",parse:e=>e},yc=e=>t=>t.test(e),xc=[Pt,F,Ne,ze,Nh,Dh,Bp],Xo=e=>xc.find(yc(e));function $p(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Ba(e):!0}const Up=new Set(["brightness","contrast","saturate","opacity"]);function zp(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Ls)||[];if(!r)return e;const s=n.replace(r,"");let o=Up.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+s+")"}const Wp=/\b([a-z-]*)\(.*?\)/gu,Jr={...Ye,getAnimatableNone:e=>{const t=e.match(Wp);return t?t.map(zp).join(" "):e}},qo={...Pt,transform:Math.round},Hp={rotate:ze,rotateX:ze,rotateY:ze,rotateZ:ze,scale:dn,scaleX:dn,scaleY:dn,scaleZ:dn,skew:ze,skewX:ze,skewY:ze,distance:F,translateX:F,translateY:F,translateZ:F,x:F,y:F,z:F,perspective:F,transformPerspective:F,opacity:Ut,originX:_o,originY:_o,originZ:F},Hs={borderWidth:F,borderTopWidth:F,borderRightWidth:F,borderBottomWidth:F,borderLeftWidth:F,borderRadius:F,radius:F,borderTopLeftRadius:F,borderTopRightRadius:F,borderBottomRightRadius:F,borderBottomLeftRadius:F,width:F,maxWidth:F,height:F,maxHeight:F,top:F,right:F,bottom:F,left:F,padding:F,paddingTop:F,paddingRight:F,paddingBottom:F,paddingLeft:F,margin:F,marginTop:F,marginRight:F,marginBottom:F,marginLeft:F,backgroundPositionX:F,backgroundPositionY:F,...Hp,zIndex:qo,fillOpacity:Ut,strokeOpacity:Ut,numOctaves:qo},Kp={...Hs,color:te,backgroundColor:te,outlineColor:te,fill:te,stroke:te,borderColor:te,borderTopColor:te,borderRightColor:te,borderBottomColor:te,borderLeftColor:te,filter:Jr,WebkitFilter:Jr},bc=e=>Kp[e];function wc(e,t){let n=bc(e);return n!==Jr&&(n=Ye),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Gp=new Set(["auto","none","0"]);function Yp(e,t,n){let r=0,s;for(;r<e.length&&!s;){const o=e[r];typeof o=="string"&&!Gp.has(o)&&zt(o).values.length&&(s=e[r]),r++}if(s&&n)for(const o of t)e[o]=wc(n,s)}class Xp extends zs{constructor(t,n,r,s,o){super(t,n,r,s,o,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let c=0;c<t.length;c++){let d=t[c];if(typeof d=="string"&&(d=d.trim(),Os(d))){const u=gc(d,n.current);u!==void 0&&(t[c]=u),c===t.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!vc.has(r)||t.length!==2)return;const[s,o]=t,i=Xo(s),a=Xo(o);if(i!==a)if(Ho(i)&&Ho(a))for(let c=0;c<t.length;c++){const d=t[c];typeof d=="string"&&(t[c]=parseFloat(d))}else rt[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let s=0;s<t.length;s++)(t[s]===null||$p(t[s]))&&r.push(s);r.length&&Yp(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=rt[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&t.getValue(r,s).jump(s,!1)}measureEndState(){const{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;const s=t.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=r.length-1,i=r[o];r[o]=rt[n](t.measureViewportBox(),window.getComputedStyle(t.current)),i!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=i),this.removedTransforms?.length&&this.removedTransforms.forEach(([a,c])=>{t.getValue(a).set(c)}),this.resolveNoneKeyframes()}}function qp(e,t,n){if(e instanceof EventTarget)return[e];if(typeof e=="string"){let r=document;const s=n?.[e]??r.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}const Cc=(e,t)=>t&&typeof e=="number"?t.transform(e):e;function Sc(e){return Fa(e)&&"offsetHeight"in e}const Zo=30,Zp=e=>!isNaN(parseFloat(e));class Jp{constructor(t,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=r=>{const s=fe.now();if(this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const o of this.dependents)o.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=fe.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=Zp(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Ns);const r=this.events[t].add(n);return t==="change"?()=>{r(),Y.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=fe.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Zo)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Zo);return $a(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ct(e,t){return new Jp(e,t)}const{schedule:Ks}=Ja(queueMicrotask,!1),we={x:!1,y:!1};function Tc(){return we.x||we.y}function Qp(e){return e==="x"||e==="y"?we[e]?null:(we[e]=!0,()=>{we[e]=!1}):we.x||we.y?null:(we.x=we.y=!0,()=>{we.x=we.y=!1})}function Ac(e,t){const n=qp(e),r=new AbortController,s={passive:!0,...t,signal:r.signal};return[n,s,()=>r.abort()]}function Jo(e){return!(e.pointerType==="touch"||Tc())}function em(e,t,n={}){const[r,s,o]=Ac(e,n),i=a=>{if(!Jo(a))return;const{target:c}=a,d=t(c,a);if(typeof d!="function"||!c)return;const u=h=>{Jo(h)&&(d(h),c.removeEventListener("pointerleave",u))};c.addEventListener("pointerleave",u,s)};return r.forEach(a=>{a.addEventListener("pointerenter",i,s)}),o}const Pc=(e,t)=>t?e===t?!0:Pc(e,t.parentElement):!1,Gs=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,tm=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function nm(e){return tm.has(e.tagName)||e.tabIndex!==-1}const Cn=new WeakSet;function Qo(e){return t=>{t.key==="Enter"&&e(t)}}function vr(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const rm=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=Qo(()=>{if(Cn.has(n))return;vr(n,"down");const s=Qo(()=>{vr(n,"up")}),o=()=>vr(n,"cancel");n.addEventListener("keyup",s,t),n.addEventListener("blur",o,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function ei(e){return Gs(e)&&!Tc()}function sm(e,t,n={}){const[r,s,o]=Ac(e,n),i=a=>{const c=a.currentTarget;if(!ei(a))return;Cn.add(c);const d=t(c,a),u=(m,g)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",p),Cn.has(c)&&Cn.delete(c),ei(m)&&typeof d=="function"&&d(m,{success:g})},h=m=>{u(m,c===window||c===document||n.useGlobalTarget||Pc(c,m.target))},p=m=>{u(m,!1)};window.addEventListener("pointerup",h,s),window.addEventListener("pointercancel",p,s)};return r.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",i,s),Sc(a)&&(a.addEventListener("focus",d=>rm(d,s)),!nm(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function Ec(e){return Fa(e)&&"ownerSVGElement"in e}function om(e){return Ec(e)&&e.tagName==="svg"}const ce=e=>!!(e&&e.getVelocity),im=[...xc,te,Ye],am=e=>im.find(yc(e)),Ys=f.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class cm extends f.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=n.offsetParent,s=Sc(r)&&r.offsetWidth||0,o=this.props.sizeRef.current;o.height=n.offsetHeight||0,o.width=n.offsetWidth||0,o.top=n.offsetTop,o.left=n.offsetLeft,o.right=s-o.width-o.left}return null}componentDidUpdate(){}render(){return this.props.children}}function lm({children:e,isPresent:t,anchorX:n,root:r}){const s=f.useId(),o=f.useRef(null),i=f.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=f.useContext(Ys);return f.useInsertionEffect(()=>{const{width:c,height:d,top:u,left:h,right:p}=i.current;if(t||!o.current||!c||!d)return;const m=n==="left"?`left: ${h}`:`right: ${p}`;o.current.dataset.motionPopId=s;const g=document.createElement("style");a&&(g.nonce=a);const v=r??document.head;return v.appendChild(g),g.sheet&&g.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${c}px !important;
            height: ${d}px !important;
            ${m}px !important;
            top: ${u}px !important;
          }
        `),()=>{v.contains(g)&&v.removeChild(g)}},[t]),l.jsx(cm,{isPresent:t,childRef:o,sizeRef:i,children:f.cloneElement(e,{ref:o})})}const um=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:s,presenceAffectsLayout:o,mode:i,anchorX:a,root:c})=>{const d=As(dm),u=f.useId();let h=!0,p=f.useMemo(()=>(h=!1,{id:u,initial:t,isPresent:n,custom:s,onExitComplete:m=>{d.set(m,!0);for(const g of d.values())if(!g)return;r&&r()},register:m=>(d.set(m,!1),()=>d.delete(m))}),[n,d,r]);return o&&h&&(p={...p}),f.useMemo(()=>{d.forEach((m,g)=>d.set(g,!1))},[n]),f.useEffect(()=>{!n&&!d.size&&r&&r()},[n]),i==="popLayout"&&(e=l.jsx(lm,{isPresent:n,anchorX:a,root:c,children:e})),l.jsx(Un.Provider,{value:p,children:e})};function dm(){return new Map}function Rc(e=!0){const t=f.useContext(Un);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:s}=t,o=f.useId();f.useEffect(()=>{if(e)return s(o)},[e]);const i=f.useCallback(()=>e&&r&&r(o),[o,r,e]);return!n&&r?[!1,i]:[!0]}const fn=e=>e.key||"";function ti(e){const t=[];return f.Children.forEach(e,n=>{f.isValidElement(n)&&t.push(n)}),t}const Mc=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:s=!0,mode:o="sync",propagate:i=!1,anchorX:a="left",root:c})=>{const[d,u]=Rc(i),h=f.useMemo(()=>ti(e),[e]),p=i&&!d?[]:h.map(fn),m=f.useRef(!0),g=f.useRef(h),v=As(()=>new Map),[y,x]=f.useState(h),[b,w]=f.useState(h);_a(()=>{m.current=!1,g.current=h;for(let A=0;A<b.length;A++){const T=fn(b[A]);p.includes(T)?v.delete(T):v.get(T)!==!0&&v.set(T,!1)}},[b,p.length,p.join("-")]);const C=[];if(h!==y){let A=[...h];for(let T=0;T<b.length;T++){const E=b[T],P=fn(E);p.includes(P)||(A.splice(T,0,E),C.push(E))}return o==="wait"&&C.length&&(A=C),w(ti(A)),x(h),null}const{forceRender:S}=f.useContext(Ts);return l.jsx(l.Fragment,{children:b.map(A=>{const T=fn(A),E=i&&!d?!1:h===b||p.includes(T),P=()=>{if(v.has(T))v.set(T,!0);else return;let D=!0;v.forEach(O=>{O||(D=!1)}),D&&(S?.(),w(g.current),i&&u?.(),r&&r())};return l.jsx(um,{isPresent:E,initial:!m.current||n?void 0:!1,custom:t,presenceAffectsLayout:s,mode:o,root:c,onExitComplete:E?void 0:P,anchorX:a,children:A},T)})})},Dc=f.createContext({strict:!1}),ni={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},St={};for(const e in ni)St[e]={isEnabled:t=>ni[e].some(n=>!!t[n])};function fm(e){for(const t in e)St[t]={...St[t],...e[t]}}const hm=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function jn(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||hm.has(e)}let Nc=e=>!jn(e);function pm(e){typeof e=="function"&&(Nc=t=>t.startsWith("on")?!jn(t):e(t))}try{pm(require("@emotion/is-prop-valid").default)}catch{}function mm(e,t,n){const r={};for(const s in e)s==="values"&&typeof e.values=="object"||(Nc(s)||n===!0&&jn(s)||!t&&!jn(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}const zn=f.createContext({});function Wn(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}function Wt(e){return typeof e=="string"||Array.isArray(e)}const Xs=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],qs=["initial",...Xs];function Hn(e){return Wn(e.animate)||qs.some(t=>Wt(e[t]))}function jc(e){return!!(Hn(e)||e.variants)}function gm(e,t){if(Hn(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Wt(n)?n:void 0,animate:Wt(r)?r:void 0}}return e.inherit!==!1?t:{}}function vm(e){const{initial:t,animate:n}=gm(e,f.useContext(zn));return f.useMemo(()=>({initial:t,animate:n}),[ri(t),ri(n)])}function ri(e){return Array.isArray(e)?e.join(" "):e}const Ht={};function ym(e){for(const t in e)Ht[t]=e[t],ks(t)&&(Ht[t].isCSSVariable=!0)}function Ic(e,{layout:t,layoutId:n}){return Rt.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ht[e]||e==="opacity")}const xm={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},bm=Et.length;function wm(e,t,n){let r="",s=!0;for(let o=0;o<bm;o++){const i=Et[o],a=e[i];if(a===void 0)continue;let c=!0;if(typeof a=="number"?c=a===(i.startsWith("scale")?1:0):c=parseFloat(a)===0,!c||n){const d=Cc(a,Hs[i]);if(!c){s=!1;const u=xm[i]||i;r+=`${u}(${d}) `}n&&(t[i]=d)}}return r=r.trim(),n?r=n(t,s?"":r):s&&(r="none"),r}function Zs(e,t,n){const{style:r,vars:s,transformOrigin:o}=e;let i=!1,a=!1;for(const c in t){const d=t[c];if(Rt.has(c)){i=!0;continue}else if(ks(c)){s[c]=d;continue}else{const u=Cc(d,Hs[c]);c.startsWith("origin")?(a=!0,o[c]=u):r[c]=u}}if(t.transform||(i||n?r.transform=wm(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:c="50%",originY:d="50%",originZ:u=0}=o;r.transformOrigin=`${c} ${d} ${u}`}}const Js=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function kc(e,t,n){for(const r in t)!ce(t[r])&&!Ic(r,n)&&(e[r]=t[r])}function Cm({transformTemplate:e},t){return f.useMemo(()=>{const n=Js();return Zs(n,t,e),Object.assign({},n.vars,n.style)},[t])}function Sm(e,t){const n=e.style||{},r={};return kc(r,n,e),Object.assign(r,Cm(e,t)),r}function Tm(e,t){const n={},r=Sm(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}const Am={offset:"stroke-dashoffset",array:"stroke-dasharray"},Pm={offset:"strokeDashoffset",array:"strokeDasharray"};function Em(e,t,n=1,r=0,s=!0){e.pathLength=1;const o=s?Am:Pm;e[o.offset]=F.transform(-r);const i=F.transform(t),a=F.transform(n);e[o.array]=`${i} ${a}`}function Oc(e,{attrX:t,attrY:n,attrScale:r,pathLength:s,pathSpacing:o=1,pathOffset:i=0,...a},c,d,u){if(Zs(e,a,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:p}=e;h.transform&&(p.transform=h.transform,delete h.transform),(p.transform||h.transformOrigin)&&(p.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),p.transform&&(p.transformBox=u?.transformBox??"fill-box",delete h.transformBox),t!==void 0&&(h.x=t),n!==void 0&&(h.y=n),r!==void 0&&(h.scale=r),s!==void 0&&Em(h,s,o,i,!1)}const Lc=()=>({...Js(),attrs:{}}),_c=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Rm(e,t,n,r){const s=f.useMemo(()=>{const o=Lc();return Oc(o,t,_c(r),e.transformTemplate,e.style),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};kc(o,e.style,e),s.style={...o,...s.style}}return s}const Mm=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Qs(e){return typeof e!="string"||e.includes("-")?!1:!!(Mm.indexOf(e)>-1||/[A-Z]/u.test(e))}function Dm(e,t,n,{latestValues:r},s,o=!1){const a=(Qs(e)?Rm:Tm)(t,r,s,e),c=mm(t,typeof e=="string",o),d=e!==f.Fragment?{...c,...a,ref:n}:{},{children:u}=t,h=f.useMemo(()=>ce(u)?u.get():u,[u]);return f.createElement(e,{...d,children:h})}function si(e){const t=[{},{}];return e?.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function eo(e,t,n,r){if(typeof t=="function"){const[s,o]=si(r);t=t(n!==void 0?n:e.custom,s,o)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[s,o]=si(r);t=t(n!==void 0?n:e.custom,s,o)}return t}function Sn(e){return ce(e)?e.get():e}function Nm({scrapeMotionValuesFromProps:e,createRenderState:t},n,r,s){return{latestValues:jm(n,r,s,e),renderState:t()}}function jm(e,t,n,r){const s={},o=r(e,{});for(const p in o)s[p]=Sn(o[p]);let{initial:i,animate:a}=e;const c=Hn(e),d=jc(e);t&&d&&!c&&e.inherit!==!1&&(i===void 0&&(i=t.initial),a===void 0&&(a=t.animate));let u=n?n.initial===!1:!1;u=u||i===!1;const h=u?a:i;if(h&&typeof h!="boolean"&&!Wn(h)){const p=Array.isArray(h)?h:[h];for(let m=0;m<p.length;m++){const g=eo(e,p[m]);if(g){const{transitionEnd:v,transition:y,...x}=g;for(const b in x){let w=x[b];if(Array.isArray(w)){const C=u?w.length-1:0;w=w[C]}w!==null&&(s[b]=w)}for(const b in v)s[b]=v[b]}}}return s}const Vc=e=>(t,n)=>{const r=f.useContext(zn),s=f.useContext(Un),o=()=>Nm(e,t,r,s);return n?o():As(o)};function to(e,t,n){const{style:r}=e,s={};for(const o in r)(ce(r[o])||t.style&&ce(t.style[o])||Ic(o,e)||n?.getValue(o)?.liveStyle!==void 0)&&(s[o]=r[o]);return s}const Im=Vc({scrapeMotionValuesFromProps:to,createRenderState:Js});function Fc(e,t,n){const r=to(e,t,n);for(const s in e)if(ce(e[s])||ce(t[s])){const o=Et.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;r[o]=e[s]}return r}const km=Vc({scrapeMotionValuesFromProps:Fc,createRenderState:Lc}),Om=Symbol.for("motionComponentSymbol");function pt(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function Lm(e,t,n){return f.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):pt(n)&&(n.current=r))},[t])}const no=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),_m="framerAppearId",Bc="data-"+no(_m),$c=f.createContext({});function Vm(e,t,n,r,s){const{visualElement:o}=f.useContext(zn),i=f.useContext(Dc),a=f.useContext(Un),c=f.useContext(Ys).reducedMotion,d=f.useRef(null);r=r||i.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:c}));const u=d.current,h=f.useContext($c);u&&!u.projection&&s&&(u.type==="html"||u.type==="svg")&&Fm(d.current,n,s,h);const p=f.useRef(!1);f.useInsertionEffect(()=>{u&&p.current&&u.update(n,a)});const m=n[Bc],g=f.useRef(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return _a(()=>{u&&(p.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),u.scheduleRenderMicrotask(),g.current&&u.animationState&&u.animationState.animateChanges())}),f.useEffect(()=>{u&&(!g.current&&u.animationState&&u.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),g.current=!1),u.enteringChildren=void 0)}),u}function Fm(e,t,n,r){const{layoutId:s,layout:o,drag:i,dragConstraints:a,layoutScroll:c,layoutRoot:d,layoutCrossfade:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Uc(e.parent)),e.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:!!i||a&&pt(a),visualElement:e,animationType:typeof o=="string"?o:"both",initialPromotionConfig:r,crossfade:u,layoutScroll:c,layoutRoot:d})}function Uc(e){if(e)return e.options.allowProjection!==!1?e.projection:Uc(e.parent)}function yr(e,{forwardMotionProps:t=!1}={},n,r){n&&fm(n);const s=Qs(e)?km:Im;function o(a,c){let d;const u={...f.useContext(Ys),...a,layoutId:Bm(a)},{isStatic:h}=u,p=vm(a),m=s(a,h);if(!h&&Ps){$m();const g=Um(u);d=g.MeasureLayout,p.visualElement=Vm(e,m,u,r,g.ProjectionNode)}return l.jsxs(zn.Provider,{value:p,children:[d&&p.visualElement?l.jsx(d,{visualElement:p.visualElement,...u}):null,Dm(e,a,Lm(m,p.visualElement,c),m,h,t)]})}o.displayName=`motion.${typeof e=="string"?e:`create(${e.displayName??e.name??""})`}`;const i=f.forwardRef(o);return i[Om]=e,i}function Bm({layoutId:e}){const t=f.useContext(Ts).id;return t&&e!==void 0?t+"-"+e:e}function $m(e,t){f.useContext(Dc).strict}function Um(e){const{drag:t,layout:n}=St;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t?.isEnabled(e)||n?.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}function zm(e,t){if(typeof Proxy>"u")return yr;const n=new Map,r=(o,i)=>yr(o,i,e,t),s=(o,i)=>r(o,i);return new Proxy(s,{get:(o,i)=>i==="create"?r:(n.has(i)||n.set(i,yr(i,void 0,e,t)),n.get(i))})}function zc({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function Wm({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Hm(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function xr(e){return e===void 0||e===1}function Qr({scale:e,scaleX:t,scaleY:n}){return!xr(e)||!xr(t)||!xr(n)}function et(e){return Qr(e)||Wc(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Wc(e){return oi(e.x)||oi(e.y)}function oi(e){return e&&e!=="0%"}function In(e,t,n){const r=e-n,s=t*r;return n+s}function ii(e,t,n,r,s){return s!==void 0&&(e=In(e,s,r)),In(e,n,r)+t}function es(e,t=0,n=1,r,s){e.min=ii(e.min,t,n,r,s),e.max=ii(e.max,t,n,r,s)}function Hc(e,{x:t,y:n}){es(e.x,t.translate,t.scale,t.originPoint),es(e.y,n.translate,n.scale,n.originPoint)}const ai=.999999999999,ci=1.0000000000001;function Km(e,t,n,r=!1){const s=n.length;if(!s)return;t.x=t.y=1;let o,i;for(let a=0;a<s;a++){o=n[a],i=o.projectionDelta;const{visualElement:c}=o.options;c&&c.props.style&&c.props.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&gt(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),i&&(t.x*=i.x.scale,t.y*=i.y.scale,Hc(e,i)),r&&et(o.latestValues)&&gt(e,o.latestValues))}t.x<ci&&t.x>ai&&(t.x=1),t.y<ci&&t.y>ai&&(t.y=1)}function mt(e,t){e.min=e.min+t,e.max=e.max+t}function li(e,t,n,r,s=.5){const o=q(e.min,e.max,s);es(e,t,n,o,r)}function gt(e,t){li(e.x,t.x,t.scaleX,t.scale,t.originX),li(e.y,t.y,t.scaleY,t.scale,t.originY)}function Kc(e,t){return zc(Hm(e.getBoundingClientRect(),t))}function Gm(e,t,n){const r=Kc(e,n),{scroll:s}=t;return s&&(mt(r.x,s.offset.x),mt(r.y,s.offset.y)),r}const ui=()=>({translate:0,scale:1,origin:0,originPoint:0}),vt=()=>({x:ui(),y:ui()}),di=()=>({min:0,max:0}),Q=()=>({x:di(),y:di()}),ts={current:null},Gc={current:!1};function Ym(){if(Gc.current=!0,!!Ps)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>ts.current=e.matches;e.addEventListener("change",t),t()}else ts.current=!1}const Xm=new WeakMap;function qm(e,t,n){for(const r in t){const s=t[r],o=n[r];if(ce(s))e.addValue(r,s);else if(ce(o))e.addValue(r,Ct(s,{owner:e}));else if(o!==s)if(e.hasValue(r)){const i=e.getValue(r);i.liveStyle===!0?i.jump(s):i.hasAnimated||i.set(s)}else{const i=e.getStaticValue(r);e.addValue(r,Ct(i!==void 0?i:s,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const fi=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Zm{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:s,blockInitialAnimation:o,visualState:i},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=zs,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const p=fe.now();this.renderScheduledAt<p&&(this.renderScheduledAt=p,Y.render(this.render,!1,!0))};const{latestValues:c,renderState:d}=i;this.latestValues=c,this.baseTarget={...c},this.initialValues=n.initial?{...c}:{},this.renderState=d,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Hn(n),this.isVariantNode=jc(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const p in h){const m=h[p];c[p]!==void 0&&ce(m)&&m.set(c[p])}}mount(t){this.current=t,Xm.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Gc.current||Ym(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ts.current,this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ge(this.notifyUpdate),Ge(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=Rt.has(t);r&&this.onBindTransform&&this.onBindTransform();const s=n.on("change",i=>{this.latestValues[t]=i,this.props.onUpdate&&Y.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{s(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in St){const n=St[t];if(!n)continue;const{isEnabled:r,Feature:s}=n;if(!this.features[t]&&s&&r(this.props)&&(this.features[t]=new s(this)),this.features[t]){const o=this.features[t];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Q()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<fi.length;r++){const s=fi[r];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const o="on"+s,i=t[o];i&&(this.propEventSubscriptions[s]=this.on(s,i))}this.prevMotionValues=qm(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Ct(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){let r=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options);return r!=null&&(typeof r=="string"&&(Va(r)||Ba(r))?r=parseFloat(r):!am(r)&&Ye.test(n)&&(r=wc(t,n)),this.setBaseTarget(t,ce(r)?r.get():r)),ce(r)?r.get():r}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){const{initial:n}=this.props;let r;if(typeof n=="string"||typeof n=="object"){const o=eo(this.props,n,this.presenceContext?.custom);o&&(r=o[t])}if(n&&r!==void 0)return r;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!ce(s)?s:this.initialValues[t]!==void 0&&r===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Ns),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}scheduleRenderMicrotask(){Ks.render(this.render)}}class Yc extends Zm{constructor(){super(...arguments),this.KeyframeResolver=Xp}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ce(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Xc(e,{style:t,vars:n},r,s){const o=e.style;let i;for(i in t)o[i]=t[i];s?.applyProjectionStyles(o,r);for(i in n)o.setProperty(i,n[i])}function Jm(e){return window.getComputedStyle(e)}class Qm extends Yc{constructor(){super(...arguments),this.type="html",this.renderInstance=Xc}readValueFromInstance(t,n){if(Rt.has(n))return this.projection?.isProjecting?Kr(n):pp(t,n);{const r=Jm(t),s=(ks(n)?r.getPropertyValue(n):r[n])||0;return typeof s=="string"?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Kc(t,n)}build(t,n,r){Zs(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return to(t,n,r)}}const qc=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function eg(e,t,n,r){Xc(e,t,void 0,r);for(const s in t.attrs)e.setAttribute(qc.has(s)?s:no(s),t.attrs[s])}class tg extends Yc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Q}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(Rt.has(n)){const r=bc(n);return r&&r.default||0}return n=qc.has(n)?n:no(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Fc(t,n,r)}build(t,n,r){Oc(t,n,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(t,n,r,s){eg(t,n,r,s)}mount(t){this.isSVGTag=_c(t.tagName),super.mount(t)}}const ng=(e,t)=>Qs(e)?new tg(t):new Qm(t,{allowProjection:e!==f.Fragment});function yt(e,t,n){const r=e.getProps();return eo(r,t,n!==void 0?n:r.custom,e)}const ns=e=>Array.isArray(e);function rg(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Ct(n))}function sg(e){return ns(e)?e[e.length-1]||0:e}function og(e,t){const n=yt(e,t);let{transitionEnd:r={},transition:s={},...o}=n||{};o={...o,...r};for(const i in o){const a=sg(o[i]);rg(e,i,a)}}function ig(e){return!!(ce(e)&&e.add)}function rs(e,t){const n=e.getValue("willChange");if(ig(n))return n.add(t);if(!n&&_e.WillChange){const r=new _e.WillChange("auto");e.addValue("willChange",r),r.add(t)}}function Zc(e){return e.props[Bc]}const ag=e=>e!==null;function cg(e,{repeat:t,repeatType:n="loop"},r){const s=e.filter(ag),o=t&&n!=="loop"&&t%2===1?0:s.length-1;return s[o]}const lg={type:"spring",stiffness:500,damping:25,restSpeed:10},ug=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),dg={type:"keyframes",duration:.8},fg={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},hg=(e,{keyframes:t})=>t.length>2?dg:Rt.has(e)?e.startsWith("scale")?ug(t[1]):lg:fg;function pg({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:o,repeatType:i,repeatDelay:a,from:c,elapsed:d,...u}){return!!Object.keys(u).length}const ro=(e,t,n,r={},s,o)=>i=>{const a=Ws(r,e)||{},c=a.delay||r.delay||0;let{elapsed:d=0}=r;d=d-Me(c);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-d,onUpdate:p=>{t.set(p),a.onUpdate&&a.onUpdate(p)},onComplete:()=>{i(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:o?void 0:s};pg(a)||Object.assign(u,hg(e,u)),u.duration&&(u.duration=Me(u.duration)),u.repeatDelay&&(u.repeatDelay=Me(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(Zr(u),u.delay===0&&(h=!0)),(_e.instantAnimations||_e.skipAnimations)&&(h=!0,Zr(u),u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!o&&t.get()!==void 0){const p=cg(u.keyframes,a);if(p!==void 0){Y.update(()=>{u.onUpdate(p),u.onComplete()});return}}return a.isSync?new Us(u):new _p(u)};function mg({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Jc(e,t,{delay:n=0,transitionOverride:r,type:s}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:i,...a}=t;r&&(o=r);const c=[],d=s&&e.animationState&&e.animationState.getState()[s];for(const u in a){const h=e.getValue(u,e.latestValues[u]??null),p=a[u];if(p===void 0||d&&mg(d,u))continue;const m={delay:n,...Ws(o||{},u)},g=h.get();if(g!==void 0&&!h.isAnimating&&!Array.isArray(p)&&p===g&&!m.velocity)continue;let v=!1;if(window.MotionHandoffAnimation){const x=Zc(e);if(x){const b=window.MotionHandoffAnimation(x,u,Y);b!==null&&(m.startTime=b,v=!0)}}rs(e,u),h.start(ro(u,h,p,e.shouldReduceMotion&&vc.has(u)?{type:!1}:m,e,v));const y=h.animation;y&&c.push(y)}return i&&Promise.all(c).then(()=>{Y.update(()=>{i&&og(e,i)})}),c}function Qc(e,t,n,r=0,s=1){const o=Array.from(e).sort((d,u)=>d.sortNodePosition(u)).indexOf(t),i=e.size,a=(i-1)*r;return typeof n=="function"?n(o,i):s===1?o*r:a-o*r}function ss(e,t,n={}){const r=yt(e,t,n.type==="exit"?e.presenceContext?.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(s=n.transitionOverride);const o=r?()=>Promise.all(Jc(e,r,n)):()=>Promise.resolve(),i=e.variantChildren&&e.variantChildren.size?(c=0)=>{const{delayChildren:d=0,staggerChildren:u,staggerDirection:h}=s;return gg(e,t,c,d,u,h,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[c,d]=a==="beforeChildren"?[o,i]:[i,o];return c().then(()=>d())}else return Promise.all([o(),i(n.delay)])}function gg(e,t,n=0,r=0,s=0,o=1,i){const a=[];for(const c of e.variantChildren)c.notify("AnimationStart",t),a.push(ss(c,t,{...i,delay:n+(typeof r=="function"?0:r)+Qc(e.variantChildren,c,r,s,o)}).then(()=>c.notify("AnimationComplete",t)));return Promise.all(a)}function vg(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const s=t.map(o=>ss(e,o,n));r=Promise.all(s)}else if(typeof t=="string")r=ss(e,t,n);else{const s=typeof t=="function"?yt(e,t,n.custom):t;r=Promise.all(Jc(e,s,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}function el(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}const yg=qs.length;function tl(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?tl(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<yg;n++){const r=qs[n],s=e.props[r];(Wt(s)||s===!1)&&(t[r]=s)}return t}const xg=[...Xs].reverse(),bg=Xs.length;function wg(e){return t=>Promise.all(t.map(({animation:n,options:r})=>vg(e,n,r)))}function Cg(e){let t=wg(e),n=hi(),r=!0;const s=c=>(d,u)=>{const h=yt(e,u,c==="exit"?e.presenceContext?.custom:void 0);if(h){const{transition:p,transitionEnd:m,...g}=h;d={...d,...g,...m}}return d};function o(c){t=c(e)}function i(c){const{props:d}=e,u=tl(e.parent)||{},h=[],p=new Set;let m={},g=1/0;for(let y=0;y<bg;y++){const x=xg[y],b=n[x],w=d[x]!==void 0?d[x]:u[x],C=Wt(w),S=x===c?b.isActive:null;S===!1&&(g=y);let A=w===u[x]&&w!==d[x]&&C;if(A&&r&&e.manuallyAnimateOnMount&&(A=!1),b.protectedKeys={...m},!b.isActive&&S===null||!w&&!b.prevProp||Wn(w)||typeof w=="boolean")continue;const T=Sg(b.prevProp,w);let E=T||x===c&&b.isActive&&!A&&C||y>g&&C,P=!1;const D=Array.isArray(w)?w:[w];let O=D.reduce(s(x),{});S===!1&&(O={});const{prevResolvedValues:W={}}=b,H={...W,...O},z=L=>{E=!0,p.has(L)&&(P=!0,p.delete(L)),b.needsAnimating[L]=!0;const I=e.getValue(L);I&&(I.liveStyle=!1)};for(const L in H){const I=O[L],M=W[L];if(m.hasOwnProperty(L))continue;let k=!1;ns(I)&&ns(M)?k=!el(I,M):k=I!==M,k?I!=null?z(L):p.add(L):I!==void 0&&p.has(L)?z(L):b.protectedKeys[L]=!0}b.prevProp=w,b.prevResolvedValues=O,b.isActive&&(m={...m,...O}),r&&e.blockInitialAnimation&&(E=!1);const G=A&&T;E&&(!G||P)&&h.push(...D.map(L=>{const I={type:x};if(typeof L=="string"&&r&&!G&&e.manuallyAnimateOnMount&&e.parent){const{parent:M}=e,k=yt(M,L);if(M.enteringChildren&&k){const{delayChildren:$}=k.transition||{};I.delay=Qc(M.enteringChildren,e,$)}}return{animation:L,options:I}}))}if(p.size){const y={};if(typeof d.initial!="boolean"){const x=yt(e,Array.isArray(d.initial)?d.initial[0]:d.initial);x&&x.transition&&(y.transition=x.transition)}p.forEach(x=>{const b=e.getBaseTarget(x),w=e.getValue(x);w&&(w.liveStyle=!0),y[x]=b??null}),h.push({animation:y})}let v=!!h.length;return r&&(d.initial===!1||d.initial===d.animate)&&!e.manuallyAnimateOnMount&&(v=!1),r=!1,v?t(h):Promise.resolve()}function a(c,d){if(n[c].isActive===d)return Promise.resolve();e.variantChildren?.forEach(h=>h.animationState?.setActive(c,d)),n[c].isActive=d;const u=i(c);for(const h in n)n[h].protectedKeys={};return u}return{animateChanges:i,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=hi(),r=!0}}}function Sg(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!el(t,e):!1}function Qe(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function hi(){return{animate:Qe(!0),whileInView:Qe(),whileHover:Qe(),whileTap:Qe(),whileDrag:Qe(),whileFocus:Qe(),exit:Qe()}}class Je{constructor(t){this.isMounted=!1,this.node=t}update(){}}class Tg extends Je{constructor(t){super(t),t.animationState||(t.animationState=Cg(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Wn(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let Ag=0;class Pg extends Je{constructor(){super(...arguments),this.id=Ag++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const s=this.node.animationState.setActive("exit",!t);n&&!t&&s.then(()=>{n(this.id)})}mount(){const{register:t,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),t&&(this.unmount=t(this.id))}unmount(){}}const Eg={animation:{Feature:Tg},exit:{Feature:Pg}};function Kt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function nn(e){return{point:{x:e.pageX,y:e.pageY}}}const Rg=e=>t=>Gs(t)&&e(t,nn(t));function Vt(e,t,n,r){return Kt(e,t,Rg(n),r)}const nl=1e-4,Mg=1-nl,Dg=1+nl,rl=.01,Ng=0-rl,jg=0+rl;function le(e){return e.max-e.min}function Ig(e,t,n){return Math.abs(e-t)<=n}function pi(e,t,n,r=.5){e.origin=r,e.originPoint=q(t.min,t.max,e.origin),e.scale=le(n)/le(t),e.translate=q(n.min,n.max,e.origin)-e.originPoint,(e.scale>=Mg&&e.scale<=Dg||isNaN(e.scale))&&(e.scale=1),(e.translate>=Ng&&e.translate<=jg||isNaN(e.translate))&&(e.translate=0)}function Ft(e,t,n,r){pi(e.x,t.x,n.x,r?r.originX:void 0),pi(e.y,t.y,n.y,r?r.originY:void 0)}function mi(e,t,n){e.min=n.min+t.min,e.max=e.min+le(t)}function kg(e,t,n){mi(e.x,t.x,n.x),mi(e.y,t.y,n.y)}function gi(e,t,n){e.min=t.min-n.min,e.max=e.min+le(t)}function Bt(e,t,n){gi(e.x,t.x,n.x),gi(e.y,t.y,n.y)}function ye(e){return[e("x"),e("y")]}const sl=({current:e})=>e?e.ownerDocument.defaultView:null,vi=(e,t)=>Math.abs(e-t);function Og(e,t){const n=vi(e.x,t.x),r=vi(e.y,t.y);return Math.sqrt(n**2+r**2)}class ol{constructor(t,n,{transformPagePoint:r,contextWindow:s=window,dragSnapToOrigin:o=!1,distanceThreshold:i=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const p=wr(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,g=Og(p.offset,{x:0,y:0})>=this.distanceThreshold;if(!m&&!g)return;const{point:v}=p,{timestamp:y}=oe;this.history.push({...v,timestamp:y});const{onStart:x,onMove:b}=this.handlers;m||(x&&x(this.lastMoveEvent,p),this.startEvent=this.lastMoveEvent),b&&b(this.lastMoveEvent,p)},this.handlePointerMove=(p,m)=>{this.lastMoveEvent=p,this.lastMoveEventInfo=br(m,this.transformPagePoint),Y.update(this.updatePoint,!0)},this.handlePointerUp=(p,m)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:y}=this.handlers;if(this.dragSnapToOrigin&&y&&y(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=wr(p.type==="pointercancel"?this.lastMoveEventInfo:br(m,this.transformPagePoint),this.history);this.startEvent&&g&&g(p,x),v&&v(p,x)},!Gs(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.distanceThreshold=i,this.contextWindow=s||window;const a=nn(t),c=br(a,this.transformPagePoint),{point:d}=c,{timestamp:u}=oe;this.history=[{...d,timestamp:u}];const{onSessionStart:h}=n;h&&h(t,wr(c,this.history)),this.removeListeners=Qt(Vt(this.contextWindow,"pointermove",this.handlePointerMove),Vt(this.contextWindow,"pointerup",this.handlePointerUp),Vt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Ge(this.updatePoint)}}function br(e,t){return t?{point:t(e.point)}:e}function yi(e,t){return{x:e.x-t.x,y:e.y-t.y}}function wr({point:e},t){return{point:e,delta:yi(e,il(t)),offset:yi(e,Lg(t)),velocity:_g(t,.1)}}function Lg(e){return e[0]}function il(e){return e[e.length-1]}function _g(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const s=il(e);for(;n>=0&&(r=e[n],!(s.timestamp-r.timestamp>Me(t)));)n--;if(!r)return{x:0,y:0};const o=De(s.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const i={x:(s.x-r.x)/o,y:(s.y-r.y)/o};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}function Vg(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?q(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?q(n,e,r.max):Math.min(e,n)),e}function xi(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Fg(e,{top:t,left:n,bottom:r,right:s}){return{x:xi(e.x,n,s),y:xi(e.y,t,r)}}function bi(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function Bg(e,t){return{x:bi(e.x,t.x),y:bi(e.y,t.y)}}function $g(e,t){let n=.5;const r=le(e),s=le(t);return s>r?n=$t(t.min,t.max-r,e.min):r>s&&(n=$t(e.min,e.max-s,t.min)),Le(0,1,n)}function Ug(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const os=.35;function zg(e=os){return e===!1?e=0:e===!0&&(e=os),{x:wi(e,"left","right"),y:wi(e,"top","bottom")}}function wi(e,t,n){return{min:Ci(e,t),max:Ci(e,n)}}function Ci(e,t){return typeof e=="number"?e:e[t]||0}const Wg=new WeakMap;class Hg{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Q(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:n=!1,distanceThreshold:r}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const o=h=>{const{dragSnapToOrigin:p}=this.getProps();p?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(nn(h).point)},i=(h,p)=>{const{drag:m,dragPropagation:g,onDragStart:v}=this.getProps();if(m&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Qp(m),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=p,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ye(x=>{let b=this.getAxisMotionValue(x).get()||0;if(Ne.test(b)){const{projection:w}=this.visualElement;if(w&&w.layout){const C=w.layout.layoutBox[x];C&&(b=le(C)*(parseFloat(b)/100))}}this.originPoint[x]=b}),v&&Y.postRender(()=>v(h,p)),rs(this.visualElement,"transform");const{animationState:y}=this.visualElement;y&&y.setActive("whileDrag",!0)},a=(h,p)=>{this.latestPointerEvent=h,this.latestPanInfo=p;const{dragPropagation:m,dragDirectionLock:g,onDirectionLock:v,onDrag:y}=this.getProps();if(!m&&!this.openDragLock)return;const{offset:x}=p;if(g&&this.currentDirection===null){this.currentDirection=Kg(x),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",p.point,x),this.updateAxis("y",p.point,x),this.visualElement.render(),y&&y(h,p)},c=(h,p)=>{this.latestPointerEvent=h,this.latestPanInfo=p,this.stop(h,p),this.latestPointerEvent=null,this.latestPanInfo=null},d=()=>ye(h=>this.getAnimationState(h)==="paused"&&this.getAxisMotionValue(h).animation?.play()),{dragSnapToOrigin:u}=this.getProps();this.panSession=new ol(t,{onSessionStart:o,onStart:i,onMove:a,onSessionEnd:c,resumeAnimation:d},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:r,contextWindow:sl(this.visualElement)})}stop(t,n){const r=t||this.latestPointerEvent,s=n||this.latestPanInfo,o=this.isDragging;if(this.cancel(),!o||!s||!r)return;const{velocity:i}=s;this.startAnimation(i);const{onDragEnd:a}=this.getProps();a&&Y.postRender(()=>a(r,s))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:s}=this.getProps();if(!r||!hn(t,s,this.currentDirection))return;const o=this.getAxisMotionValue(t);let i=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(i=Vg(i,this.constraints[t],this.elastic[t])),o.set(i)}resolveConstraints(){const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&pt(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=Fg(r.layoutBox,t):this.constraints=!1,this.elastic=zg(n),s!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&ye(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Ug(r.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!pt(t))return!1;const r=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const o=Gm(r,s.root,this.visualElement.getTransformPagePoint());let i=Bg(s.layout.layoutBox,o);if(n){const a=n(Wm(i));this.hasMutatedConstraints=!!a,a&&(i=zc(a))}return i}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:s,dragTransition:o,dragSnapToOrigin:i,onDragTransitionEnd:a}=this.getProps(),c=this.constraints||{},d=ye(u=>{if(!hn(u,n,this.currentDirection))return;let h=c&&c[u]||{};i&&(h={min:0,max:0});const p=s?200:1e6,m=s?40:1e7,g={type:"inertia",velocity:r?t[u]:0,bounceStiffness:p,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,g)});return Promise.all(d).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return rs(this.visualElement,t),r.start(ro(t,r,0,n,this.visualElement,!1))}stopAnimation(){ye(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ye(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),s=r[n];return s||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){ye(n=>{const{drag:r}=this.getProps();if(!hn(n,r,this.currentDirection))return;const{projection:s}=this.visualElement,o=this.getAxisMotionValue(n);if(s&&s.layout){const{min:i,max:a}=s.layout.layoutBox[n];o.set(t[n]-q(i,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!pt(n)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};ye(i=>{const a=this.getAxisMotionValue(i);if(a&&this.constraints!==!1){const c=a.get();s[i]=$g({min:c,max:c},this.constraints[i])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),ye(i=>{if(!hn(i,t,null))return;const a=this.getAxisMotionValue(i),{min:c,max:d}=this.constraints[i];a.set(q(c,d,s[i]))})}addListeners(){if(!this.visualElement.current)return;Wg.set(this.visualElement,this);const t=this.visualElement.current,n=Vt(t,"pointerdown",c=>{const{drag:d,dragListener:u=!0}=this.getProps();d&&u&&this.start(c)}),r=()=>{const{dragConstraints:c}=this.getProps();pt(c)&&c.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,o=s.addEventListener("measure",r);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),Y.read(r);const i=Kt(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:c,hasLayoutChanged:d})=>{this.isDragging&&d&&(ye(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=c[u].translate,h.set(h.get()+c[u].translate))}),this.visualElement.render())});return()=>{i(),n(),o(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:o=!1,dragElastic:i=os,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:s,dragConstraints:o,dragElastic:i,dragMomentum:a}}}function hn(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Kg(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class Gg extends Je{constructor(t){super(t),this.removeGroupControls=xe,this.removeListeners=xe,this.controls=new Hg(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||xe}unmount(){this.removeGroupControls(),this.removeListeners()}}const Si=e=>(t,n)=>{e&&Y.postRender(()=>e(t,n))};class Yg extends Je{constructor(){super(...arguments),this.removePointerDownListener=xe}onPointerDown(t){this.session=new ol(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:sl(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:Si(t),onStart:Si(n),onMove:r,onEnd:(o,i)=>{delete this.session,s&&Y.postRender(()=>s(o,i))}}}mount(){this.removePointerDownListener=Vt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Tn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Ti(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const jt={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(F.test(e))e=parseFloat(e);else return e;const n=Ti(e,t.target.x),r=Ti(e,t.target.y);return`${n}% ${r}%`}},Xg={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,s=Ye.parse(e);if(s.length>5)return r;const o=Ye.createTransformer(e),i=typeof s[0]!="number"?1:0,a=n.x.scale*t.x,c=n.y.scale*t.y;s[0+i]/=a,s[1+i]/=c;const d=q(a,c,.5);return typeof s[2+i]=="number"&&(s[2+i]/=d),typeof s[3+i]=="number"&&(s[3+i]/=d),o(s)}};let Cr=!1;class qg extends f.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:s}=this.props,{projection:o}=t;ym(Zg),o&&(n.group&&n.group.add(o),r&&r.register&&s&&r.register(o),Cr&&o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Tn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:s,isPresent:o}=this.props,{projection:i}=r;return i&&(i.isPresent=o,Cr=!0,s||t.layoutDependency!==n||n===void 0||t.isPresent!==o?i.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?i.promote():i.relegate()||Y.postRender(()=>{const a=i.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Ks.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:s}=t;Cr=!0,s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function al(e){const[t,n]=Rc(),r=f.useContext(Ts);return l.jsx(qg,{...e,layoutGroup:r,switchLayoutGroup:f.useContext($c),isPresent:t,safeToRemove:n})}const Zg={borderRadius:{...jt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:jt,borderTopRightRadius:jt,borderBottomLeftRadius:jt,borderBottomRightRadius:jt,boxShadow:Xg};function Jg(e,t,n){const r=ce(e)?e:Ct(e);return r.start(ro("",r,t,n)),r.animation}const Qg=(e,t)=>e.depth-t.depth;class ev{constructor(){this.children=[],this.isDirty=!1}add(t){Es(this.children,t),this.isDirty=!0}remove(t){Rs(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Qg),this.isDirty=!1,this.children.forEach(t)}}function tv(e,t){const n=fe.now(),r=({timestamp:s})=>{const o=s-n;o>=t&&(Ge(r),e(o-t))};return Y.setup(r,!0),()=>Ge(r)}const cl=["TopLeft","TopRight","BottomLeft","BottomRight"],nv=cl.length,Ai=e=>typeof e=="string"?parseFloat(e):e,Pi=e=>typeof e=="number"||F.test(e);function rv(e,t,n,r,s,o){s?(e.opacity=q(0,n.opacity??1,sv(r)),e.opacityExit=q(t.opacity??1,0,ov(r))):o&&(e.opacity=q(t.opacity??1,n.opacity??1,r));for(let i=0;i<nv;i++){const a=`border${cl[i]}Radius`;let c=Ei(t,a),d=Ei(n,a);if(c===void 0&&d===void 0)continue;c||(c=0),d||(d=0),c===0||d===0||Pi(c)===Pi(d)?(e[a]=Math.max(q(Ai(c),Ai(d),r),0),(Ne.test(d)||Ne.test(c))&&(e[a]+="%")):e[a]=d}(t.rotate||n.rotate)&&(e.rotate=q(t.rotate||0,n.rotate||0,r))}function Ei(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const sv=ll(0,.5,Ya),ov=ll(.5,.95,xe);function ll(e,t,n){return r=>r<e?0:r>t?1:n($t(e,t,r))}function Ri(e,t){e.min=t.min,e.max=t.max}function ve(e,t){Ri(e.x,t.x),Ri(e.y,t.y)}function Mi(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Di(e,t,n,r,s){return e-=t,e=In(e,1/n,r),s!==void 0&&(e=In(e,1/s,r)),e}function iv(e,t=0,n=1,r=.5,s,o=e,i=e){if(Ne.test(t)&&(t=parseFloat(t),t=q(i.min,i.max,t/100)-i.min),typeof t!="number")return;let a=q(o.min,o.max,r);e===o&&(a-=t),e.min=Di(e.min,t,n,a,s),e.max=Di(e.max,t,n,a,s)}function Ni(e,t,[n,r,s],o,i){iv(e,t[n],t[r],t[s],t.scale,o,i)}const av=["x","scaleX","originX"],cv=["y","scaleY","originY"];function ji(e,t,n,r){Ni(e.x,t,av,n?n.x:void 0,r?r.x:void 0),Ni(e.y,t,cv,n?n.y:void 0,r?r.y:void 0)}function Ii(e){return e.translate===0&&e.scale===1}function ul(e){return Ii(e.x)&&Ii(e.y)}function ki(e,t){return e.min===t.min&&e.max===t.max}function lv(e,t){return ki(e.x,t.x)&&ki(e.y,t.y)}function Oi(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function dl(e,t){return Oi(e.x,t.x)&&Oi(e.y,t.y)}function Li(e){return le(e.x)/le(e.y)}function _i(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class uv{constructor(){this.members=[]}add(t){Es(this.members,t),t.scheduleRender()}remove(t){if(Rs(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(s=>t===s);if(n===0)return!1;let r;for(let s=n;s>=0;s--){const o=this.members[s];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function dv(e,t,n){let r="";const s=e.x.translate/t.x,o=e.y.translate/t.y,i=n?.z||0;if((s||o||i)&&(r=`translate3d(${s}px, ${o}px, ${i}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:d,rotate:u,rotateX:h,rotateY:p,skewX:m,skewY:g}=n;d&&(r=`perspective(${d}px) ${r}`),u&&(r+=`rotate(${u}deg) `),h&&(r+=`rotateX(${h}deg) `),p&&(r+=`rotateY(${p}deg) `),m&&(r+=`skewX(${m}deg) `),g&&(r+=`skewY(${g}deg) `)}const a=e.x.scale*t.x,c=e.y.scale*t.y;return(a!==1||c!==1)&&(r+=`scale(${a}, ${c})`),r||"none"}const Sr=["","X","Y","Z"],fv=1e3;let hv=0;function Tr(e,t,n,r){const{latestValues:s}=t;s[e]&&(n[e]=s[e],t.setStaticValue(e,0),r&&(r[e]=0))}function fl(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Zc(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:o}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",Y,!(s||o))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&fl(r)}function hl({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(i={},a=t?.()){this.id=hv++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(gv),this.nodes.forEach(bv),this.nodes.forEach(wv),this.nodes.forEach(vv)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=i,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let c=0;c<this.path.length;c++)this.path[c].shouldResetTransform=!0;this.root===this&&(this.nodes=new ev)}addEventListener(i,a){return this.eventHandlers.has(i)||this.eventHandlers.set(i,new Ns),this.eventHandlers.get(i).add(a)}notifyListeners(i,...a){const c=this.eventHandlers.get(i);c&&c.notify(...a)}hasListeners(i){return this.eventHandlers.has(i)}mount(i){if(this.instance)return;this.isSVG=Ec(i)&&!om(i),this.instance=i;const{layoutId:a,layout:c,visualElement:d}=this.options;if(d&&!d.current&&d.mount(i),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(c||a)&&(this.isLayoutDirty=!0),e){let u,h=0;const p=()=>this.root.updateBlockedByResize=!1;Y.read(()=>{h=window.innerWidth}),e(i,()=>{const m=window.innerWidth;m!==h&&(h=m,this.root.updateBlockedByResize=!0,u&&u(),u=tv(p,250),Tn.hasAnimatedSinceResize&&(Tn.hasAnimatedSinceResize=!1,this.nodes.forEach(Bi)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&d&&(a||c)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:p,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const g=this.options.transition||d.getDefaultTransition()||Pv,{onLayoutAnimationStart:v,onLayoutAnimationComplete:y}=d.getProps(),x=!this.targetLayout||!dl(this.targetLayout,m),b=!h&&p;if(this.options.layoutRoot||this.resumeFrom||b||h&&(x||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const w={...Ws(g,"layout"),onPlay:v,onComplete:y};(d.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w),this.setAnimationOrigin(u,b)}else h||Bi(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const i=this.getStack();i&&i.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ge(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Cv),this.animationId++)}getTransformTemplate(){const{visualElement:i}=this.options;return i&&i.getProps().transformTemplate}willUpdate(i=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&fl(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:c}=this.options;if(a===void 0&&!c)return;const d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),i&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Vi);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Fi);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(xv),this.nodes.forEach(pv),this.nodes.forEach(mv)):this.nodes.forEach(Fi),this.clearAllSnapshots();const a=fe.now();oe.delta=Le(0,1e3/60,a-oe.timestamp),oe.timestamp=a,oe.isProcessing=!0,fr.update.process(oe),fr.preRender.process(oe),fr.render.process(oe),oe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ks.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(yv),this.sharedNodes.forEach(Sv)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Y.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Y.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!le(this.snapshot.measuredBox.x)&&!le(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let c=0;c<this.path.length;c++)this.path[c].updateScroll();const i=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Q(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,i?i.layoutBox:void 0)}updateScroll(i="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===i&&(a=!1),a&&this.instance){const c=r(this.instance);this.scroll={animationId:this.root.animationId,phase:i,isRoot:c,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:c}}}resetTransform(){if(!s)return;const i=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!ul(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,u=d!==this.prevTransformTemplateValue;i&&this.instance&&(a||et(this.latestValues)||u)&&(s(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(i=!0){const a=this.measurePageBox();let c=this.removeElementScroll(a);return i&&(c=this.removeTransform(c)),Ev(c),{animationId:this.root.animationId,measuredBox:a,layoutBox:c,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:i}=this.options;if(!i)return Q();const a=i.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Rv))){const{scroll:d}=this.root;d&&(mt(a.x,d.offset.x),mt(a.y,d.offset.y))}return a}removeElementScroll(i){const a=Q();if(ve(a,i),this.scroll?.wasRoot)return a;for(let c=0;c<this.path.length;c++){const d=this.path[c],{scroll:u,options:h}=d;d!==this.root&&u&&h.layoutScroll&&(u.wasRoot&&ve(a,i),mt(a.x,u.offset.x),mt(a.y,u.offset.y))}return a}applyTransform(i,a=!1){const c=Q();ve(c,i);for(let d=0;d<this.path.length;d++){const u=this.path[d];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&gt(c,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),et(u.latestValues)&&gt(c,u.latestValues)}return et(this.latestValues)&&gt(c,this.latestValues),c}removeTransform(i){const a=Q();ve(a,i);for(let c=0;c<this.path.length;c++){const d=this.path[c];if(!d.instance||!et(d.latestValues))continue;Qr(d.latestValues)&&d.updateSnapshot();const u=Q(),h=d.measurePageBox();ve(u,h),ji(a,d.latestValues,d.snapshot?d.snapshot.layoutBox:void 0,u)}return et(this.latestValues)&&ji(a,this.latestValues),a}setTargetDelta(i){this.targetDelta=i,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(i){this.options={...this.options,...i,crossfade:i.crossfade!==void 0?i.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==oe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(i=!1){const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==a;if(!(i||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=oe.timestamp,!this.targetDelta&&!this.relativeTarget){const p=this.getClosestProjectingParent();p&&p.layout&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Q(),this.relativeTargetOrigin=Q(),Bt(this.relativeTargetOrigin,this.layout.layoutBox,p.layout.layoutBox),ve(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Q(),this.targetWithTransforms=Q()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),kg(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ve(this.target,this.layout.layoutBox),Hc(this.target,this.targetDelta)):ve(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const p=this.getClosestProjectingParent();p&&!!p.resumingFrom==!!this.resumingFrom&&!p.options.layoutScroll&&p.target&&this.animationProgress!==1?(this.relativeParent=p,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Q(),this.relativeTargetOrigin=Q(),Bt(this.relativeTargetOrigin,this.target,p.target),ve(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Qr(this.parent.latestValues)||Wc(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const i=this.getLead(),a=!!this.resumingFrom||this!==i;let c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===oe.timestamp&&(c=!1),c)return;const{layout:d,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||u))return;ve(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,p=this.treeScale.y;Km(this.layoutCorrected,this.treeScale,this.path,a),i.layout&&!i.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(i.target=i.layout.layoutBox,i.targetWithTransforms=Q());const{target:m}=i;if(!m){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Mi(this.prevProjectionDelta.x,this.projectionDelta.x),Mi(this.prevProjectionDelta.y,this.projectionDelta.y)),Ft(this.projectionDelta,this.layoutCorrected,m,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==p||!_i(this.projectionDelta.x,this.prevProjectionDelta.x)||!_i(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(i=!0){if(this.options.visualElement?.scheduleRender(),i){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=vt(),this.projectionDelta=vt(),this.projectionDeltaWithTransform=vt()}setAnimationOrigin(i,a=!1){const c=this.snapshot,d=c?c.latestValues:{},u={...this.latestValues},h=vt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const p=Q(),m=c?c.source:void 0,g=this.layout?this.layout.source:void 0,v=m!==g,y=this.getStack(),x=!y||y.members.length<=1,b=!!(v&&!x&&this.options.crossfade===!0&&!this.path.some(Av));this.animationProgress=0;let w;this.mixTargetDelta=C=>{const S=C/1e3;$i(h.x,i.x,S),$i(h.y,i.y,S),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Bt(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Tv(this.relativeTarget,this.relativeTargetOrigin,p,S),w&&lv(this.relativeTarget,w)&&(this.isProjectionDirty=!1),w||(w=Q()),ve(w,this.relativeTarget)),v&&(this.animationValues=u,rv(u,d,this.latestValues,S,b,x)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(i){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Ge(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Y.update(()=>{Tn.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Ct(0)),this.currentAnimation=Jg(this.motionValue,[0,1e3],{...i,velocity:0,isSync:!0,onUpdate:a=>{this.mixTargetDelta(a),i.onUpdate&&i.onUpdate(a)},onStop:()=>{},onComplete:()=>{i.onComplete&&i.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const i=this.getStack();i&&i.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(fv),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const i=this.getLead();let{targetWithTransforms:a,target:c,layout:d,latestValues:u}=i;if(!(!a||!c||!d)){if(this!==i&&this.layout&&d&&pl(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||Q();const h=le(this.layout.layoutBox.x);c.x.min=i.target.x.min,c.x.max=c.x.min+h;const p=le(this.layout.layoutBox.y);c.y.min=i.target.y.min,c.y.max=c.y.min+p}ve(a,c),gt(a,u),Ft(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(i,a){this.sharedNodes.has(i)||this.sharedNodes.set(i,new uv),this.sharedNodes.get(i).add(a);const d=a.options.initialPromotionConfig;a.promote({transition:d?d.transition:void 0,preserveFollowOpacity:d&&d.shouldPreserveFollowOpacity?d.shouldPreserveFollowOpacity(a):void 0})}isLead(){const i=this.getStack();return i?i.lead===this:!0}getLead(){const{layoutId:i}=this.options;return i?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:i}=this.options;return i?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:i}=this.options;if(i)return this.root.sharedNodes.get(i)}promote({needsReset:i,transition:a,preserveFollowOpacity:c}={}){const d=this.getStack();d&&d.promote(this,c),i&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const i=this.getStack();return i?i.relegate(this):!1}resetSkewAndRotation(){const{visualElement:i}=this.options;if(!i)return;let a=!1;const{latestValues:c}=i;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(a=!0),!a)return;const d={};c.z&&Tr("z",i,d,this.animationValues);for(let u=0;u<Sr.length;u++)Tr(`rotate${Sr[u]}`,i,d,this.animationValues),Tr(`skew${Sr[u]}`,i,d,this.animationValues);i.render();for(const u in d)i.setStaticValue(u,d[u]),this.animationValues&&(this.animationValues[u]=d[u]);i.scheduleRender()}applyProjectionStyles(i,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){i.visibility="hidden";return}const c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,i.visibility="",i.opacity="",i.pointerEvents=Sn(a?.pointerEvents)||"",i.transform=c?c(this.latestValues,""):"none";return}const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(i.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,i.pointerEvents=Sn(a?.pointerEvents)||""),this.hasProjected&&!et(this.latestValues)&&(i.transform=c?c({},""):"none",this.hasProjected=!1);return}i.visibility="";const u=d.animationValues||d.latestValues;this.applyTransformsToTarget();let h=dv(this.projectionDeltaWithTransform,this.treeScale,u);c&&(h=c(u,h)),i.transform=h;const{x:p,y:m}=this.projectionDelta;i.transformOrigin=`${p.origin*100}% ${m.origin*100}% 0`,d.animationValues?i.opacity=d===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:i.opacity=d===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const g in Ht){if(u[g]===void 0)continue;const{correct:v,applyTo:y,isCSSVariable:x}=Ht[g],b=h==="none"?u[g]:v(u[g],d);if(y){const w=y.length;for(let C=0;C<w;C++)i[y[C]]=b}else x?this.options.visualElement.renderState.vars[g]=b:i[g]=b}this.options.layoutId&&(i.pointerEvents=d===this?Sn(a?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(i=>i.currentAnimation?.stop()),this.root.nodes.forEach(Vi),this.root.sharedNodes.clear()}}}function pv(e){e.updateLayout()}function mv(e){const t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:r}=e.layout,{animationType:s}=e.options,o=t.source!==e.layout.source;s==="size"?ye(u=>{const h=o?t.measuredBox[u]:t.layoutBox[u],p=le(h);h.min=n[u].min,h.max=h.min+p}):pl(s,t.layoutBox,n)&&ye(u=>{const h=o?t.measuredBox[u]:t.layoutBox[u],p=le(n[u]);h.max=h.min+p,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[u].max=e.relativeTarget[u].min+p)});const i=vt();Ft(i,n,t.layoutBox);const a=vt();o?Ft(a,e.applyTransform(r,!0),t.measuredBox):Ft(a,n,t.layoutBox);const c=!ul(i);let d=!1;if(!e.resumeFrom){const u=e.getClosestProjectingParent();if(u&&!u.resumeFrom){const{snapshot:h,layout:p}=u;if(h&&p){const m=Q();Bt(m,t.layoutBox,h.layoutBox);const g=Q();Bt(g,n,p.layoutBox),dl(m,g)||(d=!0),u.options.layoutRoot&&(e.relativeTarget=g,e.relativeTargetOrigin=m,e.relativeParent=u)}}}e.notifyListeners("didUpdate",{layout:n,snapshot:t,delta:a,layoutDelta:i,hasLayoutChanged:c,hasRelativeLayoutChanged:d})}else if(e.isLead()){const{onExitComplete:n}=e.options;n&&n()}e.options.transition=void 0}function gv(e){e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function vv(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function yv(e){e.clearSnapshot()}function Vi(e){e.clearMeasurements()}function Fi(e){e.isLayoutDirty=!1}function xv(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Bi(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function bv(e){e.resolveTargetDelta()}function wv(e){e.calcProjection()}function Cv(e){e.resetSkewAndRotation()}function Sv(e){e.removeLeadSnapshot()}function $i(e,t,n){e.translate=q(t.translate,0,n),e.scale=q(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Ui(e,t,n,r){e.min=q(t.min,n.min,r),e.max=q(t.max,n.max,r)}function Tv(e,t,n,r){Ui(e.x,t.x,n.x,r),Ui(e.y,t.y,n.y,r)}function Av(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const Pv={duration:.45,ease:[.4,0,.1,1]},zi=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Wi=zi("applewebkit/")&&!zi("chrome/")?Math.round:xe;function Hi(e){e.min=Wi(e.min),e.max=Wi(e.max)}function Ev(e){Hi(e.x),Hi(e.y)}function pl(e,t,n){return e==="position"||e==="preserve-aspect"&&!Ig(Li(t),Li(n),.2)}function Rv(e){return e!==e.root&&e.scroll?.wasRoot}const Mv=hl({attachResizeListener:(e,t)=>Kt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ar={current:void 0},ml=hl({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Ar.current){const e=new Mv({});e.mount(window),e.setOptions({layoutScroll:!0}),Ar.current=e}return Ar.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Dv={pan:{Feature:Yg},drag:{Feature:Gg,ProjectionNode:ml,MeasureLayout:al}};function Ki(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const s="onHover"+n,o=r[s];o&&Y.postRender(()=>o(t,nn(t)))}class Nv extends Je{mount(){const{current:t}=this.node;t&&(this.unmount=em(t,(n,r)=>(Ki(this.node,r,"Start"),s=>Ki(this.node,s,"End"))))}unmount(){}}class jv extends Je{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Qt(Kt(this.node.current,"focus",()=>this.onFocus()),Kt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Gi(e,t,n){const{props:r}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const s="onTap"+(n==="End"?"":n),o=r[s];o&&Y.postRender(()=>o(t,nn(t)))}class Iv extends Je{mount(){const{current:t}=this.node;t&&(this.unmount=sm(t,(n,r)=>(Gi(this.node,r,"Start"),(s,{success:o})=>Gi(this.node,s,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const is=new WeakMap,Pr=new WeakMap,kv=e=>{const t=is.get(e.target);t&&t(e)},Ov=e=>{e.forEach(kv)};function Lv({root:e,...t}){const n=e||document;Pr.has(n)||Pr.set(n,{});const r=Pr.get(n),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(Ov,{root:e,...t})),r[s]}function _v(e,t,n){const r=Lv(t);return is.set(e,n),r.observe(e),()=>{is.delete(e),r.unobserve(e)}}const Vv={some:0,all:1};class Fv extends Je{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:s="some",once:o}=t,i={root:n?n.current:void 0,rootMargin:r,threshold:typeof s=="number"?s:Vv[s]},a=c=>{const{isIntersecting:d}=c;if(this.isInView===d||(this.isInView=d,o&&!d&&this.hasEnteredView))return;d&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",d);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),p=d?u:h;p&&p(c)};return _v(this.node.current,i,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Bv(t,n))&&this.startObserver()}unmount(){}}function Bv({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const $v={inView:{Feature:Fv},tap:{Feature:Iv},focus:{Feature:jv},hover:{Feature:Nv}},Uv={layout:{ProjectionNode:ml,MeasureLayout:al}},zv={...Eg,...$v,...Dv,...Uv},Kn=zm(zv,ng),Wv=(...e)=>e.filter(Boolean).join(" "),kn=(...e)=>gf(Wv(e)),Hv=ws("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 overflow-hidden [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-black text-white hover:bg-gray-800",destructive:"border border-black text-black hover:bg-gray-100",outline:"border border-gray-400 bg-white hover:bg-gray-100 hover:text-black",secondary:"bg-gray-200 text-black hover:bg-gray-300",ghost:"text-black hover:bg-gray-100 hover:text-black",link:"text-black underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default"}}),rn=Ae.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...s},o)=>l.jsx("button",{className:kn(Hv({variant:t,size:n,className:e})),ref:o,...s}));rn.displayName="Button";const gl=Ae.forwardRef(({className:e,...t},n)=>l.jsx("textarea",{className:kn("flex min-h-[80px] w-full rounded-md border border-gray-400 bg-white px-3 py-2 text-base ring-offset-white placeholder:text-black focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-600 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm text-black",e),ref:n,...t}));gl.displayName="Textarea";const Kv=({size:e=16})=>l.jsx("svg",{height:e,viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:l.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3 3H13V13H3V3Z",fill:"currentColor"})}),Gv=({size:e=16})=>l.jsx("svg",{height:e,strokeLinejoin:"round",viewBox:"0 0 16 16",width:e,style:{color:"currentcolor"},children:l.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.70711 1.39644C8.31659 1.00592 7.68342 1.00592 7.2929 1.39644L2.21968 6.46966L1.68935 6.99999L2.75001 8.06065L3.28034 7.53032L7.25001 3.56065V14.25V15H8.75001V14.25V3.56065L12.7197 7.53032L13.25 8.06065L14.3107 6.99999L13.7803 6.46966L8.70711 1.39644Z",fill:"currentColor"})});function Yv({onSelectAction:e}){const t=[{title:"受限空间内气体环境满足作业要求下",label:"气体检测分析合格标准是多少",action:"受限空间内气体环境满足作业要求下，气体检测分析合格标准是多少"},{title:"受限空间作业前,应根据受限空间",label:"盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准",action:"受限空间作业前,应根据受限空间盛装（过）的物料的特性，对受限空间进行清洗或置换，需要达到什么标准"},{title:"固定式配电箱及开关箱的底面",label:"离地面垂直高度的高度是多少",action:"固定式配电箱及开关箱的底面离地面垂直高度的高度是多少"},{title:"八大保命原则",label:"是什么",action:"中化集团八大保命原则"}];return l.jsx("div",{"data-testid":"suggested-actions",className:"grid pb-2 grid-cols-1 sm:grid-cols-2 gap-2 w-full",children:l.jsx(Mc,{children:t.map((n,r)=>l.jsx(Kn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{delay:.05*r},className:"block",children:l.jsxs(rn,{variant:"ghost",onClick:()=>e(n.action),className:`text-left border rounded-xl px-4 py-3.5 text-sm flex-1 gap-1 sm:flex-col w-full h-auto justify-start items-start\r
                       border-gray-300 bg-white hover:bg-gray-100 text-black hover:text-gray-900`,children:[l.jsx("span",{className:"font-medium",children:n.title}),l.jsx("span",{className:"text-gray-500",children:n.label})]})},`suggested-action-${r}`))})})}const Xv=f.memo(Yv,(e,t)=>!(e.chatId!==t.chatId||e.selectedVisibilityType!==t.selectedVisibilityType)),Yi=({attachment:e,isUploading:t=!1})=>{const{name:n,url:r,contentType:s}=e;return l.jsxs("div",{"data-testid":"input-attachment-preview",className:"flex flex-col gap-1",children:[l.jsxs("div",{className:"w-20 h-16 aspect-video bg-gray-200 rounded-md relative flex flex-col items-center justify-center overflow-hidden border border-gray-300",children:[s?.startsWith("image/")&&r?l.jsx("img",{src:r,alt:n??"An image attachment",className:"rounded-md size-full object-cover grayscale"},r):l.jsxs("div",{className:"flex items-center justify-center text-xs text-gray-600 text-center p-1",children:["File: ",n?.split(".").pop()?.toUpperCase()||"Unknown"]}),t&&l.jsx("div",{"data-testid":"input-attachment-loader",className:"animate-spin absolute text-gray-500",children:l.jsx(Fr,{className:"size-5"})})]}),l.jsx("div",{className:"text-xs text-gray-600 max-w-20 truncate",children:n})]})};function qv({onStop:e}){return l.jsx(rn,{"data-testid":"stop-button",className:"rounded-full p-1.5 h-fit border border-black text-white",onClick:t=>{t.preventDefault(),e()},"aria-label":"Stop generating",children:l.jsx(Kv,{size:14})})}const Zv=f.memo(qv,(e,t)=>e.onStop===t.onStop);function Jv({submitForm:e,input:t,uploadQueue:n,attachments:r,canSend:s,isGenerating:o}){const i=n.length>0||!s||o||t.trim().length===0&&r.length===0;return l.jsx(rn,{"data-testid":"send-button",className:"rounded-full p-1.5 h-fit",onClick:a=>{a.preventDefault(),i||e()},disabled:i,"aria-label":"Send message",children:l.jsx(Gv,{size:14})})}const Qv=f.memo(Jv,(e,t)=>!(e.input!==t.input||e.uploadQueue.length!==t.uploadQueue.length||e.attachments.length!==t.attachments.length||e.attachments.length>0&&!dh(e.attachments,t.attachments)||e.canSend!==t.canSend||e.isGenerating!==t.isGenerating));function ey({chatId:e,messages:t,attachments:n,setAttachments:r,onSendMessage:s,onStopGenerating:o,isGenerating:i,canSend:a,className:c,selectedVisibilityType:d}){const u=f.useRef(null),h=f.useRef(null),[p,m]=f.useState(""),[g,v]=f.useState([]),y=()=>{const P=u.current;P&&(P.style.height="auto",P.style.height=`${P.scrollHeight+2}px`)},x=f.useCallback(()=>{const P=u.current;P&&(P.style.height="auto",P.rows=1,y())},[]);f.useEffect(()=>{u.current&&y()},[p]);const b=P=>{m(P.target.value)},w=async P=>(console.log(`MOCK: Simulating upload for file: ${P.name}`),new Promise(D=>{setTimeout(()=>{try{const W={url:URL.createObjectURL(P),name:P.name,contentType:P.type||"application/octet-stream",size:P.size};console.log(`MOCK: Upload successful for ${P.name}`),D(W)}catch(O){console.error("MOCK: Failed to create object URL for preview:",O),D(void 0)}finally{v(O=>O.filter(W=>W!==P.name))}},700)})),C=f.useCallback(async P=>{const D=Array.from(P.target.files||[]);if(D.length===0)return;v(L=>[...L,...D.map(I=>I.name)]),h.current&&(h.current.value="");const O=25*1024*1024,W=D.filter(L=>L.size<=O),H=D.filter(L=>L.size>O);H.length>0&&(console.warn(`Skipped ${H.length} files larger than ${O/1024/1024}MB.`),v(L=>L.filter(I=>!H.some(M=>M.name===I))));const z=W.map(L=>w(L)),U=(await Promise.all(z)).filter(L=>L!==void 0);r(L=>[...L,...U])},[r,w]),S=f.useCallback(P=>{P.url.startsWith("blob:")&&URL.revokeObjectURL(P.url),r(D=>D.filter(O=>O.url!==P.url||O.name!==P.name)),u.current?.focus()},[r,u]),A=f.useCallback(()=>{if(p.trim().length===0&&n.length===0){console.warn("Please enter a message or add an attachment.");return}s({input:p,attachments:n}),m(""),r([]),n.forEach(P=>{P.url.startsWith("blob:")&&URL.revokeObjectURL(P.url)}),x(),u.current?.focus()},[p,n,s,r,u,x]),T=t.length===0&&n.length===0&&g.length===0,E=i||g.length>0;return l.jsxs("div",{className:kn("relative w-full flex flex-col gap-4",c),children:[l.jsx(Mc,{children:T&&l.jsx(Kn.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.2},children:l.jsx(Xv,{onSelectAction:P=>{m(P),requestAnimationFrame(()=>{y(),u.current?.focus()})},chatId:e,selectedVisibilityType:d})},"suggested-actions-container")}),l.jsx("input",{type:"file",className:"fixed -top-4 -left-4 size-0.5 opacity-0 pointer-events-none",ref:h,multiple:!0,onChange:C,tabIndex:-1,disabled:E,accept:"image/*,video/*,audio/*,.pdf"}),(n.length>0||g.length>0)&&l.jsxs("div",{"data-testid":"attachments-preview",className:"flex pt-[10px] flex-row gap-3 overflow-x-auto items-end pb-2 pl-1",children:[n.map(P=>l.jsxs("div",{className:"relative group",children:[l.jsx(Yi,{attachment:P,isUploading:!1}),l.jsx(rn,{variant:"destructive",size:"icon",className:"absolute top-[-8px] right-[-8px] h-5 w-5 rounded-full p-0 flex items-center justify-center z-20 opacity-0 group-hover:opacity-100 transition-opacity",onClick:()=>S(P),"aria-label":`Remove ${P.name}`,children:l.jsx(Ss,{className:"size-3"})})]},P.url||P.name)),g.map((P,D)=>l.jsx(Yi,{attachment:{url:"",name:P,contentType:"",size:0},isUploading:!0},`upload-${P}-${D}`))]}),l.jsx(gl,{"data-testid":"multimodal-input",ref:u,placeholder:"你想问什么",value:p,onChange:b,className:kn("min-h-[24px] max-h-[calc(75dvh)] overflow-y-auto resize-none rounded-2xl !text-base pb-10","bg-gray-100 border border-gray-300",c),style:{color:"black"},rows:1,autoFocus:!0,disabled:!a||i||g.length>0,onKeyDown:P=>{P.key==="Enter"&&!P.shiftKey&&!P.nativeEvent.isComposing&&(P.preventDefault(),a&&!i&&g.length===0&&(p.trim().length>0||n.length>0)&&A())}}),l.jsx("div",{className:"absolute bottom-0 right-0 p-2 w-fit flex flex-row justify-end z-10",children:i?l.jsx(Zv,{onStop:o}):l.jsx(Qv,{submitForm:A,input:p,uploadQueue:g,attachments:n,canSend:a,isGenerating:i})})]})}function ty({onSend:e,showSuggestedActions:t=!0}={}){const[n,r]=f.useState([]),[s,o]=f.useState(!1),[i]=f.useState("demo-input-only"),[a,c]=f.useState(t),d=f.useCallback(({input:m,attachments:g})=>{console.log("--- 发送消息 ---"),console.log("输入:",m),console.log("附件:",g),console.log("---------------------------------"),e&&m.trim()&&e(m.trim()),o(!0),setTimeout(()=>{o(!1),r([])},500)},[e]),u=f.useCallback(()=>{console.log("停止按钮被点击（模拟）。"),o(!1)},[]);return l.jsx("div",{className:"w-full max-w-3xl mx-auto p-4",children:l.jsxs("div",{className:"flex flex-col gap-4",children:[t&&l.jsx("div",{className:"flex justify-center",children:l.jsx("button",{onClick:()=>c(!a),className:"p-2 rounded-full bg-white border border-gray-300 hover:bg-gray-50 transition-colors shadow-sm",title:a?"收起快捷输入":"展开快捷输入",children:a?l.jsx(If,{className:"w-4 h-4 text-gray-600"}):l.jsx(Oa,{className:"w-4 h-4 text-gray-600"})})}),l.jsx("div",{children:l.jsx(ey,{chatId:i,messages:t&&a?[]:[{id:"dummy",content:"",role:"user"}],attachments:n,setAttachments:r,onSendMessage:d,onStopGenerating:u,isGenerating:s,canSend:!0,selectedVisibilityType:"private"})})]})})}const vl=f.forwardRef(({className:e,...t},n)=>l.jsx("textarea",{className:j("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));vl.displayName="Textarea";function ny({minHeight:e,maxHeight:t}){const n=f.useRef(null),r=f.useCallback(s=>{const o=n.current;if(!o)return;if(s){o.style.height=`${e}px`;return}o.style.height=`${e}px`;const i=Math.max(e,Math.min(o.scrollHeight,t));o.style.height=`${i}px`},[e,t]);return f.useEffect(()=>{const s=n.current;s&&(s.style.height=`${e}px`)},[e]),f.useEffect(()=>{const s=()=>r();return window.addEventListener("resize",s),()=>window.removeEventListener("resize",s)},[r]),{textareaRef:n,adjustHeight:r}}function ry({onSend:e,onFileSelect:t,placeholder:n="What can I do for you?",disabled:r=!1}){const[s,o]=f.useState(""),{textareaRef:i,adjustHeight:a}=ny({minHeight:72,maxHeight:300}),c=h=>{h.key==="Enter"&&!h.shiftKey&&s.trim()&&!r&&(h.preventDefault(),d())},d=()=>{!s.trim()||r||(e?.(s.trim()),o(""),a(!0))},u=h=>{const p=h.target.files?.[0];p&&t&&t(p),h.target.value=""};return l.jsx("div",{className:"w-full py-4",children:l.jsx("div",{className:"bg-black/5 dark:bg-white/5 rounded-2xl p-1.5",children:l.jsx("div",{className:"relative",children:l.jsxs("div",{className:"relative flex flex-col",children:[l.jsx("div",{className:"overflow-y-auto",style:{maxHeight:"400px"},children:l.jsx(vl,{id:"ai-input-15",value:s,placeholder:n,disabled:r,className:j("w-full rounded-xl rounded-b-none px-4 py-3 bg-black/5 dark:bg-white/5 border-none dark:text-white placeholder:text-black/70 dark:placeholder:text-white/70 resize-none focus-visible:ring-0 focus-visible:ring-offset-0","min-h-[72px]"),ref:i,onKeyDown:c,onChange:h=>{o(h.target.value),a()}})}),l.jsx("div",{className:"h-14 bg-black/5 dark:bg-white/5 rounded-b-xl flex items-center",children:l.jsxs("div",{className:"absolute left-3 right-3 bottom-3 flex items-center justify-between w-[calc(100%-24px)]",children:[l.jsx("div",{className:"flex items-center gap-2",children:l.jsxs("label",{className:j("rounded-lg p-2 bg-black/5 dark:bg-white/5 cursor-pointer","hover:bg-black/10 dark:hover:bg-white/10 focus-visible:ring-1 focus-visible:ring-offset-0 focus-visible:ring-blue-500","text-black/40 dark:text-white/40 hover:text-black dark:hover:text-white",r&&"opacity-50 cursor-not-allowed"),"aria-label":"Attach file",children:[l.jsx("input",{type:"file",className:"hidden",onChange:u,disabled:r,accept:".txt,.pdf,.docx,.doc"}),l.jsx(th,{className:"w-4 h-4 transition-colors"})]})}),l.jsx("button",{type:"button",className:j("rounded-lg p-2 bg-black/5 dark:bg-white/5","hover:bg-black/10 dark:hover:bg-white/10 focus-visible:ring-1 focus-visible:ring-offset-0 focus-visible:ring-blue-500"),"aria-label":"Send message",disabled:!s.trim()||r,onClick:d,children:l.jsx(Cf,{className:j("w-4 h-4 dark:text-white transition-opacity duration-200",s.trim()&&!r?"opacity-100":"opacity-30")})})]})})]})})})})}function sy({text:e,selected:t,setSelected:n,discount:r=!1}){return l.jsxs("button",{onClick:()=>n(e),className:j("relative w-fit px-4 py-2 text-sm font-semibold capitalize","text-foreground transition-colors",r&&"flex items-center justify-center gap-2.5"),children:[l.jsx("span",{className:"relative z-10",children:e}),t&&l.jsx(Kn.span,{layoutId:"tab",transition:{type:"spring",duration:.4},className:"absolute inset-0 z-0 rounded-full bg-background shadow-sm"})]})}async function oy(e,t){const n={id:t.id,userId:e,title:t.title,mode:t.mode||"无忧问答"};return(await ue.insert(X).values(n).returning())[0]}async function iy(e){return await ue.select().from(X).where(pe(X.userId,e)).orderBy(Af(X.updatedAt))}async function ay(){try{await ue.update(X).set({mode:"无忧问答"}).where(ur`mode IS NULL OR mode = '' OR mode = '知识库查询'`),await ue.update(X).set({mode:"无忧分析师"}).where(ur`mode = '方案改进'`),await ue.update(X).set({mode:"无忧计算师"}).where(ur`mode = '数据计算'`),console.log("已修复对话的 mode 字段")}catch(e){console.error("修复对话 mode 字段失败:",e)}}async function cy(e,t){return(await ue.select().from(X).where(Cs(pe(X.id,e),pe(X.userId,t))).limit(1))[0]||null}async function ly(e,t,n){return(await ue.update(X).set({title:n,updatedAt:new Date().toISOString()}).where(Cs(pe(X.id,e),pe(X.userId,t))).returning()).length>0}async function uy(e,t){return await ue.delete(He).where(pe(He.conversationId,e)),(await ue.delete(X).where(Cs(pe(X.id,e),pe(X.userId,t))).returning()).length>0}async function dy(e){const t={id:e.id,conversationId:e.conversationId,content:e.content,role:e.role,docReferences:e.docReferences?JSON.stringify(e.docReferences):null};return await ue.update(X).set({updatedAt:new Date().toISOString()}).where(pe(X.id,e.conversationId)),(await ue.insert(He).values(t).returning())[0]}async function fy(e){return(await ue.select().from(He).where(pe(He.conversationId,e)).orderBy(He.timestamp)).map(n=>({...n,docReferences:n.docReferences?JSON.parse(n.docReferences):void 0}))}async function Xi(e,t){const n=await cy(e,t);if(!n)return null;const r=await fy(e);return{conversation:n,messages:r}}async function hy(e){const t=await ue.select({id:X.id}).from(X).where(pe(X.userId,e));for(const n of t)await ue.delete(He).where(pe(He.conversationId,n.id));await ue.delete(X).where(pe(X.userId,e))}const as=Sf()(Tf((e,t)=>({conversations:[],currentConversationId:null,isLoading:!1,error:null,createConversation:async(n="新对话",r="无忧问答")=>{const o=Pe.getState().user;if(!o)throw new Error("用户未登录");e({isLoading:!0,error:null});try{const i=`conv_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await oy(o.id,{id:i,title:n,mode:r});const a={id:i,title:n,mode:r,messages:[],createdAt:new Date,updatedAt:new Date};return e(c=>({conversations:[a,...c.conversations],currentConversationId:i,isLoading:!1})),i}catch(i){const a=i instanceof Error?i.message:"创建对话失败";throw e({error:a,isLoading:!1}),i}},deleteConversation:async n=>{const s=Pe.getState().user;if(!s)throw new Error("用户未登录");e({isLoading:!0,error:null});try{await uy(n,s.id),e(o=>{const i=o.conversations.filter(c=>c.id!==n),a=o.currentConversationId===n?i.length>0?i[0].id:null:o.currentConversationId;return{conversations:i,currentConversationId:a,isLoading:!1}})}catch(o){const i=o instanceof Error?o.message:"删除对话失败";throw e({error:i,isLoading:!1}),o}},updateConversationTitle:async(n,r)=>{const o=Pe.getState().user;if(!o)throw new Error("用户未登录");e({isLoading:!0,error:null});try{await ly(n,o.id,r),e(i=>({conversations:i.conversations.map(a=>a.id===n?{...a,title:r,updatedAt:new Date}:a),isLoading:!1}))}catch(i){const a=i instanceof Error?i.message:"更新标题失败";throw e({error:a,isLoading:!1}),i}},setCurrentConversation:n=>{e({currentConversationId:n})},addMessage:async(n,r,s,o)=>{e({isLoading:!0,error:null});try{const i=`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;await dy({id:i,conversationId:n,content:r,role:s,docReferences:o});const a={id:i,content:r,role:s,timestamp:new Date,docReferences:o};e(c=>({conversations:c.conversations.map(d=>d.id===n?{...d,messages:[...d.messages,a],updatedAt:new Date}:d),isLoading:!1}))}catch(i){const a=i instanceof Error?i.message:"添加消息失败";throw e({error:a,isLoading:!1}),i}},getCurrentConversation:()=>{const n=t();return n.conversations.find(r=>r.id===n.currentConversationId)||null},getConversationsByMode:n=>t().conversations.filter(s=>s.mode===n),loadUserConversations:async()=>{const r=Pe.getState().user;if(!r){e({conversations:[],currentConversationId:null});return}e({isLoading:!0,error:null});try{await ay();const s=await iy(r.id),o=await Promise.all(s.map(async i=>{const a=await Xi(i.id,r.id);return{id:i.id,title:i.title,mode:i.mode||"无忧问答",messages:a?.messages.map(c=>({id:c.id,content:c.content,role:c.role,timestamp:new Date(c.timestamp),docReferences:c.docReferences}))||[],createdAt:new Date(i.createdAt),updatedAt:new Date(i.updatedAt)}}));e({conversations:o,isLoading:!1,currentConversationId:t().currentConversationId&&o.find(i=>i.id===t().currentConversationId)?t().currentConversationId:null})}catch(s){const o=s instanceof Error?s.message:"加载对话失败";e({error:o,isLoading:!1})}},syncConversationWithDB:async n=>{const s=Pe.getState().user;if(s)try{const o=await Xi(n,s.id);if(!o)return;const i={id:o.conversation.id,title:o.conversation.title,mode:o.conversation.mode||"无忧问答",messages:o.messages.map(a=>({id:a.id,content:a.content,role:a.role,timestamp:new Date(a.timestamp),docReferences:a.docReferences})),createdAt:new Date(o.conversation.createdAt),updatedAt:new Date(o.conversation.updatedAt)};e(a=>({conversations:a.conversations.map(c=>c.id===n?i:c)}))}catch(o){console.error("同步对话失败:",o)}},clearAllConversations:async()=>{const r=Pe.getState().user;if(!r){e({conversations:[],currentConversationId:null});return}e({isLoading:!0,error:null});try{await hy(r.id),e({conversations:[],currentConversationId:null,isLoading:!1})}catch(s){const o=s instanceof Error?s.message:"清空对话失败";throw e({error:o,isLoading:!1}),s}},setLoading:n=>{e({isLoading:n})},setError:n=>{e({error:n})}}),{name:"conversation-store",partialize:e=>({currentConversationId:e.currentConversationId})})),Er=768;function py(){const[e,t]=f.useState(void 0);return f.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Er-1}px)`),r=()=>{t(window.innerWidth<Er)};return n.addEventListener("change",r),t(window.innerWidth<Er),()=>n.removeEventListener("change",r)},[]),!!e}const so=f.forwardRef(({className:e,type:t,...n},r)=>l.jsx("input",{type:t,className:j("flex h-9 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm text-foreground shadow-sm shadow-black/5 transition-shadow placeholder:text-muted-foreground/70 focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20 disabled:cursor-not-allowed disabled:opacity-50",t==="search"&&"[&::-webkit-search-cancel-button]:appearance-none [&::-webkit-search-decoration]:appearance-none [&::-webkit-search-results-button]:appearance-none [&::-webkit-search-results-decoration]:appearance-none",t==="file"&&"p-0 pr-3 italic text-muted-foreground/70 file:me-3 file:h-full file:border-0 file:border-r file:border-solid file:border-input file:bg-transparent file:px-3 file:text-sm file:font-medium file:not-italic file:text-foreground",e),ref:r,...n}));so.displayName="Input";function B(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e?.(s),n===!1||!s.defaultPrevented)return t?.(s)}}var my=Aa[" useId ".trim().toString()]||(()=>{}),gy=0;function ot(e){const[t,n]=f.useState(my());return Ke(()=>{n(r=>r??String(gy++))},[e]),e||(t?`radix-${t}`:"")}var vy=Aa[" useInsertionEffect ".trim().toString()]||Ke;function Gn({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[s,o,i]=yy({defaultProp:t,onChange:n}),a=e!==void 0,c=a?e:s;{const u=f.useRef(e!==void 0);f.useEffect(()=>{const h=u.current;h!==a&&console.warn(`${r} is changing from ${h?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),u.current=a},[a,r])}const d=f.useCallback(u=>{if(a){const h=xy(u)?u(e):u;h!==e&&i.current?.(h)}else o(u)},[a,e,o,i]);return[c,d]}function yy({defaultProp:e,onChange:t}){const[n,r]=f.useState(e),s=f.useRef(n),o=f.useRef(t);return vy(()=>{o.current=t},[t]),f.useEffect(()=>{s.current!==n&&(o.current?.(n),s.current=n)},[n,s]),[n,r,o]}function xy(e){return typeof e=="function"}function by(e,t=globalThis?.document){const n=Oe(e);f.useEffect(()=>{const r=s=>{s.key==="Escape"&&n(s)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var wy="DismissableLayer",cs="dismissableLayer.update",Cy="dismissableLayer.pointerDownOutside",Sy="dismissableLayer.focusOutside",qi,yl=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Yn=f.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:o,onInteractOutside:i,onDismiss:a,...c}=e,d=f.useContext(yl),[u,h]=f.useState(null),p=u?.ownerDocument??globalThis?.document,[,m]=f.useState({}),g=ee(t,T=>h(T)),v=Array.from(d.layers),[y]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),x=v.indexOf(y),b=u?v.indexOf(u):-1,w=d.layersWithOutsidePointerEventsDisabled.size>0,C=b>=x,S=Py(T=>{const E=T.target,P=[...d.branches].some(D=>D.contains(E));!C||P||(s?.(T),i?.(T),T.defaultPrevented||a?.())},p),A=Ey(T=>{const E=T.target;[...d.branches].some(D=>D.contains(E))||(o?.(T),i?.(T),T.defaultPrevented||a?.())},p);return by(T=>{b===d.layers.size-1&&(r?.(T),!T.defaultPrevented&&a&&(T.preventDefault(),a()))},p),f.useEffect(()=>{if(u)return n&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(qi=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(u)),d.layers.add(u),Zi(),()=>{n&&d.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=qi)}},[u,p,n,d]),f.useEffect(()=>()=>{u&&(d.layers.delete(u),d.layersWithOutsidePointerEventsDisabled.delete(u),Zi())},[u,d]),f.useEffect(()=>{const T=()=>m({});return document.addEventListener(cs,T),()=>document.removeEventListener(cs,T)},[]),l.jsx(J.div,{...c,ref:g,style:{pointerEvents:w?C?"auto":"none":void 0,...e.style},onFocusCapture:B(e.onFocusCapture,A.onFocusCapture),onBlurCapture:B(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:B(e.onPointerDownCapture,S.onPointerDownCapture)})});Yn.displayName=wy;var Ty="DismissableLayerBranch",Ay=f.forwardRef((e,t)=>{const n=f.useContext(yl),r=f.useRef(null),s=ee(t,r);return f.useEffect(()=>{const o=r.current;if(o)return n.branches.add(o),()=>{n.branches.delete(o)}},[n.branches]),l.jsx(J.div,{...e,ref:s})});Ay.displayName=Ty;function Py(e,t=globalThis?.document){const n=Oe(e),r=f.useRef(!1),s=f.useRef(()=>{});return f.useEffect(()=>{const o=a=>{if(a.target&&!r.current){let c=function(){xl(Cy,n,d,{discrete:!0})};const d={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=c,t.addEventListener("click",s.current,{once:!0})):c()}else t.removeEventListener("click",s.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",o)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",o),t.removeEventListener("click",s.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Ey(e,t=globalThis?.document){const n=Oe(e),r=f.useRef(!1);return f.useEffect(()=>{const s=o=>{o.target&&!r.current&&xl(Sy,n,{originalEvent:o},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Zi(){const e=new CustomEvent(cs);document.dispatchEvent(e)}function xl(e,t,n,{discrete:r}){const s=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?Ea(s,o):s.dispatchEvent(o)}var Rr="focusScope.autoFocusOnMount",Mr="focusScope.autoFocusOnUnmount",Ji={bubbles:!1,cancelable:!0},Ry="FocusScope",oo=f.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:o,...i}=e,[a,c]=f.useState(null),d=Oe(s),u=Oe(o),h=f.useRef(null),p=ee(t,v=>c(v)),m=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let v=function(w){if(m.paused||!a)return;const C=w.target;a.contains(C)?h.current=C:We(h.current,{select:!0})},y=function(w){if(m.paused||!a)return;const C=w.relatedTarget;C!==null&&(a.contains(C)||We(h.current,{select:!0}))},x=function(w){if(document.activeElement===document.body)for(const S of w)S.removedNodes.length>0&&We(a)};document.addEventListener("focusin",v),document.addEventListener("focusout",y);const b=new MutationObserver(x);return a&&b.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",v),document.removeEventListener("focusout",y),b.disconnect()}}},[r,a,m.paused]),f.useEffect(()=>{if(a){ea.add(m);const v=document.activeElement;if(!a.contains(v)){const x=new CustomEvent(Rr,Ji);a.addEventListener(Rr,d),a.dispatchEvent(x),x.defaultPrevented||(My(ky(bl(a)),{select:!0}),document.activeElement===v&&We(a))}return()=>{a.removeEventListener(Rr,d),setTimeout(()=>{const x=new CustomEvent(Mr,Ji);a.addEventListener(Mr,u),a.dispatchEvent(x),x.defaultPrevented||We(v??document.body,{select:!0}),a.removeEventListener(Mr,u),ea.remove(m)},0)}}},[a,d,u,m]);const g=f.useCallback(v=>{if(!n&&!r||m.paused)return;const y=v.key==="Tab"&&!v.altKey&&!v.ctrlKey&&!v.metaKey,x=document.activeElement;if(y&&x){const b=v.currentTarget,[w,C]=Dy(b);w&&C?!v.shiftKey&&x===C?(v.preventDefault(),n&&We(w,{select:!0})):v.shiftKey&&x===w&&(v.preventDefault(),n&&We(C,{select:!0})):x===b&&v.preventDefault()}},[n,r,m.paused]);return l.jsx(J.div,{tabIndex:-1,...i,ref:p,onKeyDown:g})});oo.displayName=Ry;function My(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(We(r,{select:t}),document.activeElement!==n)return}function Dy(e){const t=bl(e),n=Qi(t,e),r=Qi(t.reverse(),e);return[n,r]}function bl(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Qi(e,t){for(const n of e)if(!Ny(n,{upTo:t}))return n}function Ny(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function jy(e){return e instanceof HTMLInputElement&&"select"in e}function We(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&jy(e)&&t&&e.select()}}var ea=Iy();function Iy(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=ta(e,t),e.unshift(t)},remove(t){e=ta(e,t),e[0]?.resume()}}}function ta(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function ky(e){return e.filter(t=>t.tagName!=="A")}var Oy="Portal",io=f.forwardRef((e,t)=>{const{container:n,...r}=e,[s,o]=f.useState(!1);Ke(()=>o(!0),[]);const i=n||s&&globalThis?.document?.body;return i?hf.createPortal(l.jsx(J.div,{...r,ref:t}),i):null});io.displayName=Oy;function Ly(e,t){return f.useReducer((n,r)=>t[n][r]??n,e)}var Be=e=>{const{present:t,children:n}=e,r=_y(t),s=typeof n=="function"?n({present:r.isPresent}):f.Children.only(n),o=ee(r.ref,Vy(s));return typeof n=="function"||r.isPresent?f.cloneElement(s,{ref:o}):null};Be.displayName="Presence";function _y(e){const[t,n]=f.useState(),r=f.useRef(null),s=f.useRef(e),o=f.useRef("none"),i=e?"mounted":"unmounted",[a,c]=Ly(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return f.useEffect(()=>{const d=pn(r.current);o.current=a==="mounted"?d:"none"},[a]),Ke(()=>{const d=r.current,u=s.current;if(u!==e){const p=o.current,m=pn(d);e?c("MOUNT"):m==="none"||d?.display==="none"?c("UNMOUNT"):c(u&&p!==m?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,c]),Ke(()=>{if(t){let d;const u=t.ownerDocument.defaultView??window,h=m=>{const v=pn(r.current).includes(m.animationName);if(m.target===t&&v&&(c("ANIMATION_END"),!s.current)){const y=t.style.animationFillMode;t.style.animationFillMode="forwards",d=u.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=y)})}},p=m=>{m.target===t&&(o.current=pn(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",h),t.addEventListener("animationend",h),()=>{u.clearTimeout(d),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",h),t.removeEventListener("animationend",h)}}else c("ANIMATION_END")},[t,c]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:f.useCallback(d=>{r.current=d?getComputedStyle(d):null,n(d)},[])}}function pn(e){return e?.animationName||"none"}function Vy(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Dr=0;function wl(){f.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??na()),document.body.insertAdjacentElement("beforeend",e[1]??na()),Dr++,()=>{Dr===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Dr--}},[])}function na(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Ee=function(){return Ee=Object.assign||function(t){for(var n,r=1,s=arguments.length;r<s;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},Ee.apply(this,arguments)};function Cl(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n}function Fy(e,t,n){if(n||arguments.length===2)for(var r=0,s=t.length,o;r<s;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}var An="right-scroll-bar-position",Pn="width-before-scroll-bar",By="with-scroll-bars-hidden",$y="--removed-body-scroll-bar-size";function Nr(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Uy(e,t){var n=f.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var s=n.value;s!==r&&(n.value=r,n.callback(r,s))}}}})[0];return n.callback=t,n.facade}var zy=typeof window<"u"?f.useLayoutEffect:f.useEffect,ra=new WeakMap;function Wy(e,t){var n=Uy(null,function(r){return e.forEach(function(s){return Nr(s,r)})});return zy(function(){var r=ra.get(n);if(r){var s=new Set(r),o=new Set(e),i=n.current;s.forEach(function(a){o.has(a)||Nr(a,null)}),o.forEach(function(a){s.has(a)||Nr(a,i)})}ra.set(n,e)},[e]),n}function Hy(e){return e}function Ky(e,t){t===void 0&&(t=Hy);var n=[],r=!1,s={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(o){var i=t(o,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(o){for(r=!0;n.length;){var i=n;n=[],i.forEach(o)}n={push:function(a){return o(a)},filter:function(){return n}}},assignMedium:function(o){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(o),i=n}var c=function(){var u=i;i=[],u.forEach(o)},d=function(){return Promise.resolve().then(c)};d(),n={push:function(u){i.push(u),d()},filter:function(u){return i=i.filter(u),n}}}};return s}function Gy(e){e===void 0&&(e={});var t=Ky(null);return t.options=Ee({async:!0,ssr:!1},e),t}var Sl=function(e){var t=e.sideCar,n=Cl(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return f.createElement(r,Ee({},n))};Sl.isSideCarExport=!0;function Yy(e,t){return e.useMedium(t),Sl}var Tl=Gy(),jr=function(){},Xn=f.forwardRef(function(e,t){var n=f.useRef(null),r=f.useState({onScrollCapture:jr,onWheelCapture:jr,onTouchMoveCapture:jr}),s=r[0],o=r[1],i=e.forwardProps,a=e.children,c=e.className,d=e.removeScrollBar,u=e.enabled,h=e.shards,p=e.sideCar,m=e.noRelative,g=e.noIsolation,v=e.inert,y=e.allowPinchZoom,x=e.as,b=x===void 0?"div":x,w=e.gapMode,C=Cl(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=p,A=Wy([n,t]),T=Ee(Ee({},C),s);return f.createElement(f.Fragment,null,u&&f.createElement(S,{sideCar:Tl,removeScrollBar:d,shards:h,noRelative:m,noIsolation:g,inert:v,setCallbacks:o,allowPinchZoom:!!y,lockRef:n,gapMode:w}),i?f.cloneElement(f.Children.only(a),Ee(Ee({},T),{ref:A})):f.createElement(b,Ee({},T,{className:c,ref:A}),a))});Xn.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Xn.classNames={fullWidth:Pn,zeroRight:An};var Xy=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function qy(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Xy();return t&&e.setAttribute("nonce",t),e}function Zy(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Jy(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Qy=function(){var e=0,t=null;return{add:function(n){e==0&&(t=qy())&&(Zy(t,n),Jy(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},ex=function(){var e=Qy();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Al=function(){var e=ex(),t=function(n){var r=n.styles,s=n.dynamic;return e(r,s),null};return t},tx={left:0,top:0,right:0,gap:0},Ir=function(e){return parseInt(e||"",10)||0},nx=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],s=t[e==="padding"?"paddingRight":"marginRight"];return[Ir(n),Ir(r),Ir(s)]},rx=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return tx;var t=nx(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},sx=Al(),xt="data-scroll-locked",ox=function(e,t,n,r){var s=e.left,o=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(By,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(xt,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(o,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(An,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Pn,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(An," .").concat(An,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Pn," .").concat(Pn,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(xt,`] {
    `).concat($y,": ").concat(a,`px;
  }
`)},sa=function(){var e=parseInt(document.body.getAttribute(xt)||"0",10);return isFinite(e)?e:0},ix=function(){f.useEffect(function(){return document.body.setAttribute(xt,(sa()+1).toString()),function(){var e=sa()-1;e<=0?document.body.removeAttribute(xt):document.body.setAttribute(xt,e.toString())}},[])},ax=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,s=r===void 0?"margin":r;ix();var o=f.useMemo(function(){return rx(s)},[s]);return f.createElement(sx,{styles:ox(o,!t,s,n?"":"!important")})},ls=!1;if(typeof window<"u")try{var mn=Object.defineProperty({},"passive",{get:function(){return ls=!0,!0}});window.addEventListener("test",mn,mn),window.removeEventListener("test",mn,mn)}catch{ls=!1}var ut=ls?{passive:!1}:!1,cx=function(e){return e.tagName==="TEXTAREA"},Pl=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!cx(e)&&n[t]==="visible")},lx=function(e){return Pl(e,"overflowY")},ux=function(e){return Pl(e,"overflowX")},oa=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var s=El(e,r);if(s){var o=Rl(e,r),i=o[1],a=o[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},dx=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},fx=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},El=function(e,t){return e==="v"?lx(t):ux(t)},Rl=function(e,t){return e==="v"?dx(t):fx(t)},hx=function(e,t){return e==="h"&&t==="rtl"?-1:1},px=function(e,t,n,r,s){var o=hx(e,window.getComputedStyle(t).direction),i=o*r,a=n.target,c=t.contains(a),d=!1,u=i>0,h=0,p=0;do{if(!a)break;var m=Rl(e,a),g=m[0],v=m[1],y=m[2],x=v-y-o*g;(g||x)&&El(e,a)&&(h+=x,p+=g);var b=a.parentNode;a=b&&b.nodeType===Node.DOCUMENT_FRAGMENT_NODE?b.host:b}while(!c&&a!==document.body||c&&(t.contains(a)||t===a));return(u&&Math.abs(h)<1||!u&&Math.abs(p)<1)&&(d=!0),d},gn=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ia=function(e){return[e.deltaX,e.deltaY]},aa=function(e){return e&&"current"in e?e.current:e},mx=function(e,t){return e[0]===t[0]&&e[1]===t[1]},gx=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},vx=0,dt=[];function yx(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),s=f.useState(vx++)[0],o=f.useState(Al)[0],i=f.useRef(e);f.useEffect(function(){i.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var v=Fy([e.lockRef.current],(e.shards||[]).map(aa),!0).filter(Boolean);return v.forEach(function(y){return y.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),v.forEach(function(y){return y.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var a=f.useCallback(function(v,y){if("touches"in v&&v.touches.length===2||v.type==="wheel"&&v.ctrlKey)return!i.current.allowPinchZoom;var x=gn(v),b=n.current,w="deltaX"in v?v.deltaX:b[0]-x[0],C="deltaY"in v?v.deltaY:b[1]-x[1],S,A=v.target,T=Math.abs(w)>Math.abs(C)?"h":"v";if("touches"in v&&T==="h"&&A.type==="range")return!1;var E=oa(T,A);if(!E)return!0;if(E?S=T:(S=T==="v"?"h":"v",E=oa(T,A)),!E)return!1;if(!r.current&&"changedTouches"in v&&(w||C)&&(r.current=S),!S)return!0;var P=r.current||S;return px(P,y,v,P==="h"?w:C)},[]),c=f.useCallback(function(v){var y=v;if(!(!dt.length||dt[dt.length-1]!==o)){var x="deltaY"in y?ia(y):gn(y),b=t.current.filter(function(S){return S.name===y.type&&(S.target===y.target||y.target===S.shadowParent)&&mx(S.delta,x)})[0];if(b&&b.should){y.cancelable&&y.preventDefault();return}if(!b){var w=(i.current.shards||[]).map(aa).filter(Boolean).filter(function(S){return S.contains(y.target)}),C=w.length>0?a(y,w[0]):!i.current.noIsolation;C&&y.cancelable&&y.preventDefault()}}},[]),d=f.useCallback(function(v,y,x,b){var w={name:v,delta:y,target:x,should:b,shadowParent:xx(x)};t.current.push(w),setTimeout(function(){t.current=t.current.filter(function(C){return C!==w})},1)},[]),u=f.useCallback(function(v){n.current=gn(v),r.current=void 0},[]),h=f.useCallback(function(v){d(v.type,ia(v),v.target,a(v,e.lockRef.current))},[]),p=f.useCallback(function(v){d(v.type,gn(v),v.target,a(v,e.lockRef.current))},[]);f.useEffect(function(){return dt.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ut),document.addEventListener("touchmove",c,ut),document.addEventListener("touchstart",u,ut),function(){dt=dt.filter(function(v){return v!==o}),document.removeEventListener("wheel",c,ut),document.removeEventListener("touchmove",c,ut),document.removeEventListener("touchstart",u,ut)}},[]);var m=e.removeScrollBar,g=e.inert;return f.createElement(f.Fragment,null,g?f.createElement(o,{styles:gx(s)}):null,m?f.createElement(ax,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function xx(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const bx=Yy(Tl,yx);var ao=f.forwardRef(function(e,t){return f.createElement(Xn,Ee({},e,{ref:t,sideCar:bx}))});ao.classNames=Xn.classNames;var wx=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},ft=new WeakMap,vn=new WeakMap,yn={},kr=0,Ml=function(e){return e&&(e.host||Ml(e.parentNode))},Cx=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Ml(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Sx=function(e,t,n,r){var s=Cx(t,Array.isArray(e)?e:[e]);yn[n]||(yn[n]=new WeakMap);var o=yn[n],i=[],a=new Set,c=new Set(s),d=function(h){!h||a.has(h)||(a.add(h),d(h.parentNode))};s.forEach(d);var u=function(h){!h||c.has(h)||Array.prototype.forEach.call(h.children,function(p){if(a.has(p))u(p);else try{var m=p.getAttribute(r),g=m!==null&&m!=="false",v=(ft.get(p)||0)+1,y=(o.get(p)||0)+1;ft.set(p,v),o.set(p,y),i.push(p),v===1&&g&&vn.set(p,!0),y===1&&p.setAttribute(n,"true"),g||p.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",p,x)}})};return u(t),a.clear(),kr++,function(){i.forEach(function(h){var p=ft.get(h)-1,m=o.get(h)-1;ft.set(h,p),o.set(h,m),p||(vn.has(h)||h.removeAttribute(r),vn.delete(h)),m||h.removeAttribute(n)}),kr--,kr||(ft=new WeakMap,ft=new WeakMap,vn=new WeakMap,yn={})}},Dl=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),s=wx(e);return s?(r.push.apply(r,Array.from(s.querySelectorAll("[aria-live], script"))),Sx(r,s,n,"aria-hidden")):function(){return null}},qn="Dialog",[Nl,jl]=Ze(qn),[Tx,Te]=Nl(qn),Il=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:s,onOpenChange:o,modal:i=!0}=e,a=f.useRef(null),c=f.useRef(null),[d,u]=Gn({prop:r,defaultProp:s??!1,onChange:o,caller:qn});return l.jsx(Tx,{scope:t,triggerRef:a,contentRef:c,contentId:ot(),titleId:ot(),descriptionId:ot(),open:d,onOpenChange:u,onOpenToggle:f.useCallback(()=>u(h=>!h),[u]),modal:i,children:n})};Il.displayName=qn;var kl="DialogTrigger",Ol=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=Te(kl,n),o=ee(t,s.triggerRef);return l.jsx(J.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":uo(s.open),...r,ref:o,onClick:B(e.onClick,s.onOpenToggle)})});Ol.displayName=kl;var co="DialogPortal",[Ax,Ll]=Nl(co,{forceMount:void 0}),_l=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:s}=e,o=Te(co,t);return l.jsx(Ax,{scope:t,forceMount:n,children:f.Children.map(r,i=>l.jsx(Be,{present:n||o.open,children:l.jsx(io,{asChild:!0,container:s,children:i})}))})};_l.displayName=co;var On="DialogOverlay",Vl=f.forwardRef((e,t)=>{const n=Ll(On,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,o=Te(On,e.__scopeDialog);return o.modal?l.jsx(Be,{present:r||o.open,children:l.jsx(Ex,{...s,ref:t})}):null});Vl.displayName=On;var Px=Rn("DialogOverlay.RemoveScroll"),Ex=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=Te(On,n);return l.jsx(ao,{as:Px,allowPinchZoom:!0,shards:[s.contentRef],children:l.jsx(J.div,{"data-state":uo(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),it="DialogContent",Fl=f.forwardRef((e,t)=>{const n=Ll(it,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,o=Te(it,e.__scopeDialog);return l.jsx(Be,{present:r||o.open,children:o.modal?l.jsx(Rx,{...s,ref:t}):l.jsx(Mx,{...s,ref:t})})});Fl.displayName=it;var Rx=f.forwardRef((e,t)=>{const n=Te(it,e.__scopeDialog),r=f.useRef(null),s=ee(t,n.contentRef,r);return f.useEffect(()=>{const o=r.current;if(o)return Dl(o)},[]),l.jsx(Bl,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:B(e.onCloseAutoFocus,o=>{o.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:B(e.onPointerDownOutside,o=>{const i=o.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&o.preventDefault()}),onFocusOutside:B(e.onFocusOutside,o=>o.preventDefault())})}),Mx=f.forwardRef((e,t)=>{const n=Te(it,e.__scopeDialog),r=f.useRef(!1),s=f.useRef(!1);return l.jsx(Bl,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{e.onCloseAutoFocus?.(o),o.defaultPrevented||(r.current||n.triggerRef.current?.focus(),o.preventDefault()),r.current=!1,s.current=!1},onInteractOutside:o=>{e.onInteractOutside?.(o),o.defaultPrevented||(r.current=!0,o.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const i=o.target;n.triggerRef.current?.contains(i)&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&s.current&&o.preventDefault()}})}),Bl=f.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:o,...i}=e,a=Te(it,n),c=f.useRef(null),d=ee(t,c);return wl(),l.jsxs(l.Fragment,{children:[l.jsx(oo,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:o,children:l.jsx(Yn,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":uo(a.open),...i,ref:d,onDismiss:()=>a.onOpenChange(!1)})}),l.jsxs(l.Fragment,{children:[l.jsx(Nx,{titleId:a.titleId}),l.jsx(Ix,{contentRef:c,descriptionId:a.descriptionId})]})]})}),lo="DialogTitle",$l=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=Te(lo,n);return l.jsx(J.h2,{id:s.titleId,...r,ref:t})});$l.displayName=lo;var Ul="DialogDescription",zl=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=Te(Ul,n);return l.jsx(J.p,{id:s.descriptionId,...r,ref:t})});zl.displayName=Ul;var Wl="DialogClose",Hl=f.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=Te(Wl,n);return l.jsx(J.button,{type:"button",...r,ref:t,onClick:B(e.onClick,()=>s.onOpenChange(!1))})});Hl.displayName=Wl;function uo(e){return e?"open":"closed"}var Kl="DialogTitleWarning",[Dx,Gl]=yf(Kl,{contentName:it,titleName:lo,docsSlug:"dialog"}),Nx=({titleId:e})=>{const t=Gl(Kl),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return f.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},jx="DialogDescriptionWarning",Ix=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Gl(jx).contentName}}.`;return f.useEffect(()=>{const s=e.current?.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Yl=Il,kx=Ol,Xl=_l,fo=Vl,ho=Fl,po=$l,mo=zl,go=Hl;const Ox=Yl,Lx=Xl,ql=f.forwardRef(({className:e,...t},n)=>l.jsx(fo,{className:j("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));ql.displayName=fo.displayName;const _x=ws("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),Zl=f.forwardRef(({side:e="right",className:t,children:n,...r},s)=>l.jsxs(Lx,{children:[l.jsx(ql,{}),l.jsxs(ho,{ref:s,className:j(_x({side:e}),t),...r,children:[n,l.jsxs(go,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[l.jsx(Ss,{className:"h-4 w-4"}),l.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Zl.displayName=ho.displayName;const Vx=f.forwardRef(({className:e,...t},n)=>l.jsx(po,{ref:n,className:j("text-lg font-semibold text-foreground",e),...t}));Vx.displayName=po.displayName;const Fx=f.forwardRef(({className:e,...t},n)=>l.jsx(mo,{ref:n,className:j("text-sm text-muted-foreground",e),...t}));Fx.displayName=mo.displayName;function ca({className:e,...t}){return l.jsx("div",{className:j("animate-pulse rounded-md bg-muted",e),...t})}const Bx=["top","right","bottom","left"],Xe=Math.min,he=Math.max,Ln=Math.round,xn=Math.floor,je=e=>({x:e,y:e}),$x={left:"right",right:"left",bottom:"top",top:"bottom"},Ux={start:"end",end:"start"};function us(e,t,n){return he(e,Xe(t,n))}function Ve(e,t){return typeof e=="function"?e(t):e}function Fe(e){return e.split("-")[0]}function Mt(e){return e.split("-")[1]}function vo(e){return e==="x"?"y":"x"}function yo(e){return e==="y"?"height":"width"}const zx=new Set(["top","bottom"]);function Re(e){return zx.has(Fe(e))?"y":"x"}function xo(e){return vo(Re(e))}function Wx(e,t,n){n===void 0&&(n=!1);const r=Mt(e),s=xo(e),o=yo(s);let i=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(i=_n(i)),[i,_n(i)]}function Hx(e){const t=_n(e);return[ds(e),t,ds(t)]}function ds(e){return e.replace(/start|end/g,t=>Ux[t])}const la=["left","right"],ua=["right","left"],Kx=["top","bottom"],Gx=["bottom","top"];function Yx(e,t,n){switch(e){case"top":case"bottom":return n?t?ua:la:t?la:ua;case"left":case"right":return t?Kx:Gx;default:return[]}}function Xx(e,t,n,r){const s=Mt(e);let o=Yx(Fe(e),n==="start",r);return s&&(o=o.map(i=>i+"-"+s),t&&(o=o.concat(o.map(ds)))),o}function _n(e){return e.replace(/left|right|bottom|top/g,t=>$x[t])}function qx(e){return{top:0,right:0,bottom:0,left:0,...e}}function Jl(e){return typeof e!="number"?qx(e):{top:e,right:e,bottom:e,left:e}}function Vn(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function da(e,t,n){let{reference:r,floating:s}=e;const o=Re(t),i=xo(t),a=yo(i),c=Fe(t),d=o==="y",u=r.x+r.width/2-s.width/2,h=r.y+r.height/2-s.height/2,p=r[a]/2-s[a]/2;let m;switch(c){case"top":m={x:u,y:r.y-s.height};break;case"bottom":m={x:u,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:h};break;case"left":m={x:r.x-s.width,y:h};break;default:m={x:r.x,y:r.y}}switch(Mt(t)){case"start":m[i]-=p*(n&&d?-1:1);break;case"end":m[i]+=p*(n&&d?-1:1);break}return m}const Zx=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:o=[],platform:i}=n,a=o.filter(Boolean),c=await(i.isRTL==null?void 0:i.isRTL(t));let d=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:u,y:h}=da(d,r,c),p=r,m={},g=0;for(let v=0;v<a.length;v++){const{name:y,fn:x}=a[v],{x:b,y:w,data:C,reset:S}=await x({x:u,y:h,initialPlacement:r,placement:p,strategy:s,middlewareData:m,rects:d,platform:i,elements:{reference:e,floating:t}});u=b??u,h=w??h,m={...m,[y]:{...m[y],...C}},S&&g<=50&&(g++,typeof S=="object"&&(S.placement&&(p=S.placement),S.rects&&(d=S.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):S.rects),{x:u,y:h}=da(d,p,c)),v=-1)}return{x:u,y:h,placement:p,strategy:s,middlewareData:m}};async function Gt(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:o,rects:i,elements:a,strategy:c}=e,{boundary:d="clippingAncestors",rootBoundary:u="viewport",elementContext:h="floating",altBoundary:p=!1,padding:m=0}=Ve(t,e),g=Jl(m),y=a[p?h==="floating"?"reference":"floating":h],x=Vn(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(y)))==null||n?y:y.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(a.floating)),boundary:d,rootBoundary:u,strategy:c})),b=h==="floating"?{x:r,y:s,width:i.floating.width,height:i.floating.height}:i.reference,w=await(o.getOffsetParent==null?void 0:o.getOffsetParent(a.floating)),C=await(o.isElement==null?void 0:o.isElement(w))?await(o.getScale==null?void 0:o.getScale(w))||{x:1,y:1}:{x:1,y:1},S=Vn(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:w,strategy:c}):b);return{top:(x.top-S.top+g.top)/C.y,bottom:(S.bottom-x.bottom+g.bottom)/C.y,left:(x.left-S.left+g.left)/C.x,right:(S.right-x.right+g.right)/C.x}}const Jx=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:o,platform:i,elements:a,middlewareData:c}=t,{element:d,padding:u=0}=Ve(e,t)||{};if(d==null)return{};const h=Jl(u),p={x:n,y:r},m=xo(s),g=yo(m),v=await i.getDimensions(d),y=m==="y",x=y?"top":"left",b=y?"bottom":"right",w=y?"clientHeight":"clientWidth",C=o.reference[g]+o.reference[m]-p[m]-o.floating[g],S=p[m]-o.reference[m],A=await(i.getOffsetParent==null?void 0:i.getOffsetParent(d));let T=A?A[w]:0;(!T||!await(i.isElement==null?void 0:i.isElement(A)))&&(T=a.floating[w]||o.floating[g]);const E=C/2-S/2,P=T/2-v[g]/2-1,D=Xe(h[x],P),O=Xe(h[b],P),W=D,H=T-v[g]-O,z=T/2-v[g]/2+E,G=us(W,z,H),U=!c.arrow&&Mt(s)!=null&&z!==G&&o.reference[g]/2-(z<W?D:O)-v[g]/2<0,L=U?z<W?z-W:z-H:0;return{[m]:p[m]+L,data:{[m]:G,centerOffset:z-G-L,...U&&{alignmentOffset:L}},reset:U}}}),Qx=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:o,rects:i,initialPlacement:a,platform:c,elements:d}=t,{mainAxis:u=!0,crossAxis:h=!0,fallbackPlacements:p,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:v=!0,...y}=Ve(e,t);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const x=Fe(s),b=Re(a),w=Fe(a)===a,C=await(c.isRTL==null?void 0:c.isRTL(d.floating)),S=p||(w||!v?[_n(a)]:Hx(a)),A=g!=="none";!p&&A&&S.push(...Xx(a,v,g,C));const T=[a,...S],E=await Gt(t,y),P=[];let D=((r=o.flip)==null?void 0:r.overflows)||[];if(u&&P.push(E[x]),h){const z=Wx(s,i,C);P.push(E[z[0]],E[z[1]])}if(D=[...D,{placement:s,overflows:P}],!P.every(z=>z<=0)){var O,W;const z=(((O=o.flip)==null?void 0:O.index)||0)+1,G=T[z];if(G&&(!(h==="alignment"?b!==Re(G):!1)||D.every(I=>I.overflows[0]>0&&Re(I.placement)===b)))return{data:{index:z,overflows:D},reset:{placement:G}};let U=(W=D.filter(L=>L.overflows[0]<=0).sort((L,I)=>L.overflows[1]-I.overflows[1])[0])==null?void 0:W.placement;if(!U)switch(m){case"bestFit":{var H;const L=(H=D.filter(I=>{if(A){const M=Re(I.placement);return M===b||M==="y"}return!0}).map(I=>[I.placement,I.overflows.filter(M=>M>0).reduce((M,k)=>M+k,0)]).sort((I,M)=>I[1]-M[1])[0])==null?void 0:H[0];L&&(U=L);break}case"initialPlacement":U=a;break}if(s!==U)return{reset:{placement:U}}}return{}}}};function fa(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ha(e){return Bx.some(t=>e[t]>=0)}const eb=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...s}=Ve(e,t);switch(r){case"referenceHidden":{const o=await Gt(t,{...s,elementContext:"reference"}),i=fa(o,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:ha(i)}}}case"escaped":{const o=await Gt(t,{...s,altBoundary:!0}),i=fa(o,n.floating);return{data:{escapedOffsets:i,escaped:ha(i)}}}default:return{}}}}},Ql=new Set(["left","top"]);async function tb(e,t){const{placement:n,platform:r,elements:s}=e,o=await(r.isRTL==null?void 0:r.isRTL(s.floating)),i=Fe(n),a=Mt(n),c=Re(n)==="y",d=Ql.has(i)?-1:1,u=o&&c?-1:1,h=Ve(t,e);let{mainAxis:p,crossAxis:m,alignmentAxis:g}=typeof h=="number"?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&typeof g=="number"&&(m=a==="end"?g*-1:g),c?{x:m*u,y:p*d}:{x:p*d,y:m*u}}const nb=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:o,placement:i,middlewareData:a}=t,c=await tb(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:s+c.x,y:o+c.y,data:{...c,placement:i}}}}},rb=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:s}=t,{mainAxis:o=!0,crossAxis:i=!1,limiter:a={fn:y=>{let{x,y:b}=y;return{x,y:b}}},...c}=Ve(e,t),d={x:n,y:r},u=await Gt(t,c),h=Re(Fe(s)),p=vo(h);let m=d[p],g=d[h];if(o){const y=p==="y"?"top":"left",x=p==="y"?"bottom":"right",b=m+u[y],w=m-u[x];m=us(b,m,w)}if(i){const y=h==="y"?"top":"left",x=h==="y"?"bottom":"right",b=g+u[y],w=g-u[x];g=us(b,g,w)}const v=a.fn({...t,[p]:m,[h]:g});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[p]:o,[h]:i}}}}}},sb=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:s,rects:o,middlewareData:i}=t,{offset:a=0,mainAxis:c=!0,crossAxis:d=!0}=Ve(e,t),u={x:n,y:r},h=Re(s),p=vo(h);let m=u[p],g=u[h];const v=Ve(a,t),y=typeof v=="number"?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(c){const w=p==="y"?"height":"width",C=o.reference[p]-o.floating[w]+y.mainAxis,S=o.reference[p]+o.reference[w]-y.mainAxis;m<C?m=C:m>S&&(m=S)}if(d){var x,b;const w=p==="y"?"width":"height",C=Ql.has(Fe(s)),S=o.reference[h]-o.floating[w]+(C&&((x=i.offset)==null?void 0:x[h])||0)+(C?0:y.crossAxis),A=o.reference[h]+o.reference[w]+(C?0:((b=i.offset)==null?void 0:b[h])||0)-(C?y.crossAxis:0);g<S?g=S:g>A&&(g=A)}return{[p]:m,[h]:g}}}},ob=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:s,rects:o,platform:i,elements:a}=t,{apply:c=()=>{},...d}=Ve(e,t),u=await Gt(t,d),h=Fe(s),p=Mt(s),m=Re(s)==="y",{width:g,height:v}=o.floating;let y,x;h==="top"||h==="bottom"?(y=h,x=p===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(x=h,y=p==="end"?"top":"bottom");const b=v-u.top-u.bottom,w=g-u.left-u.right,C=Xe(v-u[y],b),S=Xe(g-u[x],w),A=!t.middlewareData.shift;let T=C,E=S;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(E=w),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(T=b),A&&!p){const D=he(u.left,0),O=he(u.right,0),W=he(u.top,0),H=he(u.bottom,0);m?E=g-2*(D!==0||O!==0?D+O:he(u.left,u.right)):T=v-2*(W!==0||H!==0?W+H:he(u.top,u.bottom))}await c({...t,availableWidth:E,availableHeight:T});const P=await i.getDimensions(a.floating);return g!==P.width||v!==P.height?{reset:{rects:!0}}:{}}}};function Zn(){return typeof window<"u"}function Dt(e){return eu(e)?(e.nodeName||"").toLowerCase():"#document"}function me(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ke(e){var t;return(t=(eu(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function eu(e){return Zn()?e instanceof Node||e instanceof me(e).Node:!1}function Ce(e){return Zn()?e instanceof Element||e instanceof me(e).Element:!1}function Ie(e){return Zn()?e instanceof HTMLElement||e instanceof me(e).HTMLElement:!1}function pa(e){return!Zn()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof me(e).ShadowRoot}const ib=new Set(["inline","contents"]);function sn(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=Se(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!ib.has(s)}const ab=new Set(["table","td","th"]);function cb(e){return ab.has(Dt(e))}const lb=[":popover-open",":modal"];function Jn(e){return lb.some(t=>{try{return e.matches(t)}catch{return!1}})}const ub=["transform","translate","scale","rotate","perspective"],db=["transform","translate","scale","rotate","perspective","filter"],fb=["paint","layout","strict","content"];function bo(e){const t=wo(),n=Ce(e)?Se(e):e;return ub.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||db.some(r=>(n.willChange||"").includes(r))||fb.some(r=>(n.contain||"").includes(r))}function hb(e){let t=qe(e);for(;Ie(t)&&!Tt(t);){if(bo(t))return t;if(Jn(t))return null;t=qe(t)}return null}function wo(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const pb=new Set(["html","body","#document"]);function Tt(e){return pb.has(Dt(e))}function Se(e){return me(e).getComputedStyle(e)}function Qn(e){return Ce(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function qe(e){if(Dt(e)==="html")return e;const t=e.assignedSlot||e.parentNode||pa(e)&&e.host||ke(e);return pa(t)?t.host:t}function tu(e){const t=qe(e);return Tt(t)?e.ownerDocument?e.ownerDocument.body:e.body:Ie(t)&&sn(t)?t:tu(t)}function Yt(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=tu(e),o=s===((r=e.ownerDocument)==null?void 0:r.body),i=me(s);if(o){const a=fs(i);return t.concat(i,i.visualViewport||[],sn(s)?s:[],a&&n?Yt(a):[])}return t.concat(s,Yt(s,[],n))}function fs(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function nu(e){const t=Se(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=Ie(e),o=s?e.offsetWidth:n,i=s?e.offsetHeight:r,a=Ln(n)!==o||Ln(r)!==i;return a&&(n=o,r=i),{width:n,height:r,$:a}}function Co(e){return Ce(e)?e:e.contextElement}function bt(e){const t=Co(e);if(!Ie(t))return je(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:o}=nu(t);let i=(o?Ln(n.width):n.width)/r,a=(o?Ln(n.height):n.height)/s;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const mb=je(0);function ru(e){const t=me(e);return!wo()||!t.visualViewport?mb:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function gb(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==me(e)?!1:t}function at(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),o=Co(e);let i=je(1);t&&(r?Ce(r)&&(i=bt(r)):i=bt(e));const a=gb(o,n,r)?ru(o):je(0);let c=(s.left+a.x)/i.x,d=(s.top+a.y)/i.y,u=s.width/i.x,h=s.height/i.y;if(o){const p=me(o),m=r&&Ce(r)?me(r):r;let g=p,v=fs(g);for(;v&&r&&m!==g;){const y=bt(v),x=v.getBoundingClientRect(),b=Se(v),w=x.left+(v.clientLeft+parseFloat(b.paddingLeft))*y.x,C=x.top+(v.clientTop+parseFloat(b.paddingTop))*y.y;c*=y.x,d*=y.y,u*=y.x,h*=y.y,c+=w,d+=C,g=me(v),v=fs(g)}}return Vn({width:u,height:h,x:c,y:d})}function So(e,t){const n=Qn(e).scrollLeft;return t?t.left+n:at(ke(e)).left+n}function su(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=r.left+t.scrollLeft-(n?0:So(e,r)),o=r.top+t.scrollTop;return{x:s,y:o}}function vb(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const o=s==="fixed",i=ke(r),a=t?Jn(t.floating):!1;if(r===i||a&&o)return n;let c={scrollLeft:0,scrollTop:0},d=je(1);const u=je(0),h=Ie(r);if((h||!h&&!o)&&((Dt(r)!=="body"||sn(i))&&(c=Qn(r)),Ie(r))){const m=at(r);d=bt(r),u.x=m.x+r.clientLeft,u.y=m.y+r.clientTop}const p=i&&!h&&!o?su(i,c,!0):je(0);return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-c.scrollLeft*d.x+u.x+p.x,y:n.y*d.y-c.scrollTop*d.y+u.y+p.y}}function yb(e){return Array.from(e.getClientRects())}function xb(e){const t=ke(e),n=Qn(e),r=e.ownerDocument.body,s=he(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=he(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+So(e);const a=-n.scrollTop;return Se(r).direction==="rtl"&&(i+=he(t.clientWidth,r.clientWidth)-s),{width:s,height:o,x:i,y:a}}function bb(e,t){const n=me(e),r=ke(e),s=n.visualViewport;let o=r.clientWidth,i=r.clientHeight,a=0,c=0;if(s){o=s.width,i=s.height;const d=wo();(!d||d&&t==="fixed")&&(a=s.offsetLeft,c=s.offsetTop)}return{width:o,height:i,x:a,y:c}}const wb=new Set(["absolute","fixed"]);function Cb(e,t){const n=at(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,o=Ie(e)?bt(e):je(1),i=e.clientWidth*o.x,a=e.clientHeight*o.y,c=s*o.x,d=r*o.y;return{width:i,height:a,x:c,y:d}}function ma(e,t,n){let r;if(t==="viewport")r=bb(e,n);else if(t==="document")r=xb(ke(e));else if(Ce(t))r=Cb(t,n);else{const s=ru(e);r={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return Vn(r)}function ou(e,t){const n=qe(e);return n===t||!Ce(n)||Tt(n)?!1:Se(n).position==="fixed"||ou(n,t)}function Sb(e,t){const n=t.get(e);if(n)return n;let r=Yt(e,[],!1).filter(a=>Ce(a)&&Dt(a)!=="body"),s=null;const o=Se(e).position==="fixed";let i=o?qe(e):e;for(;Ce(i)&&!Tt(i);){const a=Se(i),c=bo(i);!c&&a.position==="fixed"&&(s=null),(o?!c&&!s:!c&&a.position==="static"&&!!s&&wb.has(s.position)||sn(i)&&!c&&ou(e,i))?r=r.filter(u=>u!==i):s=a,i=qe(i)}return t.set(e,r),r}function Tb(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const i=[...n==="clippingAncestors"?Jn(t)?[]:Sb(t,this._c):[].concat(n),r],a=i[0],c=i.reduce((d,u)=>{const h=ma(t,u,s);return d.top=he(h.top,d.top),d.right=Xe(h.right,d.right),d.bottom=Xe(h.bottom,d.bottom),d.left=he(h.left,d.left),d},ma(t,a,s));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function Ab(e){const{width:t,height:n}=nu(e);return{width:t,height:n}}function Pb(e,t,n){const r=Ie(t),s=ke(t),o=n==="fixed",i=at(e,!0,o,t);let a={scrollLeft:0,scrollTop:0};const c=je(0);function d(){c.x=So(s)}if(r||!r&&!o)if((Dt(t)!=="body"||sn(s))&&(a=Qn(t)),r){const m=at(t,!0,o,t);c.x=m.x+t.clientLeft,c.y=m.y+t.clientTop}else s&&d();o&&!r&&s&&d();const u=s&&!r&&!o?su(s,a):je(0),h=i.left+a.scrollLeft-c.x-u.x,p=i.top+a.scrollTop-c.y-u.y;return{x:h,y:p,width:i.width,height:i.height}}function Or(e){return Se(e).position==="static"}function ga(e,t){if(!Ie(e)||Se(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ke(e)===n&&(n=n.ownerDocument.body),n}function iu(e,t){const n=me(e);if(Jn(e))return n;if(!Ie(e)){let s=qe(e);for(;s&&!Tt(s);){if(Ce(s)&&!Or(s))return s;s=qe(s)}return n}let r=ga(e,t);for(;r&&cb(r)&&Or(r);)r=ga(r,t);return r&&Tt(r)&&Or(r)&&!bo(r)?n:r||hb(e)||n}const Eb=async function(e){const t=this.getOffsetParent||iu,n=this.getDimensions,r=await n(e.floating);return{reference:Pb(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Rb(e){return Se(e).direction==="rtl"}const Mb={convertOffsetParentRelativeRectToViewportRelativeRect:vb,getDocumentElement:ke,getClippingRect:Tb,getOffsetParent:iu,getElementRects:Eb,getClientRects:yb,getDimensions:Ab,getScale:bt,isElement:Ce,isRTL:Rb};function au(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Db(e,t){let n=null,r;const s=ke(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,c){a===void 0&&(a=!1),c===void 0&&(c=1),o();const d=e.getBoundingClientRect(),{left:u,top:h,width:p,height:m}=d;if(a||t(),!p||!m)return;const g=xn(h),v=xn(s.clientWidth-(u+p)),y=xn(s.clientHeight-(h+m)),x=xn(u),w={rootMargin:-g+"px "+-v+"px "+-y+"px "+-x+"px",threshold:he(0,Xe(1,c))||1};let C=!0;function S(A){const T=A[0].intersectionRatio;if(T!==c){if(!C)return i();T?i(!1,T):r=setTimeout(()=>{i(!1,1e-7)},1e3)}T===1&&!au(d,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(S,{...w,root:s.ownerDocument})}catch{n=new IntersectionObserver(S,w)}n.observe(e)}return i(!0),o}function Nb(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:c=!1}=r,d=Co(e),u=s||o?[...d?Yt(d):[],...Yt(t)]:[];u.forEach(x=>{s&&x.addEventListener("scroll",n,{passive:!0}),o&&x.addEventListener("resize",n)});const h=d&&a?Db(d,n):null;let p=-1,m=null;i&&(m=new ResizeObserver(x=>{let[b]=x;b&&b.target===d&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var w;(w=m)==null||w.observe(t)})),n()}),d&&!c&&m.observe(d),m.observe(t));let g,v=c?at(e):null;c&&y();function y(){const x=at(e);v&&!au(v,x)&&n(),v=x,g=requestAnimationFrame(y)}return n(),()=>{var x;u.forEach(b=>{s&&b.removeEventListener("scroll",n),o&&b.removeEventListener("resize",n)}),h?.(),(x=m)==null||x.disconnect(),m=null,c&&cancelAnimationFrame(g)}}const jb=nb,Ib=rb,kb=Qx,Ob=ob,Lb=eb,va=Jx,_b=sb,Vb=(e,t,n)=>{const r=new Map,s={platform:Mb,...n},o={...s.platform,_c:r};return Zx(e,t,{...s,platform:o})};var Fb=typeof document<"u",Bb=function(){},En=Fb?f.useLayoutEffect:Bb;function Fn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Fn(e[r],t[r]))return!1;return!0}if(s=Object.keys(e),n=s.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,s[r]))return!1;for(r=n;r--!==0;){const o=s[r];if(!(o==="_owner"&&e.$$typeof)&&!Fn(e[o],t[o]))return!1}return!0}return e!==e&&t!==t}function cu(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ya(e,t){const n=cu(e);return Math.round(t*n)/n}function Lr(e){const t=f.useRef(e);return En(()=>{t.current=e}),t}function $b(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:o,floating:i}={},transform:a=!0,whileElementsMounted:c,open:d}=e,[u,h]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=f.useState(r);Fn(p,r)||m(r);const[g,v]=f.useState(null),[y,x]=f.useState(null),b=f.useCallback(I=>{I!==A.current&&(A.current=I,v(I))},[]),w=f.useCallback(I=>{I!==T.current&&(T.current=I,x(I))},[]),C=o||g,S=i||y,A=f.useRef(null),T=f.useRef(null),E=f.useRef(u),P=c!=null,D=Lr(c),O=Lr(s),W=Lr(d),H=f.useCallback(()=>{if(!A.current||!T.current)return;const I={placement:t,strategy:n,middleware:p};O.current&&(I.platform=O.current),Vb(A.current,T.current,I).then(M=>{const k={...M,isPositioned:W.current!==!1};z.current&&!Fn(E.current,k)&&(E.current=k,pf.flushSync(()=>{h(k)}))})},[p,t,n,O,W]);En(()=>{d===!1&&E.current.isPositioned&&(E.current.isPositioned=!1,h(I=>({...I,isPositioned:!1})))},[d]);const z=f.useRef(!1);En(()=>(z.current=!0,()=>{z.current=!1}),[]),En(()=>{if(C&&(A.current=C),S&&(T.current=S),C&&S){if(D.current)return D.current(C,S,H);H()}},[C,S,H,D,P]);const G=f.useMemo(()=>({reference:A,floating:T,setReference:b,setFloating:w}),[b,w]),U=f.useMemo(()=>({reference:C,floating:S}),[C,S]),L=f.useMemo(()=>{const I={position:n,left:0,top:0};if(!U.floating)return I;const M=ya(U.floating,u.x),k=ya(U.floating,u.y);return a?{...I,transform:"translate("+M+"px, "+k+"px)",...cu(U.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:M,top:k}},[n,a,U.floating,u.x,u.y]);return f.useMemo(()=>({...u,update:H,refs:G,elements:U,floatingStyles:L}),[u,H,G,U,L])}const Ub=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:s}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?va({element:r.current,padding:s}).fn(n):{}:r?va({element:r,padding:s}).fn(n):{}}}},zb=(e,t)=>({...jb(e),options:[e,t]}),Wb=(e,t)=>({...Ib(e),options:[e,t]}),Hb=(e,t)=>({..._b(e),options:[e,t]}),Kb=(e,t)=>({...kb(e),options:[e,t]}),Gb=(e,t)=>({...Ob(e),options:[e,t]}),Yb=(e,t)=>({...Lb(e),options:[e,t]}),Xb=(e,t)=>({...Ub(e),options:[e,t]});var qb="Arrow",lu=f.forwardRef((e,t)=>{const{children:n,width:r=10,height:s=5,...o}=e;return l.jsx(J.svg,{...o,ref:t,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:l.jsx("polygon",{points:"0,0 30,0 15,10"})})});lu.displayName=qb;var Zb=lu;function Jb(e){const[t,n]=f.useState(void 0);return Ke(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const o=s[0];let i,a;if("borderBoxSize"in o){const c=o.borderBoxSize,d=Array.isArray(c)?c[0]:c;i=d.inlineSize,a=d.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var To="Popper",[uu,er]=Ze(To),[Qb,du]=uu(To),fu=e=>{const{__scopePopper:t,children:n}=e,[r,s]=f.useState(null);return l.jsx(Qb,{scope:t,anchor:r,onAnchorChange:s,children:n})};fu.displayName=To;var hu="PopperAnchor",pu=f.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...s}=e,o=du(hu,n),i=f.useRef(null),a=ee(t,i);return f.useEffect(()=>{o.onAnchorChange(r?.current||i.current)}),r?null:l.jsx(J.div,{...s,ref:a})});pu.displayName=hu;var Ao="PopperContent",[ew,tw]=uu(Ao),mu=f.forwardRef((e,t)=>{const{__scopePopper:n,side:r="bottom",sideOffset:s=0,align:o="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:c=!0,collisionBoundary:d=[],collisionPadding:u=0,sticky:h="partial",hideWhenDetached:p=!1,updatePositionStrategy:m="optimized",onPlaced:g,...v}=e,y=du(Ao,n),[x,b]=f.useState(null),w=ee(t,V=>b(V)),[C,S]=f.useState(null),A=Jb(C),T=A?.width??0,E=A?.height??0,P=r+(o!=="center"?"-"+o:""),D=typeof u=="number"?u:{top:0,right:0,bottom:0,left:0,...u},O=Array.isArray(d)?d:[d],W=O.length>0,H={padding:D,boundary:O.filter(rw),altBoundary:W},{refs:z,floatingStyles:G,placement:U,isPositioned:L,middlewareData:I}=$b({strategy:"fixed",placement:P,whileElementsMounted:(...V)=>Nb(...V,{animationFrame:m==="always"}),elements:{reference:y.anchor},middleware:[zb({mainAxis:s+E,alignmentAxis:i}),c&&Wb({mainAxis:!0,crossAxis:!1,limiter:h==="partial"?Hb():void 0,...H}),c&&Kb({...H}),Gb({...H,apply:({elements:V,rects:K,availableWidth:ae,availableHeight:Ue})=>{const{width:Nt,height:cr}=K.reference,ln=V.floating.style;ln.setProperty("--radix-popper-available-width",`${ae}px`),ln.setProperty("--radix-popper-available-height",`${Ue}px`),ln.setProperty("--radix-popper-anchor-width",`${Nt}px`),ln.setProperty("--radix-popper-anchor-height",`${cr}px`)}}),C&&Xb({element:C,padding:a}),sw({arrowWidth:T,arrowHeight:E}),p&&Yb({strategy:"referenceHidden",...H})]}),[M,k]=yu(U),$=Oe(g);Ke(()=>{L&&$?.()},[L,$]);const se=I.arrow?.x,ge=I.arrow?.y,R=I.arrow?.centerOffset!==0,[N,_]=f.useState();return Ke(()=>{x&&_(window.getComputedStyle(x).zIndex)},[x]),l.jsx("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:L?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:N,"--radix-popper-transform-origin":[I.transformOrigin?.x,I.transformOrigin?.y].join(" "),...I.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:l.jsx(ew,{scope:n,placedSide:M,onArrowChange:S,arrowX:se,arrowY:ge,shouldHideArrow:R,children:l.jsx(J.div,{"data-side":M,"data-align":k,...v,ref:w,style:{...v.style,animation:L?void 0:"none"}})})})});mu.displayName=Ao;var gu="PopperArrow",nw={top:"bottom",right:"left",bottom:"top",left:"right"},vu=f.forwardRef(function(t,n){const{__scopePopper:r,...s}=t,o=tw(gu,r),i=nw[o.placedSide];return l.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:l.jsx(Zb,{...s,ref:n,style:{...s.style,display:"block"}})})});vu.displayName=gu;function rw(e){return e!==null}var sw=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:s}=t,i=s.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,c=i?0:e.arrowHeight,[d,u]=yu(n),h={start:"0%",center:"50%",end:"100%"}[u],p=(s.arrow?.x??0)+a/2,m=(s.arrow?.y??0)+c/2;let g="",v="";return d==="bottom"?(g=i?h:`${p}px`,v=`${-c}px`):d==="top"?(g=i?h:`${p}px`,v=`${r.floating.height+c}px`):d==="right"?(g=`${-c}px`,v=i?h:`${m}px`):d==="left"&&(g=`${r.floating.width+c}px`,v=i?h:`${m}px`),{data:{x:g,y:v}}}});function yu(e){const[t,n="center"]=e.split("-");return[t,n]}var xu=fu,bu=pu,wu=mu,Cu=vu,ow=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),iw="VisuallyHidden",Su=f.forwardRef((e,t)=>l.jsx(J.span,{...e,ref:t,style:{...ow,...e.style}}));Su.displayName=iw;var aw=Su,[tr,SS]=Ze("Tooltip",[er]),nr=er(),Tu="TooltipProvider",cw=700,hs="tooltip.open",[lw,Po]=tr(Tu),Au=e=>{const{__scopeTooltip:t,delayDuration:n=cw,skipDelayDuration:r=300,disableHoverableContent:s=!1,children:o}=e,i=f.useRef(!0),a=f.useRef(!1),c=f.useRef(0);return f.useEffect(()=>{const d=c.current;return()=>window.clearTimeout(d)},[]),l.jsx(lw,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:f.useCallback(()=>{window.clearTimeout(c.current),i.current=!1},[]),onClose:f.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:f.useCallback(d=>{a.current=d},[]),disableHoverableContent:s,children:o})};Au.displayName=Tu;var Xt="Tooltip",[uw,rr]=tr(Xt),Pu=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:s,onOpenChange:o,disableHoverableContent:i,delayDuration:a}=e,c=Po(Xt,e.__scopeTooltip),d=nr(t),[u,h]=f.useState(null),p=ot(),m=f.useRef(0),g=i??c.disableHoverableContent,v=a??c.delayDuration,y=f.useRef(!1),[x,b]=Gn({prop:r,defaultProp:s??!1,onChange:T=>{T?(c.onOpen(),document.dispatchEvent(new CustomEvent(hs))):c.onClose(),o?.(T)},caller:Xt}),w=f.useMemo(()=>x?y.current?"delayed-open":"instant-open":"closed",[x]),C=f.useCallback(()=>{window.clearTimeout(m.current),m.current=0,y.current=!1,b(!0)},[b]),S=f.useCallback(()=>{window.clearTimeout(m.current),m.current=0,b(!1)},[b]),A=f.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{y.current=!0,b(!0),m.current=0},v)},[v,b]);return f.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),l.jsx(xu,{...d,children:l.jsx(uw,{scope:t,contentId:p,open:x,stateAttribute:w,trigger:u,onTriggerChange:h,onTriggerEnter:f.useCallback(()=>{c.isOpenDelayedRef.current?A():C()},[c.isOpenDelayedRef,A,C]),onTriggerLeave:f.useCallback(()=>{g?S():(window.clearTimeout(m.current),m.current=0)},[S,g]),onOpen:C,onClose:S,disableHoverableContent:g,children:n})})};Pu.displayName=Xt;var ps="TooltipTrigger",Eu=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=rr(ps,n),o=Po(ps,n),i=nr(n),a=f.useRef(null),c=ee(t,a,s.onTriggerChange),d=f.useRef(!1),u=f.useRef(!1),h=f.useCallback(()=>d.current=!1,[]);return f.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),l.jsx(bu,{asChild:!0,...i,children:l.jsx(J.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...r,ref:c,onPointerMove:B(e.onPointerMove,p=>{p.pointerType!=="touch"&&!u.current&&!o.isPointerInTransitRef.current&&(s.onTriggerEnter(),u.current=!0)}),onPointerLeave:B(e.onPointerLeave,()=>{s.onTriggerLeave(),u.current=!1}),onPointerDown:B(e.onPointerDown,()=>{s.open&&s.onClose(),d.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:B(e.onFocus,()=>{d.current||s.onOpen()}),onBlur:B(e.onBlur,s.onClose),onClick:B(e.onClick,s.onClose)})})});Eu.displayName=ps;var dw="TooltipPortal",[TS,fw]=tr(dw,{forceMount:void 0}),At="TooltipContent",Ru=f.forwardRef((e,t)=>{const n=fw(At,e.__scopeTooltip),{forceMount:r=n.forceMount,side:s="top",...o}=e,i=rr(At,e.__scopeTooltip);return l.jsx(Be,{present:r||i.open,children:i.disableHoverableContent?l.jsx(Mu,{side:s,...o,ref:t}):l.jsx(hw,{side:s,...o,ref:t})})}),hw=f.forwardRef((e,t)=>{const n=rr(At,e.__scopeTooltip),r=Po(At,e.__scopeTooltip),s=f.useRef(null),o=ee(t,s),[i,a]=f.useState(null),{trigger:c,onClose:d}=n,u=s.current,{onPointerInTransitChange:h}=r,p=f.useCallback(()=>{a(null),h(!1)},[h]),m=f.useCallback((g,v)=>{const y=g.currentTarget,x={x:g.clientX,y:g.clientY},b=yw(x,y.getBoundingClientRect()),w=xw(x,b),C=bw(v.getBoundingClientRect()),S=Cw([...w,...C]);a(S),h(!0)},[h]);return f.useEffect(()=>()=>p(),[p]),f.useEffect(()=>{if(c&&u){const g=y=>m(y,u),v=y=>m(y,c);return c.addEventListener("pointerleave",g),u.addEventListener("pointerleave",v),()=>{c.removeEventListener("pointerleave",g),u.removeEventListener("pointerleave",v)}}},[c,u,m,p]),f.useEffect(()=>{if(i){const g=v=>{const y=v.target,x={x:v.clientX,y:v.clientY},b=c?.contains(y)||u?.contains(y),w=!ww(x,i);b?p():w&&(p(),d())};return document.addEventListener("pointermove",g),()=>document.removeEventListener("pointermove",g)}},[c,u,i,d,p]),l.jsx(Mu,{...e,ref:o})}),[pw,mw]=tr(Xt,{isInside:!1}),gw=Na("TooltipContent"),Mu=f.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":s,onEscapeKeyDown:o,onPointerDownOutside:i,...a}=e,c=rr(At,n),d=nr(n),{onClose:u}=c;return f.useEffect(()=>(document.addEventListener(hs,u),()=>document.removeEventListener(hs,u)),[u]),f.useEffect(()=>{if(c.trigger){const h=p=>{p.target?.contains(c.trigger)&&u()};return window.addEventListener("scroll",h,{capture:!0}),()=>window.removeEventListener("scroll",h,{capture:!0})}},[c.trigger,u]),l.jsx(Yn,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:h=>h.preventDefault(),onDismiss:u,children:l.jsxs(wu,{"data-state":c.stateAttribute,...d,...a,ref:t,style:{...a.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[l.jsx(gw,{children:r}),l.jsx(pw,{scope:n,isInside:!0,children:l.jsx(aw,{id:c.contentId,role:"tooltip",children:s||r})})]})})});Ru.displayName=At;var Du="TooltipArrow",vw=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,s=nr(n);return mw(Du,n).isInside?null:l.jsx(Cu,{...s,...r,ref:t})});vw.displayName=Du;function yw(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,s,o)){case o:return"left";case s:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function xw(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function bw(e){const{top:t,right:n,bottom:r,left:s}=e;return[{x:s,y:t},{x:n,y:t},{x:n,y:r},{x:s,y:r}]}function ww(e,t){const{x:n,y:r}=e;let s=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const a=t[o],c=t[i],d=a.x,u=a.y,h=c.x,p=c.y;u>r!=p>r&&n<(h-d)*(r-u)/(p-u)+d&&(s=!s)}return s}function Cw(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Sw(t)}function Sw(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const s=e[r];for(;t.length>=2;){const o=t[t.length-1],i=t[t.length-2];if((o.x-i.x)*(s.y-i.y)>=(o.y-i.y)*(s.x-i.x))t.pop();else break}t.push(s)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const s=e[r];for(;n.length>=2;){const o=n[n.length-1],i=n[n.length-2];if((o.x-i.x)*(s.y-i.y)>=(o.y-i.y)*(s.x-i.x))n.pop();else break}n.push(s)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Tw=Au,Aw=Pu,Pw=Eu,Nu=Ru;const Ew=Tw,Rw=Aw,Mw=Pw,ju=f.forwardRef(({className:e,sideOffset:t=4,...n},r)=>l.jsx(Nu,{ref:r,sideOffset:t,className:j("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));ju.displayName=Nu.displayName;const Dw="sidebar_state",Nw=3600*24*7,jw="20rem",Iw="22rem",kw="3rem",Ow="b",Iu=f.createContext(null);function sr(){const e=f.useContext(Iu);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}const ku=f.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:s,children:o,...i},a)=>{const c=py(),[d,u]=f.useState(!1),[h,p]=f.useState(e),m=t??h,g=f.useCallback(b=>{const w=typeof b=="function"?b(m):b;n?n(w):p(w),document.cookie=`${Dw}=${w}; path=/; max-age=${Nw}`},[n,m]),v=f.useCallback(()=>c?u(b=>!b):g(b=>!b),[c,g,u]);f.useEffect(()=>{const b=w=>{w.key===Ow&&(w.metaKey||w.ctrlKey)&&(w.preventDefault(),v())};return window.addEventListener("keydown",b),()=>window.removeEventListener("keydown",b)},[v]);const y=m?"expanded":"collapsed",x=f.useMemo(()=>({state:y,open:m,setOpen:g,isMobile:c,openMobile:d,setOpenMobile:u,toggleSidebar:v}),[y,m,g,c,d,u,v]);return l.jsx(Iu.Provider,{value:x,children:l.jsx(Ew,{delayDuration:0,children:l.jsx("div",{style:{"--sidebar-width":jw,"--sidebar-width-icon":kw,...s},className:j("group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",r),ref:a,...i,children:o})})})});ku.displayName="SidebarProvider";const Lw=f.forwardRef(({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:s,...o},i)=>{const{isMobile:a,state:c,openMobile:d,setOpenMobile:u}=sr();return n==="none"?l.jsx("div",{className:j("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",r),ref:i,...o,children:s}):a?l.jsx(Ox,{open:d,onOpenChange:u,...o,children:l.jsx(Zl,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":Iw},side:e,children:l.jsx("div",{className:"flex h-full w-full flex-col",children:s})})}):l.jsxs("div",{ref:i,className:"group peer hidden md:block text-sidebar-foreground","data-state":c,"data-collapsible":c==="collapsed"?n:"","data-variant":t,"data-side":e,children:[l.jsx("div",{className:j("duration-200 relative h-svh w-[--sidebar-width] bg-transparent transition-[width] ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),l.jsx("div",{className:j("duration-200 fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...o,children:l.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:s})})]})});Lw.displayName="Sidebar";const _w=f.forwardRef(({className:e,onClick:t,...n},r)=>{const{toggleSidebar:s}=sr();return l.jsxs(ie,{ref:r,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:j("h-7 w-7",e),onClick:o=>{t?.(o),s()},...n,children:[l.jsx(Qf,{}),l.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});_w.displayName="SidebarTrigger";const Vw=f.forwardRef(({className:e,...t},n)=>{const{toggleSidebar:r}=sr();return l.jsx("button",{ref:n,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:j("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})});Vw.displayName="SidebarRail";const Fw=f.forwardRef(({className:e,...t},n)=>l.jsx("main",{ref:n,className:j("relative flex min-h-svh flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",e),...t}));Fw.displayName="SidebarInset";const Bw=f.forwardRef(({className:e,...t},n)=>l.jsx(so,{ref:n,"data-sidebar":"input",className:j("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t}));Bw.displayName="SidebarInput";const $w=f.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,"data-sidebar":"header",className:j("flex flex-col gap-2 p-2",e),...t}));$w.displayName="SidebarHeader";const Uw=f.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,"data-sidebar":"footer",className:j("flex flex-col gap-2 p-2",e),...t}));Uw.displayName="SidebarFooter";const zw=f.forwardRef(({className:e,...t},n)=>l.jsx(xf,{ref:n,"data-sidebar":"separator",className:j("mx-2 w-auto bg-sidebar-border",e),...t}));zw.displayName="SidebarSeparator";const Ww=f.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,"data-sidebar":"content",className:j("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));Ww.displayName="SidebarContent";const Hw=f.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,"data-sidebar":"group",className:j("relative flex w-full min-w-0 flex-col p-2",e),...t}));Hw.displayName="SidebarGroup";const Kw=f.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const s=t?Jt:"div";return l.jsx(s,{ref:r,"data-sidebar":"group-label",className:j("duration-200 flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})});Kw.displayName="SidebarGroupLabel";const Gw=f.forwardRef(({className:e,asChild:t=!1,...n},r)=>{const s=t?Jt:"button";return l.jsx(s,{ref:r,"data-sidebar":"group-action",className:j("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",e),...n})});Gw.displayName="SidebarGroupAction";const Yw=f.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,"data-sidebar":"group-content",className:j("w-full text-sm",e),...t}));Yw.displayName="SidebarGroupContent";const Xw=f.forwardRef(({className:e,...t},n)=>l.jsx("ul",{ref:n,"data-sidebar":"menu",className:j("flex w-full min-w-0 flex-col gap-1",e),...t}));Xw.displayName="SidebarMenu";const qw=f.forwardRef(({className:e,...t},n)=>l.jsx("li",{ref:n,"data-sidebar":"menu-item",className:j("group/menu-item relative",e),...t}));qw.displayName="SidebarMenuItem";const Zw=ws("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!size-8"}},defaultVariants:{variant:"default",size:"default"}}),Jw=f.forwardRef(({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:s,className:o,...i},a)=>{const c=e?Jt:"button",{isMobile:d,state:u}=sr(),h=l.jsx(c,{ref:a,"data-sidebar":"menu-button","data-size":r,"data-active":t,className:j(Zw({variant:n,size:r}),o),...i});return s?(typeof s=="string"&&(s={children:s}),l.jsxs(Rw,{children:[l.jsx(Mw,{asChild:!0,children:h}),l.jsx(ju,{side:"right",align:"center",hidden:u!=="collapsed"||d,...s})]})):h});Jw.displayName="SidebarMenuButton";const Qw=f.forwardRef(({className:e,asChild:t=!1,showOnHover:n=!1,...r},s)=>{const o=t?Jt:"button";return l.jsx(o,{ref:s,"data-sidebar":"menu-action",className:j("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",n&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",e),...r})});Qw.displayName="SidebarMenuAction";const e0=f.forwardRef(({className:e,...t},n)=>l.jsx("div",{ref:n,"data-sidebar":"menu-badge",className:j("absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground select-none pointer-events-none","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t}));e0.displayName="SidebarMenuBadge";const t0=f.forwardRef(({className:e,showIcon:t=!1,...n},r)=>{const s=f.useMemo(()=>`${Math.floor(Math.random()*40)+50}%`,[]);return l.jsxs("div",{ref:r,"data-sidebar":"menu-skeleton",className:j("rounded-md h-8 flex gap-2 px-2 items-center",e),...n,children:[t&&l.jsx(ca,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),l.jsx(ca,{className:"h-4 flex-1 max-w-[--skeleton-width]","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":s}})]})});t0.displayName="SidebarMenuSkeleton";const n0=f.forwardRef(({className:e,...t},n)=>l.jsx("ul",{ref:n,"data-sidebar":"menu-sub",className:j("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t}));n0.displayName="SidebarMenuSub";const r0=f.forwardRef(({...e},t)=>l.jsx("li",{ref:t,...e}));r0.displayName="SidebarMenuSubItem";const s0=f.forwardRef(({asChild:e=!1,size:t="md",isActive:n,className:r,...s},o)=>{const i=e?Jt:"a";return l.jsx(i,{ref:o,"data-sidebar":"menu-sub-button","data-size":t,"data-active":n,className:j("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground",t==="sm"&&"text-xs",t==="md"&&"text-sm","group-data-[collapsible=icon]:hidden",r),...s})});s0.displayName="SidebarMenuSubButton";var Ou="AlertDialog",[o0,AS]=Ze(Ou,[jl]),$e=jl(),Lu=e=>{const{__scopeAlertDialog:t,...n}=e,r=$e(t);return l.jsx(Yl,{...r,...n,modal:!0})};Lu.displayName=Ou;var i0="AlertDialogTrigger",_u=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=$e(n);return l.jsx(kx,{...s,...r,ref:t})});_u.displayName=i0;var a0="AlertDialogPortal",Vu=e=>{const{__scopeAlertDialog:t,...n}=e,r=$e(t);return l.jsx(Xl,{...r,...n})};Vu.displayName=a0;var c0="AlertDialogOverlay",Fu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=$e(n);return l.jsx(fo,{...s,...r,ref:t})});Fu.displayName=c0;var wt="AlertDialogContent",[l0,u0]=o0(wt),d0=Na("AlertDialogContent"),Bu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:r,...s}=e,o=$e(n),i=f.useRef(null),a=ee(t,i),c=f.useRef(null);return l.jsx(Dx,{contentName:wt,titleName:$u,docsSlug:"alert-dialog",children:l.jsx(l0,{scope:n,cancelRef:c,children:l.jsxs(ho,{role:"alertdialog",...o,...s,ref:a,onOpenAutoFocus:B(s.onOpenAutoFocus,d=>{d.preventDefault(),c.current?.focus({preventScroll:!0})}),onPointerDownOutside:d=>d.preventDefault(),onInteractOutside:d=>d.preventDefault(),children:[l.jsx(d0,{children:r}),l.jsx(h0,{contentRef:i})]})})})});Bu.displayName=wt;var $u="AlertDialogTitle",Uu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=$e(n);return l.jsx(po,{...s,...r,ref:t})});Uu.displayName=$u;var zu="AlertDialogDescription",Wu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=$e(n);return l.jsx(mo,{...s,...r,ref:t})});Wu.displayName=zu;var f0="AlertDialogAction",Hu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,s=$e(n);return l.jsx(go,{...s,...r,ref:t})});Hu.displayName=f0;var Ku="AlertDialogCancel",Gu=f.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:s}=u0(Ku,n),o=$e(n),i=ee(t,s);return l.jsx(go,{...o,...r,ref:i})});Gu.displayName=Ku;var h0=({contentRef:e})=>{const t=`\`${wt}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${wt}\` by passing a \`${zu}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${wt}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return f.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},p0=Lu,m0=_u,g0=Vu,Yu=Fu,Xu=Bu,qu=Hu,Zu=Gu,Ju=Uu,Qu=Wu;const v0=p0,y0=m0,x0=g0,ed=f.forwardRef(({className:e,...t},n)=>l.jsx(Yu,{className:j("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));ed.displayName=Yu.displayName;const td=f.forwardRef(({className:e,...t},n)=>l.jsxs(x0,{children:[l.jsx(ed,{}),l.jsx(Xu,{ref:n,className:j("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t})]}));td.displayName=Xu.displayName;const nd=({className:e,...t})=>l.jsx("div",{className:j("flex flex-col space-y-2 text-center sm:text-left",e),...t});nd.displayName="AlertDialogHeader";const rd=({className:e,...t})=>l.jsx("div",{className:j("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});rd.displayName="AlertDialogFooter";const sd=f.forwardRef(({className:e,...t},n)=>l.jsx(Ju,{ref:n,className:j("text-lg font-semibold",e),...t}));sd.displayName=Ju.displayName;const od=f.forwardRef(({className:e,...t},n)=>l.jsx(Qu,{ref:n,className:j("text-sm text-muted-foreground",e),...t}));od.displayName=Qu.displayName;const id=f.forwardRef(({className:e,...t},n)=>l.jsx(qu,{ref:n,className:j(ja(),e),...t}));id.displayName=qu.displayName;const ad=f.forwardRef(({className:e,...t},n)=>l.jsx(Zu,{ref:n,className:j(ja({variant:"outline"}),"mt-2 sm:mt-0",e),...t}));ad.displayName=Zu.displayName;function b0({children:e,isCollapsed:t,onToggle:n,currentMode:r="无忧问答"}){const{conversations:s,currentConversationId:o,createConversation:i,deleteConversation:a,updateConversationTitle:c,setCurrentConversation:d,loadUserConversations:u,getConversationsByMode:h,error:p}=as(),m=h(r),{user:g}=Pe(),[v,y]=f.useState(null),[x,b]=f.useState(""),[w,C]=f.useState(!1),[S,A]=f.useState(!1),[T,E]=f.useState(!1),[P,D]=f.useState(new Set),O=t!==void 0?t:w,W=()=>{d(null),window.location.reload()},H=async k=>{D($=>new Set($).add(k));try{await a(k),D($=>{const se=new Set($);return se.delete(k),se})}catch($){console.error("删除对话失败:",$),D(se=>{const ge=new Set(se);return ge.delete(k),ge})}},z=(k,$,se)=>{se.stopPropagation(),y(k),b($)},G=async k=>{if(x.trim())try{await c(k,x.trim())}catch($){console.error("更新标题失败:",$)}y(null),b("")},U=()=>{y(null),b("")},L=()=>{O?(n?n():C(!1),setTimeout(()=>{A(!1)},150)):(A(!0),setTimeout(()=>{n?n():C(!0)},150))};f.useEffect(()=>{O?A(!0):setTimeout(()=>{A(!1)},300)},[O]),f.useEffect(()=>{const k=()=>{E(window.innerWidth<768)};return k(),window.addEventListener("resize",k),()=>window.removeEventListener("resize",k)},[]),f.useEffect(()=>{T&&!w&&t===void 0&&C(!0)},[T]),f.useEffect(()=>{g&&u()},[g,u]),f.useEffect(()=>{const k=$=>{$.key==="b"&&($.ctrlKey||$.metaKey)&&($.preventDefault(),L())};return window.addEventListener("keydown",k),()=>window.removeEventListener("keydown",k)},[O]);const I=()=>{T&&!O&&L()},M=k=>{const $=k instanceof Date?k:new Date(k);if(isNaN($.getTime()))return"无效日期";const ge=new Date().getTime()-$.getTime(),R=Math.floor(ge/(1e3*60*60*24));return R===0?"今天":R===1?"昨天":R<7?`${R}天前`:$.toLocaleDateString("zh-CN")};return l.jsxs(ku,{defaultOpen:!0,children:[T&&!O&&l.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 md:hidden",onClick:I}),T&&l.jsx(ie,{variant:"outline",size:"sm",className:"fixed top-4 left-4 z-50 md:hidden h-10 w-10 p-0",onClick:L,"aria-label":O?"展开侧边栏":"收起侧边栏",children:l.jsx(Hf,{className:"h-4 w-4"})}),l.jsxs("div",{className:j("flex min-h-screen w-full",T?"relative":""),children:[l.jsx("div",{className:j("bg-sidebar border-r border-border transition-all duration-300 ease-in-out z-50",!T&&"flex-shrink-0",!T&&(O?"w-0 overflow-hidden":"w-80"),T&&"fixed top-0 left-0 h-full",T&&(O?"-translate-x-full":"translate-x-0 w-80")),children:l.jsxs("div",{className:"h-full flex flex-col",children:[l.jsxs("div",{className:j("border-b px-4 h-16 py-3 transition-opacity duration-150 ease-in-out",S?"opacity-0":"opacity-100"),children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx("h2",{className:"text-lg font-semibold",children:"对话历史"}),l.jsx(ie,{onClick:W,size:"sm",variant:"outline",className:"h-8 w-8 p-0",disabled:!g,children:l.jsx(oh,{className:"h-4 w-4"})})]}),p&&l.jsx("div",{className:"mt-2 text-xs text-red-500 bg-red-50 p-2 rounded",children:p})]}),l.jsx("div",{className:j("flex-1 overflow-auto transition-opacity duration-150 ease-in-out",S?"opacity-0":"opacity-100"),children:l.jsx("div",{className:"p-2",children:g?m.length===0?l.jsxs("div",{className:"px-4 py-8 text-center text-sm text-muted-foreground",children:["暂无 ",r," 对话历史"]}):l.jsx("div",{className:"space-y-1",children:m.filter(k=>!P.has(k.id)).map(k=>l.jsxs("div",{className:j("group relative flex items-center gap-3 rounded-lg px-3 py-2 text-sm cursor-pointer hover:bg-accent transition-colors",o===k.id&&"bg-accent"),onClick:()=>d(k.id),children:[l.jsx(Gf,{className:"h-4 w-4 flex-shrink-0"}),l.jsx("div",{className:"flex-1 min-w-0",children:v===k.id?l.jsxs("div",{className:"flex items-center gap-1",onClick:$=>$.stopPropagation(),children:[l.jsx(so,{value:x,onChange:$=>b($.target.value),className:"h-6 text-xs",onKeyDown:$=>{$.key==="Enter"?G(k.id):$.key==="Escape"&&U()},autoFocus:!0}),l.jsx(ie,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:()=>G(k.id),children:l.jsx(ka,{className:"h-3 w-3"})}),l.jsx(ie,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:U,children:l.jsx(Ss,{className:"h-3 w-3"})})]}):l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"font-medium truncate",children:k.title}),l.jsxs("div",{className:"text-xs text-muted-foreground",children:[M(k.updatedAt)," •"," ",k.messages.length," 条消息"]})]})}),v!==k.id&&l.jsxs("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[l.jsx(ie,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:$=>z(k.id,k.title,$),children:l.jsx(rh,{className:"h-3 w-3"})}),l.jsxs(v0,{children:[l.jsx(y0,{asChild:!0,children:l.jsx(ie,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0 text-destructive hover:text-destructive",onClick:$=>$.stopPropagation(),children:l.jsx(ah,{className:"h-3 w-3"})})}),l.jsxs(td,{children:[l.jsxs(nd,{children:[l.jsx(sd,{children:"确认删除对话"}),l.jsxs(od,{children:['确定要删除对话 "',k.title,'" 吗？此操作不可撤销，对话中的所有消息都将被永久删除。']})]}),l.jsxs(rd,{children:[l.jsx(ad,{children:"取消"}),l.jsx(id,{onClick:()=>H(k.id),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"删除"})]})]})]})]})]},k.id))}):l.jsx("div",{className:"px-4 py-8 text-center text-sm text-muted-foreground",children:"请先登录"})})}),l.jsx("div",{className:j("border-t px-4 py-3 transition-opacity duration-150 ease-in-out",S?"opacity-0":"opacity-100"),children:l.jsxs("div",{className:"text-xs text-muted-foreground",children:["共 ",m.length," 个 ",r," 对话"]})})]})}),l.jsx("main",{className:j("flex-1 flex flex-col min-w-0 w-full relative",T&&"w-full"),children:e})]})]})}function cd(e){const t=e+"CollectionProvider",[n,r]=Ze(t),[s,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=v=>{const{scope:y,children:x}=v,b=Ae.useRef(null),w=Ae.useRef(new Map).current;return l.jsx(s,{scope:y,itemMap:w,collectionRef:b,children:x})};i.displayName=t;const a=e+"CollectionSlot",c=Rn(a),d=Ae.forwardRef((v,y)=>{const{scope:x,children:b}=v,w=o(a,x),C=ee(y,w.collectionRef);return l.jsx(c,{ref:C,children:b})});d.displayName=a;const u=e+"CollectionItemSlot",h="data-radix-collection-item",p=Rn(u),m=Ae.forwardRef((v,y)=>{const{scope:x,children:b,...w}=v,C=Ae.useRef(null),S=ee(y,C),A=o(u,x);return Ae.useEffect(()=>(A.itemMap.set(C,{ref:C,...w}),()=>void A.itemMap.delete(C))),l.jsx(p,{[h]:"",ref:S,children:b})});m.displayName=u;function g(v){const y=o(e+"CollectionConsumer",v);return Ae.useCallback(()=>{const b=y.collectionRef.current;if(!b)return[];const w=Array.from(b.querySelectorAll(`[${h}]`));return Array.from(y.itemMap.values()).sort((A,T)=>w.indexOf(A.ref.current)-w.indexOf(T.ref.current))},[y.collectionRef,y.itemMap])}return[{Provider:i,Slot:d,ItemSlot:m},g,r]}var w0=f.createContext(void 0);function ld(e){const t=f.useContext(w0);return e||t||"ltr"}var _r="rovingFocusGroup.onEntryFocus",C0={bubbles:!1,cancelable:!0},on="RovingFocusGroup",[ms,ud,S0]=cd(on),[T0,dd]=Ze(on,[S0]),[A0,P0]=T0(on),fd=f.forwardRef((e,t)=>l.jsx(ms.Provider,{scope:e.__scopeRovingFocusGroup,children:l.jsx(ms.Slot,{scope:e.__scopeRovingFocusGroup,children:l.jsx(E0,{...e,ref:t})})}));fd.displayName=on;var E0=f.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:s=!1,dir:o,currentTabStopId:i,defaultCurrentTabStopId:a,onCurrentTabStopIdChange:c,onEntryFocus:d,preventScrollOnEntryFocus:u=!1,...h}=e,p=f.useRef(null),m=ee(t,p),g=ld(o),[v,y]=Gn({prop:i,defaultProp:a??null,onChange:c,caller:on}),[x,b]=f.useState(!1),w=Oe(d),C=ud(n),S=f.useRef(!1),[A,T]=f.useState(0);return f.useEffect(()=>{const E=p.current;if(E)return E.addEventListener(_r,w),()=>E.removeEventListener(_r,w)},[w]),l.jsx(A0,{scope:n,orientation:r,dir:g,loop:s,currentTabStopId:v,onItemFocus:f.useCallback(E=>y(E),[y]),onItemShiftTab:f.useCallback(()=>b(!0),[]),onFocusableItemAdd:f.useCallback(()=>T(E=>E+1),[]),onFocusableItemRemove:f.useCallback(()=>T(E=>E-1),[]),children:l.jsx(J.div,{tabIndex:x||A===0?-1:0,"data-orientation":r,...h,ref:m,style:{outline:"none",...e.style},onMouseDown:B(e.onMouseDown,()=>{S.current=!0}),onFocus:B(e.onFocus,E=>{const P=!S.current;if(E.target===E.currentTarget&&P&&!x){const D=new CustomEvent(_r,C0);if(E.currentTarget.dispatchEvent(D),!D.defaultPrevented){const O=C().filter(U=>U.focusable),W=O.find(U=>U.active),H=O.find(U=>U.id===v),G=[W,H,...O].filter(Boolean).map(U=>U.ref.current);md(G,u)}}S.current=!1}),onBlur:B(e.onBlur,()=>b(!1))})})}),hd="RovingFocusGroupItem",pd=f.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:s=!1,tabStopId:o,children:i,...a}=e,c=ot(),d=o||c,u=P0(hd,n),h=u.currentTabStopId===d,p=ud(n),{onFocusableItemAdd:m,onFocusableItemRemove:g,currentTabStopId:v}=u;return f.useEffect(()=>{if(r)return m(),()=>g()},[r,m,g]),l.jsx(ms.ItemSlot,{scope:n,id:d,focusable:r,active:s,children:l.jsx(J.span,{tabIndex:h?0:-1,"data-orientation":u.orientation,...a,ref:t,onMouseDown:B(e.onMouseDown,y=>{r?u.onItemFocus(d):y.preventDefault()}),onFocus:B(e.onFocus,()=>u.onItemFocus(d)),onKeyDown:B(e.onKeyDown,y=>{if(y.key==="Tab"&&y.shiftKey){u.onItemShiftTab();return}if(y.target!==y.currentTarget)return;const x=D0(y,u.orientation,u.dir);if(x!==void 0){if(y.metaKey||y.ctrlKey||y.altKey||y.shiftKey)return;y.preventDefault();let w=p().filter(C=>C.focusable).map(C=>C.ref.current);if(x==="last")w.reverse();else if(x==="prev"||x==="next"){x==="prev"&&w.reverse();const C=w.indexOf(y.currentTarget);w=u.loop?N0(w,C+1):w.slice(C+1)}setTimeout(()=>md(w))}}),children:typeof i=="function"?i({isCurrentTabStop:h,hasTabStop:v!=null}):i})})});pd.displayName=hd;var R0={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M0(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function D0(e,t,n){const r=M0(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return R0[r]}function md(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function N0(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var j0=fd,I0=pd,gs=["Enter"," "],k0=["ArrowDown","PageUp","Home"],gd=["ArrowUp","PageDown","End"],O0=[...k0,...gd],L0={ltr:[...gs,"ArrowRight"],rtl:[...gs,"ArrowLeft"]},_0={ltr:["ArrowLeft"],rtl:["ArrowRight"]},an="Menu",[qt,V0,F0]=cd(an),[ct,vd]=Ze(an,[F0,er,dd]),or=er(),yd=dd(),[B0,lt]=ct(an),[$0,cn]=ct(an),xd=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:s,onOpenChange:o,modal:i=!0}=e,a=or(t),[c,d]=f.useState(null),u=f.useRef(!1),h=Oe(o),p=ld(s);return f.useEffect(()=>{const m=()=>{u.current=!0,document.addEventListener("pointerdown",g,{capture:!0,once:!0}),document.addEventListener("pointermove",g,{capture:!0,once:!0})},g=()=>u.current=!1;return document.addEventListener("keydown",m,{capture:!0}),()=>{document.removeEventListener("keydown",m,{capture:!0}),document.removeEventListener("pointerdown",g,{capture:!0}),document.removeEventListener("pointermove",g,{capture:!0})}},[]),l.jsx(xu,{...a,children:l.jsx(B0,{scope:t,open:n,onOpenChange:h,content:c,onContentChange:d,children:l.jsx($0,{scope:t,onClose:f.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:u,dir:p,modal:i,children:r})})})};xd.displayName=an;var U0="MenuAnchor",Eo=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,s=or(n);return l.jsx(bu,{...s,...r,ref:t})});Eo.displayName=U0;var Ro="MenuPortal",[z0,bd]=ct(Ro,{forceMount:void 0}),wd=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:s}=e,o=lt(Ro,t);return l.jsx(z0,{scope:t,forceMount:n,children:l.jsx(Be,{present:n||o.open,children:l.jsx(io,{asChild:!0,container:s,children:r})})})};wd.displayName=Ro;var be="MenuContent",[W0,Mo]=ct(be),Cd=f.forwardRef((e,t)=>{const n=bd(be,e.__scopeMenu),{forceMount:r=n.forceMount,...s}=e,o=lt(be,e.__scopeMenu),i=cn(be,e.__scopeMenu);return l.jsx(qt.Provider,{scope:e.__scopeMenu,children:l.jsx(Be,{present:r||o.open,children:l.jsx(qt.Slot,{scope:e.__scopeMenu,children:i.modal?l.jsx(H0,{...s,ref:t}):l.jsx(K0,{...s,ref:t})})})})}),H0=f.forwardRef((e,t)=>{const n=lt(be,e.__scopeMenu),r=f.useRef(null),s=ee(t,r);return f.useEffect(()=>{const o=r.current;if(o)return Dl(o)},[]),l.jsx(Do,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:B(e.onFocusOutside,o=>o.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),K0=f.forwardRef((e,t)=>{const n=lt(be,e.__scopeMenu);return l.jsx(Do,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),G0=Rn("MenuContent.ScrollLock"),Do=f.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:s,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEntryFocus:c,onEscapeKeyDown:d,onPointerDownOutside:u,onFocusOutside:h,onInteractOutside:p,onDismiss:m,disableOutsideScroll:g,...v}=e,y=lt(be,n),x=cn(be,n),b=or(n),w=yd(n),C=V0(n),[S,A]=f.useState(null),T=f.useRef(null),E=ee(t,T,y.onContentChange),P=f.useRef(0),D=f.useRef(""),O=f.useRef(0),W=f.useRef(null),H=f.useRef("right"),z=f.useRef(0),G=g?ao:f.Fragment,U=g?{as:G0,allowPinchZoom:!0}:void 0,L=M=>{const k=D.current+M,$=C().filter(V=>!V.disabled),se=document.activeElement,ge=$.find(V=>V.ref.current===se)?.textValue,R=$.map(V=>V.textValue),N=oC(R,k,ge),_=$.find(V=>V.textValue===N)?.ref.current;(function V(K){D.current=K,window.clearTimeout(P.current),K!==""&&(P.current=window.setTimeout(()=>V(""),1e3))})(k),_&&setTimeout(()=>_.focus())};f.useEffect(()=>()=>window.clearTimeout(P.current),[]),wl();const I=f.useCallback(M=>H.current===W.current?.side&&aC(M,W.current?.area),[]);return l.jsx(W0,{scope:n,searchRef:D,onItemEnter:f.useCallback(M=>{I(M)&&M.preventDefault()},[I]),onItemLeave:f.useCallback(M=>{I(M)||(T.current?.focus(),A(null))},[I]),onTriggerLeave:f.useCallback(M=>{I(M)&&M.preventDefault()},[I]),pointerGraceTimerRef:O,onPointerGraceIntentChange:f.useCallback(M=>{W.current=M},[]),children:l.jsx(G,{...U,children:l.jsx(oo,{asChild:!0,trapped:s,onMountAutoFocus:B(o,M=>{M.preventDefault(),T.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:l.jsx(Yn,{asChild:!0,disableOutsidePointerEvents:a,onEscapeKeyDown:d,onPointerDownOutside:u,onFocusOutside:h,onInteractOutside:p,onDismiss:m,children:l.jsx(j0,{asChild:!0,...w,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:S,onCurrentTabStopIdChange:A,onEntryFocus:B(c,M=>{x.isUsingKeyboardRef.current||M.preventDefault()}),preventScrollOnEntryFocus:!0,children:l.jsx(wu,{role:"menu","aria-orientation":"vertical","data-state":Vd(y.open),"data-radix-menu-content":"",dir:x.dir,...b,...v,ref:E,style:{outline:"none",...v.style},onKeyDown:B(v.onKeyDown,M=>{const $=M.target.closest("[data-radix-menu-content]")===M.currentTarget,se=M.ctrlKey||M.altKey||M.metaKey,ge=M.key.length===1;$&&(M.key==="Tab"&&M.preventDefault(),!se&&ge&&L(M.key));const R=T.current;if(M.target!==R||!O0.includes(M.key))return;M.preventDefault();const _=C().filter(V=>!V.disabled).map(V=>V.ref.current);gd.includes(M.key)&&_.reverse(),rC(_)}),onBlur:B(e.onBlur,M=>{M.currentTarget.contains(M.target)||(window.clearTimeout(P.current),D.current="")}),onPointerMove:B(e.onPointerMove,Zt(M=>{const k=M.target,$=z.current!==M.clientX;if(M.currentTarget.contains(k)&&$){const se=M.clientX>z.current?"right":"left";H.current=se,z.current=M.clientX}}))})})})})})})});Cd.displayName=be;var Y0="MenuGroup",No=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return l.jsx(J.div,{role:"group",...r,ref:t})});No.displayName=Y0;var X0="MenuLabel",Sd=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return l.jsx(J.div,{...r,ref:t})});Sd.displayName=X0;var Bn="MenuItem",xa="menu.itemSelect",ir=f.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...s}=e,o=f.useRef(null),i=cn(Bn,e.__scopeMenu),a=Mo(Bn,e.__scopeMenu),c=ee(t,o),d=f.useRef(!1),u=()=>{const h=o.current;if(!n&&h){const p=new CustomEvent(xa,{bubbles:!0,cancelable:!0});h.addEventListener(xa,m=>r?.(m),{once:!0}),Ea(h,p),p.defaultPrevented?d.current=!1:i.onClose()}};return l.jsx(Td,{...s,ref:c,disabled:n,onClick:B(e.onClick,u),onPointerDown:h=>{e.onPointerDown?.(h),d.current=!0},onPointerUp:B(e.onPointerUp,h=>{d.current||h.currentTarget?.click()}),onKeyDown:B(e.onKeyDown,h=>{const p=a.searchRef.current!=="";n||p&&h.key===" "||gs.includes(h.key)&&(h.currentTarget.click(),h.preventDefault())})})});ir.displayName=Bn;var Td=f.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:s,...o}=e,i=Mo(Bn,n),a=yd(n),c=f.useRef(null),d=ee(t,c),[u,h]=f.useState(!1),[p,m]=f.useState("");return f.useEffect(()=>{const g=c.current;g&&m((g.textContent??"").trim())},[o.children]),l.jsx(qt.ItemSlot,{scope:n,disabled:r,textValue:s??p,children:l.jsx(I0,{asChild:!0,...a,focusable:!r,children:l.jsx(J.div,{role:"menuitem","data-highlighted":u?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...o,ref:d,onPointerMove:B(e.onPointerMove,Zt(g=>{r?i.onItemLeave(g):(i.onItemEnter(g),g.defaultPrevented||g.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:B(e.onPointerLeave,Zt(g=>i.onItemLeave(g))),onFocus:B(e.onFocus,()=>h(!0)),onBlur:B(e.onBlur,()=>h(!1))})})})}),q0="MenuCheckboxItem",Ad=f.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...s}=e;return l.jsx(Dd,{scope:e.__scopeMenu,checked:n,children:l.jsx(ir,{role:"menuitemcheckbox","aria-checked":$n(n)?"mixed":n,...s,ref:t,"data-state":Io(n),onSelect:B(s.onSelect,()=>r?.($n(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Ad.displayName=q0;var Pd="MenuRadioGroup",[Z0,J0]=ct(Pd,{value:void 0,onValueChange:()=>{}}),Ed=f.forwardRef((e,t)=>{const{value:n,onValueChange:r,...s}=e,o=Oe(r);return l.jsx(Z0,{scope:e.__scopeMenu,value:n,onValueChange:o,children:l.jsx(No,{...s,ref:t})})});Ed.displayName=Pd;var Rd="MenuRadioItem",Md=f.forwardRef((e,t)=>{const{value:n,...r}=e,s=J0(Rd,e.__scopeMenu),o=n===s.value;return l.jsx(Dd,{scope:e.__scopeMenu,checked:o,children:l.jsx(ir,{role:"menuitemradio","aria-checked":o,...r,ref:t,"data-state":Io(o),onSelect:B(r.onSelect,()=>s.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});Md.displayName=Rd;var jo="MenuItemIndicator",[Dd,Q0]=ct(jo,{checked:!1}),Nd=f.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...s}=e,o=Q0(jo,n);return l.jsx(Be,{present:r||$n(o.checked)||o.checked===!0,children:l.jsx(J.span,{...s,ref:t,"data-state":Io(o.checked)})})});Nd.displayName=jo;var eC="MenuSeparator",jd=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return l.jsx(J.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});jd.displayName=eC;var tC="MenuArrow",Id=f.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,s=or(n);return l.jsx(Cu,{...s,...r,ref:t})});Id.displayName=tC;var nC="MenuSub",[PS,kd]=ct(nC),kt="MenuSubTrigger",Od=f.forwardRef((e,t)=>{const n=lt(kt,e.__scopeMenu),r=cn(kt,e.__scopeMenu),s=kd(kt,e.__scopeMenu),o=Mo(kt,e.__scopeMenu),i=f.useRef(null),{pointerGraceTimerRef:a,onPointerGraceIntentChange:c}=o,d={__scopeMenu:e.__scopeMenu},u=f.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return f.useEffect(()=>u,[u]),f.useEffect(()=>{const h=a.current;return()=>{window.clearTimeout(h),c(null)}},[a,c]),l.jsx(Eo,{asChild:!0,...d,children:l.jsx(Td,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":s.contentId,"data-state":Vd(n.open),...e,ref:Ia(t,s.onTriggerChange),onClick:h=>{e.onClick?.(h),!(e.disabled||h.defaultPrevented)&&(h.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:B(e.onPointerMove,Zt(h=>{o.onItemEnter(h),!h.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(o.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),u()},100))})),onPointerLeave:B(e.onPointerLeave,Zt(h=>{u();const p=n.content?.getBoundingClientRect();if(p){const m=n.content?.dataset.side,g=m==="right",v=g?-5:5,y=p[g?"left":"right"],x=p[g?"right":"left"];o.onPointerGraceIntentChange({area:[{x:h.clientX+v,y:h.clientY},{x:y,y:p.top},{x,y:p.top},{x,y:p.bottom},{x:y,y:p.bottom}],side:m}),window.clearTimeout(a.current),a.current=window.setTimeout(()=>o.onPointerGraceIntentChange(null),300)}else{if(o.onTriggerLeave(h),h.defaultPrevented)return;o.onPointerGraceIntentChange(null)}})),onKeyDown:B(e.onKeyDown,h=>{const p=o.searchRef.current!=="";e.disabled||p&&h.key===" "||L0[r.dir].includes(h.key)&&(n.onOpenChange(!0),n.content?.focus(),h.preventDefault())})})})});Od.displayName=kt;var Ld="MenuSubContent",_d=f.forwardRef((e,t)=>{const n=bd(be,e.__scopeMenu),{forceMount:r=n.forceMount,...s}=e,o=lt(be,e.__scopeMenu),i=cn(be,e.__scopeMenu),a=kd(Ld,e.__scopeMenu),c=f.useRef(null),d=ee(t,c);return l.jsx(qt.Provider,{scope:e.__scopeMenu,children:l.jsx(Be,{present:r||o.open,children:l.jsx(qt.Slot,{scope:e.__scopeMenu,children:l.jsx(Do,{id:a.contentId,"aria-labelledby":a.triggerId,...s,ref:d,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:u=>{i.isUsingKeyboardRef.current&&c.current?.focus(),u.preventDefault()},onCloseAutoFocus:u=>u.preventDefault(),onFocusOutside:B(e.onFocusOutside,u=>{u.target!==a.trigger&&o.onOpenChange(!1)}),onEscapeKeyDown:B(e.onEscapeKeyDown,u=>{i.onClose(),u.preventDefault()}),onKeyDown:B(e.onKeyDown,u=>{const h=u.currentTarget.contains(u.target),p=_0[i.dir].includes(u.key);h&&p&&(o.onOpenChange(!1),a.trigger?.focus(),u.preventDefault())})})})})})});_d.displayName=Ld;function Vd(e){return e?"open":"closed"}function $n(e){return e==="indeterminate"}function Io(e){return $n(e)?"indeterminate":e?"checked":"unchecked"}function rC(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function sC(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function oC(e,t,n){const s=t.length>1&&Array.from(t).every(d=>d===t[0])?t[0]:t,o=n?e.indexOf(n):-1;let i=sC(e,Math.max(o,0));s.length===1&&(i=i.filter(d=>d!==n));const c=i.find(d=>d.toLowerCase().startsWith(s.toLowerCase()));return c!==n?c:void 0}function iC(e,t){const{x:n,y:r}=e;let s=!1;for(let o=0,i=t.length-1;o<t.length;i=o++){const a=t[o],c=t[i],d=a.x,u=a.y,h=c.x,p=c.y;u>r!=p>r&&n<(h-d)*(r-u)/(p-u)+d&&(s=!s)}return s}function aC(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return iC(n,t)}function Zt(e){return t=>t.pointerType==="mouse"?e(t):void 0}var cC=xd,lC=Eo,uC=wd,dC=Cd,fC=No,hC=Sd,pC=ir,mC=Ad,gC=Ed,vC=Md,yC=Nd,xC=jd,bC=Id,wC=Od,CC=_d,ar="DropdownMenu",[SC,ES]=Ze(ar,[vd]),de=vd(),[TC,Fd]=SC(ar),Bd=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:s,defaultOpen:o,onOpenChange:i,modal:a=!0}=e,c=de(t),d=f.useRef(null),[u,h]=Gn({prop:s,defaultProp:o??!1,onChange:i,caller:ar});return l.jsx(TC,{scope:t,triggerId:ot(),triggerRef:d,contentId:ot(),open:u,onOpenChange:h,onOpenToggle:f.useCallback(()=>h(p=>!p),[h]),modal:a,children:l.jsx(cC,{...c,open:u,onOpenChange:h,dir:r,modal:a,children:n})})};Bd.displayName=ar;var $d="DropdownMenuTrigger",Ud=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...s}=e,o=Fd($d,n),i=de(n);return l.jsx(lC,{asChild:!0,...i,children:l.jsx(J.button,{type:"button",id:o.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":o.open?o.contentId:void 0,"data-state":o.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...s,ref:Ia(t,o.triggerRef),onPointerDown:B(e.onPointerDown,a=>{!r&&a.button===0&&a.ctrlKey===!1&&(o.onOpenToggle(),o.open||a.preventDefault())}),onKeyDown:B(e.onKeyDown,a=>{r||(["Enter"," "].includes(a.key)&&o.onOpenToggle(),a.key==="ArrowDown"&&o.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});Ud.displayName=$d;var AC="DropdownMenuPortal",zd=e=>{const{__scopeDropdownMenu:t,...n}=e,r=de(t);return l.jsx(uC,{...r,...n})};zd.displayName=AC;var Wd="DropdownMenuContent",Hd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=Fd(Wd,n),o=de(n),i=f.useRef(!1);return l.jsx(dC,{id:s.contentId,"aria-labelledby":s.triggerId,...o,...r,ref:t,onCloseAutoFocus:B(e.onCloseAutoFocus,a=>{i.current||s.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:B(e.onInteractOutside,a=>{const c=a.detail.originalEvent,d=c.button===0&&c.ctrlKey===!0,u=c.button===2||d;(!s.modal||u)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Hd.displayName=Wd;var PC="DropdownMenuGroup",EC=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(fC,{...s,...r,ref:t})});EC.displayName=PC;var RC="DropdownMenuLabel",Kd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(hC,{...s,...r,ref:t})});Kd.displayName=RC;var MC="DropdownMenuItem",Gd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(pC,{...s,...r,ref:t})});Gd.displayName=MC;var DC="DropdownMenuCheckboxItem",Yd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(mC,{...s,...r,ref:t})});Yd.displayName=DC;var NC="DropdownMenuRadioGroup",jC=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(gC,{...s,...r,ref:t})});jC.displayName=NC;var IC="DropdownMenuRadioItem",Xd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(vC,{...s,...r,ref:t})});Xd.displayName=IC;var kC="DropdownMenuItemIndicator",qd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(yC,{...s,...r,ref:t})});qd.displayName=kC;var OC="DropdownMenuSeparator",Zd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(xC,{...s,...r,ref:t})});Zd.displayName=OC;var LC="DropdownMenuArrow",_C=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(bC,{...s,...r,ref:t})});_C.displayName=LC;var VC="DropdownMenuSubTrigger",Jd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(wC,{...s,...r,ref:t})});Jd.displayName=VC;var FC="DropdownMenuSubContent",Qd=f.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,s=de(n);return l.jsx(CC,{...s,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Qd.displayName=FC;var BC=Bd,$C=Ud,UC=zd,ef=Hd,tf=Kd,nf=Gd,rf=Yd,sf=Xd,of=qd,af=Zd,cf=Jd,lf=Qd;const zC=BC,WC=$C,HC=f.forwardRef(({className:e,inset:t,children:n,...r},s)=>l.jsxs(cf,{ref:s,className:j("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[n,l.jsx(La,{className:"ml-auto h-4 w-4"})]}));HC.displayName=cf.displayName;const KC=f.forwardRef(({className:e,...t},n)=>l.jsx(lf,{ref:n,className:j("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));KC.displayName=lf.displayName;const uf=f.forwardRef(({className:e,sideOffset:t=4,...n},r)=>l.jsx(UC,{children:l.jsx(ef,{ref:r,sideOffset:t,className:j("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})}));uf.displayName=ef.displayName;const vs=f.forwardRef(({className:e,inset:t,...n},r)=>l.jsx(nf,{ref:r,className:j("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...n}));vs.displayName=nf.displayName;const GC=f.forwardRef(({className:e,children:t,checked:n,...r},s)=>l.jsxs(rf,{ref:s,className:j("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:n,...r,children:[l.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:l.jsx(of,{children:l.jsx(ka,{className:"h-4 w-4"})})}),t]}));GC.displayName=rf.displayName;const YC=f.forwardRef(({className:e,children:t,...n},r)=>l.jsxs(sf,{ref:r,className:j("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[l.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:l.jsx(of,{children:l.jsx(Of,{className:"h-2 w-2 fill-current"})})}),t]}));YC.displayName=sf.displayName;const df=f.forwardRef(({className:e,inset:t,...n},r)=>l.jsx(tf,{ref:r,className:j("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...n}));df.displayName=tf.displayName;const ys=f.forwardRef(({className:e,...t},n)=>l.jsx(af,{ref:n,className:j("-mx-1 my-1 h-px bg-muted",e),...t}));ys.displayName=af.displayName;function XC({className:e}){const t=Pa(),{logout:n}=Da(),{user:r}=Pe(),s=a=>a.split(" ").map(c=>c[0]).join("").toUpperCase().slice(0,2),o=()=>{t({to:"/user"})},i=()=>{n(),t({to:"/auth/login"})};return l.jsxs(zC,{children:[l.jsx(WC,{asChild:!0,children:l.jsx(ie,{variant:"ghost",size:"sm",className:`h-8 w-8 rounded-full p-0 ${e}`,children:l.jsx(Ra,{className:"h-8 w-8",children:l.jsx(Ma,{className:"text-xs",children:s(r?.name||"用户")})})})}),l.jsxs(uf,{align:"end",className:"w-56",children:[l.jsx(df,{className:"font-normal",children:l.jsx("div",{className:"flex flex-col space-y-1",children:l.jsx("p",{className:"text-sm font-medium leading-none",children:r?.name||"用户"})})}),l.jsx(ys,{}),l.jsxs(vs,{onClick:o,children:[l.jsx(bf,{className:"mr-2 h-4 w-4"}),l.jsx("span",{children:"账号管理"})]}),l.jsx(ys,{}),l.jsxs(vs,{onClick:i,children:[l.jsx(zf,{className:"mr-2 h-4 w-4"}),l.jsx("span",{children:"退出登录"})]})]})]})}function qC({references:e,messageId:t}){const[n,r]=f.useState(new Set),s=o=>{r(i=>{const a=new Set(i);return a.has(o)?a.delete(o):a.add(o),a})};return!e||e.length===0?null:l.jsxs("div",{className:"mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[l.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[l.jsx($f,{className:"h-4 w-4 text-blue-600"}),l.jsx("span",{className:"text-sm font-medium text-blue-800",children:"参考文档"}),l.jsxs(Rf,{variant:"secondary",className:"text-xs",children:[e.length," 个文档"]})]}),l.jsx("div",{className:"space-y-2",children:e.map((o,i)=>l.jsxs("div",{className:"border border-blue-200 rounded-md bg-white",children:[l.jsxs(ie,{variant:"ghost",className:"w-full justify-between p-3 h-auto text-left",onClick:()=>s(i),children:[l.jsxs("div",{className:"flex-1",children:[l.jsx("div",{className:"font-medium text-sm text-gray-900",children:o.doc_name}),o.page_number&&o.page_number.length>0&&l.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["页码: ",o.page_number.join(", ")]})]}),n.has(i)?l.jsx(Oa,{className:"h-4 w-4 text-gray-500"}):l.jsx(La,{className:"h-4 w-4 text-gray-500"})]}),n.has(i)&&l.jsxs("div",{className:"px-3 pb-3 border-t border-gray-100",children:[o.title&&l.jsx("div",{className:"text-sm font-medium text-blue-700 mb-2",children:o.title}),l.jsx("div",{className:"text-sm text-gray-700 leading-relaxed",children:o.text}),o.doc_id&&l.jsxs("div",{className:"text-xs text-gray-400 mt-2",children:["文档ID: ",o.doc_id]})]})]},i))})]})}const ba=()=>l.jsx("div",{className:"flex items-center justify-center gap-1",children:[...Array(3)].map((e,t)=>l.jsx(Kn.div,{className:"h-3 w-3 rounded-full bg-blue-500",initial:{x:0},animate:{x:[0,10,0],opacity:[.5,1,.5],scale:[1,1.2,1]},transition:{duration:1,repeat:1/0,delay:t*.2}},t))});let xs=null,Ot=null,bs=null;const ko=async()=>{if(typeof window>"u")throw new Error("File reading is only supported on the client side");if(xs||(xs=await lr(()=>import("./index-BDXR_-XV.js").then(e=>e.i),__vite__mapDeps([0,1,2]))),Ot||(Ot=await lr(()=>import("./pdf-CtA8PhPd.js"),[]),Ot.GlobalWorkerOptions.workerSrc=`//cdnjs.cloudflare.com/ajax/libs/pdf.js/${Ot.version}/pdf.worker.min.js`),!bs){const e=await lr(()=>import("./word-f0viOaJw.js").then(t=>t.w),__vite__mapDeps([3,1,2]));bs=e.default||e}};async function ZC(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=s=>{const o=s.target?.result;t(o)},r.onerror=()=>n(new Error("Failed to read text file")),r.readAsText(e,"utf-8")})}async function JC(e){return await ko(),new Promise((t,n)=>{const r=new FileReader;r.onload=async s=>{try{const o=s.target?.result,i=await xs.extractRawText({arrayBuffer:o});t(i.value)}catch(o){n(new Error(`Failed to read Word file: ${o}`))}},r.onerror=()=>n(new Error("Failed to read Word file")),r.readAsArrayBuffer(e)})}async function QC(e){return await ko(),new Promise((t,n)=>{const r=new FileReader;r.onload=async s=>{try{const o=s.target?.result,a=await new bs().extract(Buffer.from(o));t(a.getBody())}catch(o){n(new Error(`Failed to read legacy Word file: ${o}`))}},r.onerror=()=>n(new Error("Failed to read legacy Word file")),r.readAsArrayBuffer(e)})}async function eS(e){return await ko(),new Promise((t,n)=>{const r=new FileReader;r.onload=async s=>{try{const o=s.target?.result,i=await Ot.getDocument({data:o}).promise;let a="";for(let c=1;c<=i.numPages;c++){const h=(await(await i.getPage(c)).getTextContent()).items.map(p=>p.str).join(" ");a+=`Page ${c}:
${h}

`}t(a)}catch(o){n(new Error(`Failed to read PDF file: ${o}`))}},r.onerror=()=>n(new Error("Failed to read PDF file")),r.readAsArrayBuffer(e)})}async function tS(e){const n=e.name.toLowerCase().split(".").pop();console.log(`Reading file: ${e.name} (${e.size} bytes)`),console.log(`File type: ${e.type}`),console.log(`File extension: ${n}`);try{let r="";if(n==="txt"||e.type==="text/plain")r=await ZC(e);else if(n==="docx"||e.type==="application/vnd.openxmlformats-officedocument.wordprocessingml.document")r=await JC(e);else if(n==="doc"||e.type==="application/msword")r=await QC(e);else if(n==="pdf"||e.type==="application/pdf")r=await eS(e);else throw new Error(`Unsupported file type: ${n}. Only TXT, DOC, DOCX, and PDF files are supported.`);return console.log(`Successfully read file content (${r.length} characters)`),console.log("File content preview:",r.substring(0,200)+"..."),r}catch(r){throw console.error("Error reading file:",r),r}}function nS(e){const n=e.name.toLowerCase().split(".").pop(),r=["txt","doc","docx","pdf"],s=["text/plain","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/pdf"];return r.includes(n||"")||s.includes(e.type)}function rS(){return l.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"text-foreground",children:[l.jsx("circle",{cx:"4",cy:"12",r:"2",fill:"currentColor",children:l.jsx("animate",{id:"spinner_qFRN",begin:"0;spinner_OcgL.end+0.25s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),l.jsx("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor",children:l.jsx("animate",{begin:"spinner_qFRN.begin+0.1s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),l.jsx("circle",{cx:"20",cy:"12",r:"2",fill:"currentColor",children:l.jsx("animate",{id:"spinner_OcgL",begin:"spinner_qFRN.begin+0.2s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})})]})}function wa({variant:e="received",layout:t="default",className:n,children:r}){return l.jsx("div",{className:j("flex items-start gap-2 mb-4",e==="sent"&&"flex-row-reverse",n),children:r})}function Ca({variant:e="received",isLoading:t,className:n,children:r}){return l.jsx("div",{className:j("rounded-lg p-3",e==="sent"?"bg-primary text-primary-foreground":"bg-muted",n),children:t?l.jsx("div",{className:"flex items-center space-x-2",children:l.jsx(rS,{})}):r})}function Sa({src:e,fallback:t="AI",className:n}){return l.jsxs(Ra,{className:j("h-8 w-8",n),children:[e&&l.jsx(wf,{src:e}),l.jsx(Ma,{children:t})]})}function sS({icon:e,onClick:t,className:n}){return l.jsx(ie,{variant:"ghost",size:"icon",className:j("h-6 w-6",n),onClick:t,children:e})}function oS({className:e,children:t}){return l.jsx("div",{className:j("flex items-center gap-1 mt-2",e),children:t})}function iS({children:e,redirectTo:t="/auth/login"}){const{isAuthenticated:n,isLoading:r}=Da(),s=Pa();return Ae.useEffect(()=>{if(!r&&!n){const o=window.location.pathname;o!==t&&localStorage.setItem("redirectAfterLogin",o),s({to:t})}},[r,n,s,t]),r?l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:l.jsxs("div",{className:"text-center",children:[l.jsx(Fr,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),l.jsx("p",{className:"text-gray-600",children:"正在验证登录状态..."})]})}):n?l.jsx(l.Fragment,{children:e}):l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:l.jsxs("div",{className:"text-center",children:[l.jsx(Fr,{className:"h-8 w-8 animate-spin mx-auto mb-4 text-blue-600"}),l.jsx("p",{className:"text-gray-600",children:"正在跳转到登录页面..."})]})})}const aS=({className:e})=>l.jsx("svg",{height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",className:e,children:l.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.4697 13.5303L13 14.0607L14.0607 13L13.5303 12.4697L9.06065 7.99999L13.5303 3.53032L14.0607 2.99999L13 1.93933L12.4697 2.46966L7.99999 6.93933L3.53032 2.46966L2.99999 1.93933L1.93933 2.99999L2.46966 3.53032L6.93933 7.99999L2.46966 12.4697L1.93933 13L2.99999 14.0607L3.53032 13.5303L7.99999 9.06065L12.4697 13.5303Z"})}),cS=()=>l.jsx("svg",{height:"16",strokeLinejoin:"round",viewBox:"0 0 16 16",width:"16",className:"fill-gray-1000",children:l.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.5 8C13.5 4.96643 11.0257 2.5 7.96452 2.5C5.42843 2.5 3.29365 4.19393 2.63724 6.5H5.25H6V8H5.25H0.75C0.335787 8 0 7.66421 0 7.25V2.75V2H1.5V2.75V5.23347C2.57851 2.74164 5.06835 1 7.96452 1C11.8461 1 15 4.13001 15 8C15 11.87 11.8461 15 7.96452 15C5.62368 15 3.54872 13.8617 2.27046 12.1122L1.828 11.5066L3.03915 10.6217L3.48161 11.2273C4.48831 12.6051 6.12055 13.5 7.96452 13.5C11.0257 13.5 13.5 11.0336 13.5 8Z"})});let Vr=null,lS=0;const ne={toasts:[],listeners:new Set,add(e,t,n,r,s,o){const i=lS++,a={id:i,text:e,preserve:n,action:r,onAction:s,onUndoAction:o,type:t};if(!a.preserve){a.remaining=3e3,a.start=Date.now();const c=()=>{this.toasts=this.toasts.filter(d=>d.id!==i),this.notify()};a.timeout=setTimeout(c,a.remaining),a.pause=()=>{a.timeout&&(clearTimeout(a.timeout),a.timeout=void 0,a.remaining-=Date.now()-a.start)},a.resume=()=>{a.timeout||(a.start=Date.now(),a.timeout=setTimeout(c,a.remaining))}}this.toasts.push(a),this.notify()},remove(e){ne.toasts=ne.toasts.filter(t=>t.id!==e),ne.notify()},subscribe(e){return ne.listeners.add(e),()=>{ne.listeners.delete(e)}},notify(){ne.listeners.forEach(e=>e())}},uS=()=>{const[e,t]=f.useState([]),[n,r]=f.useState([]),[s,o]=f.useState(!1),i=g=>v=>{v&&g.measuredHeight==null&&(g.measuredHeight=v.getBoundingClientRect().height,ne.notify())};f.useEffect(()=>(t([...ne.toasts]),ne.subscribe(()=>{t([...ne.toasts])})),[]),f.useEffect(()=>{const g=e.filter(v=>!n.includes(v.id)).map(v=>v.id);g.length>0&&requestAnimationFrame(()=>{r(v=>[...v,...g])})},[e]);const c=Math.max(0,e.length-3),d=(g,v)=>{if(g===v-1)return"none";const y=v-1-g;let x=e[v-1]?.measuredHeight||63;for(let C=v-1;C>g;C--)s?x+=(e[C-1]?.measuredHeight||63)+10:x+=20;const b=-y,w=s?1:1-.05*y;return`translate3d(0, calc(100% - ${x}px), ${b}px) scale(${w})`},u=()=>{o(!0),ne.toasts.forEach(g=>g.pause?.())},h=()=>{o(!1),ne.toasts.forEach(g=>g.resume?.())},m=e.slice(c).reduce((g,v)=>g+(v.measuredHeight??63),0);return l.jsx("div",{className:"fixed bottom-4 right-4 z-[9999] pointer-events-none w-[420px]",style:{height:m},children:l.jsx("div",{className:"relative pointer-events-auto w-full",style:{height:m},onMouseEnter:u,onMouseLeave:h,children:e.map((g,v)=>{const y=v>=c;return l.jsx("div",{ref:i(g),className:vf("absolute right-0 bottom-0 shadow-menu rounded-xl leading-[21px] p-4 h-fit",{message:"bg-geist-background text-gray-1000",success:"bg-blue-700 text-contrast-fg",warning:"bg-amber-800 text-gray-1000 dark:text-gray-100",error:"bg-red-800 text-contrast-fg"}[g.type],y?"opacity-100":"opacity-0",v<c&&"pointer-events-none"),style:{width:420,transition:"all .35s cubic-bezier(.25,.75,.6,.98)",transform:n.includes(g.id)?d(v,e.length):"translate3d(0, 100%, 150px) scale(1)"},children:l.jsxs("div",{className:"flex flex-col items-center justify-between text-[.875rem]",children:[l.jsxs("div",{className:"w-full h-full flex items-center justify-between gap-4",children:[l.jsx("span",{children:g.text}),!g.action&&l.jsxs("div",{className:"flex gap-1",children:[g.onUndoAction&&l.jsx(ie,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>{g.onUndoAction?.(),ne.remove(g.id)},children:l.jsx(cS,{})}),l.jsx(ie,{variant:"ghost",size:"icon",className:"h-6 w-6",onClick:()=>ne.remove(g.id),children:l.jsx(aS,{className:{message:"fill-gray-1000",success:"fill-contrast-fg",warning:"fill-gray-1000 dark:fill-gray-100",error:"fill-contrast-fg"}[g.type]})})]})]}),g.action&&l.jsxs("div",{className:"w-full flex items-center justify-end gap-2",children:[l.jsx(ie,{variant:"ghost",size:"sm",onClick:()=>ne.remove(g.id),children:"Dismiss"}),l.jsx(ie,{variant:"default",size:"sm",onClick:()=>{g?.onAction&&g?.onAction(),ne.remove(g.id)},children:g.action})]})]})},g.id)})})})},bn=()=>{if(Vr)return;const e=document.createElement("div");e.className="fixed bottom-4 right-4 z-[9999]",document.body.appendChild(e),Vr=mf.createRoot(e),Vr.render(l.jsx(uS,{}))},dS=()=>({message:f.useCallback(({text:e,preserve:t,action:n,onAction:r,onUndoAction:s})=>{bn(),ne.add(e,"message",t,n,r,s)},[]),success:f.useCallback(e=>{bn(),ne.add(e,"success")},[]),warning:f.useCallback(e=>{bn(),ne.add(e,"warning")},[]),error:f.useCallback(e=>{bn(),ne.add(e,"error")},[])}),Ta=["无忧问答","无忧分析师","无忧计算师"];function fS(){const[e,t]=f.useState(Ta[0]),[n,r]=f.useState(!1),[s,o]=f.useState(null),[i,a]=f.useState(!1),[c,d]=f.useState(!1),{getCurrentConversation:u,addMessage:h,currentConversationId:p,createConversation:m}=as(),g=dS(),v=u(),y=v?.messages||[];f.useEffect(()=>{if(v){if(v.mode!==e&&t(v.mode),v.mode==="无忧分析师"){const R=v.messages.filter(N=>N.role==="assistant").pop();if(R)try{const _=R.content.match(/```json\n?([\s\S]*?)\n?```/);if(_){const V=JSON.parse(_[1]);O(V),console.log("从对话历史恢复分析结果:",V)}else O([])}catch(N){console.log("无法解析历史分析结果:",N),O([])}else O([])}else O([]);o(null)}else O([]),o(null)},[p,v,e]);const x=f.useCallback(R=>{t(R),o(null);const{setCurrentConversation:N}=as.getState();N(null),console.log(`切换到 ${R} 模式`)},[]),b="sk-c904fe28cf294faaa28a80fbd13175f9",w="622fbd2ef57c413baafa29527d205414",C="YOUR_FILE_APP_ID",S="YOUR_CALCULATE_APP_ID",[A,T]=f.useState(null),[E,P]=f.useState(""),[D,O]=f.useState([]),W=f.useCallback(async R=>{T(R),P(""),O([]);try{if(console.log("=== 开始读取文件 ==="),console.log("文件名:",R.name),console.log("文件大小:",R.size,"bytes"),console.log("文件类型:",R.type),!nS(R)){alert("不支持的文件类型。请选择 TXT、DOC、DOCX 或 PDF 文件。");return}const N=await tS(R);console.log("=== 文件内容 ==="),console.log("内容长度:",N.length,"字符"),console.log("文件内容:"),console.log(N),console.log("=== 文件读取完成 ==="),P(N)}catch(N){console.error("文件读取失败:",N),P(""),alert(`文件读取失败: ${N instanceof Error?N.message:"未知错误"}`)}},[]),H=f.useCallback(async R=>{if(!A||!E){alert("请先上传文件");return}try{d(!0);const N=R?`${R}

请分析以下文件内容：`:"请分析以下文件内容：";console.log("=== 开始发送到 DashScope API ==="),console.log("用户输入:",R),console.log("增强后的 prompt:",N),await U(E,A.name,N)}catch(N){console.error("分析失败:",N),d(!1),alert(`分析失败: ${N instanceof Error?N.message:"未知错误"}`)}},[A,E]),z=R=>{O(R),console.log("=== 格式化显示结果 ==="),R.forEach((N,_)=>{console.log(`%c问题 ${_+1}:`,"color: #e74c3c; font-weight: bold;"),console.log(`%c原文: ${N.origin}`,"color: #3498db;"),console.log(`%c问题描述: ${N.issueDes}`,"color: #f39c12;"),console.log(`%c改进建议: ${N.suggestion}`,"color: #27ae60;"),console.log(`%c依据: ${N.reason}`,"color: #9b59b6;"),console.log("---")})},G=async R=>{try{const{user:N}=Pe.getState();if(N&&N.id){console.log("=== 数据库更新 ==="),console.log(`Token 使用量: ${R}`),console.log("请求次数: +1");let _;N.dingTalkUnionId?_=await Pf(N.dingTalkUnionId,R):_=await Ef(N.id,R),_?(console.log("用户token使用量更新成功:",_),Pe.getState().setUser(_)):console.error("更新用户token使用量失败")}else console.warn("用户信息不完整，无法更新数据库")}catch(N){console.error("更新数据库失败:",N)}},U=async(R,N,_)=>{try{const V=_?`${_}

文件内容：
${R}`:R,K=await fetch(`https://dashscope.aliyuncs.com/api/v1/apps/${C}/completion`,{method:"POST",headers:{Authorization:`Bearer ${b}`,"Content-Type":"application/json"},body:JSON.stringify({input:{prompt:V},parameters:{},debug:{}})});if(!K.ok)throw new Error(`API 请求失败: ${K.status} ${K.statusText}`);const ae=await K.json();if(console.log("=== DashScope API 响应 ==="),console.log("完整响应数据:"),console.log(JSON.stringify(ae,null,2)),ae.output&&ae.output.text)try{const Ue=ae.output.text.replace(/```json\n?/,"").replace(/\n?```$/,""),Nt=JSON.parse(Ue);if(console.log("=== 解析后的结果 ==="),console.log(Nt),z(Nt),await L(N,V,Nt),d(!1),ae.usage&&ae.usage.models&&ae.usage.models[0]){const cr=ae.usage.models[0].input_tokens+ae.usage.models[0].output_tokens;setTimeout(()=>{G(cr)},0)}}catch(Ue){console.error("解析 JSON 失败:",Ue),console.log("原始 text 内容:",ae.output.text),d(!1)}console.log("=== API 响应完成 ===")}catch(V){throw console.error("API 请求失败:",V),V}},L=async(R,N,_)=>{try{let V=p;if(!V){const Ue=`分析文件: ${R}`;V=await m(Ue,"无忧分析师"),console.log(`创建新对话: ${Ue}`)}const K=`上传文件进行分析：${R}

文件内容摘要：${N.substring(0,200)}${N.length>200?"...":""}`;await h(V,K,"user");const ae=`文件分析完成，共发现 ${_.length} 个问题：

\`\`\`json
${JSON.stringify(_,null,2)}
\`\`\``;await h(V,ae,"assistant"),console.log("分析结果已保存到对话历史")}catch(V){console.error("保存分析结果到对话历史失败:",V)}},I=()=>{r(!n)},M=async(R,N)=>{console.log("=== API调用调试信息 ==="),console.log("API_KEY:",b),console.log("APP_ID:",w),console.log("环境变量 VITE_DASHSCOPE_API_KEY:","sk-c904fe28cf294faaa28a80fbd13175f9"),console.log("环境变量 VITE_YOUR_APP_ID:","622fbd2ef57c413baafa29527d205414"),console.log("========================");const _=`https://dashscope.aliyuncs.com/api/v1/apps/${w}/completion`,V={input:{prompt:R},parameters:{},debug:{}};N&&(V.input.session_id=N);const K=await fetch(_,{method:"POST",headers:{Authorization:`Bearer ${b}`,"Content-Type":"application/json"},body:JSON.stringify(V)});if(!K.ok)throw new Error(`API请求失败: ${K.status} ${K.statusText}`);return await K.json()},k=async(R,N)=>{console.log("=== 计算API调用调试信息 ==="),console.log("API_KEY:",b),console.log("CALCULATE_APP_ID:",S),console.log("环境变量 VITE_CALCULATE_APP_ID:",void 0),console.log("========================");const _=`https://dashscope.aliyuncs.com/api/v1/apps/${S}/completion`,V={input:{prompt:R},parameters:{},debug:{}};N&&(V.input.session_id=N);const K=await fetch(_,{method:"POST",headers:{Authorization:`Bearer ${b}`,"Content-Type":"application/json"},body:JSON.stringify(V)});if(!K.ok)throw new Error(`计算API请求失败: ${K.status} ${K.statusText}`);return await K.json()},$=async R=>{let N=p;if(!N)try{const _=R.length>8?R.substring(0,8)+"...":R;N=await m(_,e),console.log(`创建新对话: ${_}`)}catch(_){console.error("创建对话失败:",_);return}try{await h(N,R,"user"),a(!0);let _;e==="无忧计算师"?_=await k(R,s||void 0):_=await M(R,s||void 0),_.output.session_id&&o(_.output.session_id);let V=_.output.text,K=[];if(e==="无忧问答"&&_.output.doc_references&&(K=_.output.doc_references,console.log("文档引用:",K)),a(!1),await h(N,V,"assistant",K),_.usage&&_.usage.models&&_.usage.models[0]){const ae=_.usage.models[0].input_tokens+_.usage.models[0].output_tokens;setTimeout(()=>{G(ae)},0)}}catch(_){console.error("API调用失败:",_),a(!1);const V=`抱歉，服务暂时不可用。请检查API配置或稍后重试。错误信息: ${_ instanceof Error?_.message:"未知错误"}`;await h(N,V,"assistant")}},se=async R=>{try{await navigator.clipboard.writeText(R),g.success("复制成功")}catch(N){console.error("复制失败:",N);try{const _=document.createElement("textarea");_.value=R,document.body.appendChild(_),_.select(),document.execCommand("copy"),document.body.removeChild(_),g.success("复制成功")}catch(_){console.error("降级复制方案也失败:",_),g.error("复制失败，请手动选择文本复制")}}},ge=()=>{try{const R=window.open("","_blank");if(!R){g.error("无法打开打印窗口，请检查浏览器弹窗设置");return}if(!document.querySelector("[data-analysis-results]")){g.error("未找到分析结果内容");return}const _=`
				<!DOCTYPE html>
				<html>
				<head>
					<meta charset="utf-8">
					<title>无忧分析师 - 分析报告</title>
					<style>
						body {
							font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
							line-height: 1.6;
							color: #333;
							max-width: 800px;
							margin: 0 auto;
							padding: 20px;
						}
						.header {
							text-align: center;
							margin-bottom: 30px;
							border-bottom: 2px solid #e5e7eb;
							padding-bottom: 20px;
						}
						.header h1 {
							color: #1f2937;
							margin: 0;
							font-size: 24px;
						}
						.header .date {
							color: #6b7280;
							font-size: 14px;
							margin-top: 10px;
						}
						.analysis-item {
							margin-bottom: 30px;
							border: 1px solid #e5e7eb;
							border-radius: 8px;
							padding: 20px;
							break-inside: avoid;
						}
						.problem-badge {
							display: inline-block;
							background-color: #fef2f2;
							color: #dc2626;
							padding: 4px 12px;
							border-radius: 20px;
							font-size: 14px;
							font-weight: 600;
							margin-bottom: 16px;
						}
						.section {
							margin-bottom: 16px;
						}
						.section-title {
							font-weight: 600;
							margin-bottom: 8px;
							font-size: 14px;
						}
						.section-title.origin { color: #1d4ed8; }
						.section-title.issue { color: #ea580c; }
						.section-title.suggestion { color: #059669; }
						.section-title.reason { color: #7c3aed; }
						.section-content {
							padding: 12px;
							border-radius: 6px;
							border-left: 4px solid;
						}
						.section-content.origin {
							background-color: #eff6ff;
							border-left-color: #3b82f6;
						}
						.section-content.issue {
							background-color: #fff7ed;
							border-left-color: #f97316;
						}
						.section-content.suggestion {
							background-color: #ecfdf5;
							border-left-color: #10b981;
						}
						.section-content.reason {
							background-color: #f3e8ff;
							border-left-color: #8b5cf6;
						}
						@media print {
							body { margin: 0; }
							.analysis-item { page-break-inside: avoid; }
						}
					</style>
				</head>
				<body>
					<div class="header">
						<h1>📋 无忧分析师 - 分析报告</h1>
						<div class="date">生成时间：${new Date().toLocaleString("zh-CN")}</div>
						<div class="date">共发现 ${D.length} 个问题</div>
					</div>
					${D.map((V,K)=>`
						<div class="analysis-item">
							<div class="problem-badge">问题 ${K+1}</div>

							<div class="section">
								<div class="section-title origin">📝 原文内容</div>
								<div class="section-content origin">${V.origin}</div>
							</div>

							<div class="section">
								<div class="section-title issue">⚠️ 问题描述</div>
								<div class="section-content issue">${V.issueDes}</div>
							</div>

							<div class="section">
								<div class="section-title suggestion">💡 改进建议</div>
								<div class="section-content suggestion">${V.suggestion}</div>
							</div>

							<div class="section">
								<div class="section-title reason">📚 依据说明</div>
								<div class="section-content reason">${V.reason}</div>
							</div>
						</div>
					`).join("")}
				</body>
				</html>
			`;R.document.write(_),R.document.close(),R.onload=()=>{setTimeout(()=>{R.print(),R.onafterprint=()=>{R.close()}},500)},g.success("正在准备PDF导出...")}catch(R){console.error("导出PDF失败:",R),g.error("导出PDF失败，请重试")}};return l.jsx(b0,{isCollapsed:n,onToggle:I,currentMode:e,children:l.jsxs("div",{className:"h-screen bg-gray-50 flex flex-col",children:[l.jsx("div",{className:"bg-white h-16 border-b border-gray-200 px-6 py-3 flex-shrink-0",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsx(ie,{variant:"ghost",size:"sm",onClick:I,className:"h-8 w-8 p-0",title:n?"展开侧边栏 (Ctrl+B)":"收起侧边栏 (Ctrl+B)",children:n?l.jsx(Zf,{className:"h-4 w-4"}):l.jsx(Xf,{className:"h-4 w-4"})}),l.jsx("div",{className:"flex justify-center flex-1",children:l.jsx("div",{className:"flex w-fit rounded-full bg-muted p-1",children:Ta.map(R=>l.jsx(sy,{text:R,selected:e===R,setSelected:x,discount:R==="无忧分析师"},R))})}),l.jsx(XC,{className:"ml-4"})]})}),l.jsx("main",{className:"flex-1 flex flex-col min-h-0 overflow-hidden",children:e==="无忧问答"||e==="无忧计算师"?l.jsxs(l.Fragment,{children:[l.jsx("div",{className:"flex-1 overflow-y-auto p-6 pb-0 min-h-0",children:y.length===0?l.jsxs("div",{className:"text-center text-gray-500 mt-20",children:[e==="无忧问答"?l.jsxs(l.Fragment,{children:[l.jsx("p",{className:"text-lg mb-2",children:"👋 欢迎使用无忧问答"}),l.jsx("p",{children:"开始对话，快速检索石化油储行业"})]}):l.jsxs(l.Fragment,{children:[l.jsx("p",{className:"text-lg mb-2",children:"🧮 欢迎使用无忧计算师"}),l.jsx("p",{children:"开始对话，进行智能计算和分析"})]}),s&&l.jsxs("p",{className:"text-sm mt-4 text-blue-600",children:["🔗 多轮对话已启用 (会话ID: ",s.slice(0,8),"...)"]})]}):l.jsxs("div",{className:"max-w-4xl mx-auto space-y-4",children:[y.map(R=>{const N=R.role==="user"?"sent":"received";return l.jsxs("div",{className:"space-y-2",children:[R.role==="assistant"&&R.docReferences&&R.docReferences.length>0&&l.jsx(qC,{references:R.docReferences,messageId:R.id}),l.jsxs(wa,{variant:N,children:[l.jsx(Sa,{src:N==="sent"?"/user.jpg":"/chatlogo.png",fallback:N==="sent"?"用户":"AI"}),l.jsxs("div",{className:"flex-1",children:[l.jsx(Ca,{variant:N,children:R.content}),R.role==="assistant"&&l.jsx(oS,{children:l.jsx(sS,{icon:l.jsx(_f,{className:"h-3 w-3"}),onClick:()=>se(R.content)})})]})]})]},R.id)}),i&&l.jsx("div",{className:"space-y-2",children:l.jsxs(wa,{variant:"received",children:[l.jsx(Sa,{src:"/chatlogo.png",fallback:"AI"}),l.jsx("div",{className:"flex-1",children:l.jsx(Ca,{variant:"received",children:l.jsx("div",{className:"flex items-center justify-center py-2",children:l.jsx(ba,{})})})})]})})]})}),l.jsx("div",{className:"w-full flex justify-center flex-shrink-0 p-6 pt-0",children:l.jsx("div",{className:"w-full max-w-3xl",children:l.jsx(ty,{onSend:$,showSuggestedActions:e!=="无忧计算师"})})})]}):l.jsx(l.Fragment,{children:l.jsx("div",{className:"flex-1 flex flex-col p-6 min-h-0 overflow-hidden",children:D.length===0?l.jsxs("div",{className:"flex-1 flex flex-col justify-center items-center",children:[l.jsxs("div",{className:"text-center text-gray-500 mb-6",children:[l.jsx("p",{className:"text-lg mb-2",children:"📄 无忧分析师"}),l.jsx("p",{children:"上传文件进行智能分析和优化建议"})]}),l.jsxs("div",{className:"w-full max-w-2xl",children:[A&&l.jsx("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:l.jsxs("div",{className:"flex items-center gap-2 text-blue-700",children:[l.jsx("span",{children:"📎"}),l.jsxs("span",{className:"font-medium",children:["已选择文件：",A.name]}),l.jsx("button",{onClick:()=>{T(null),P("")},className:"ml-auto text-blue-500 hover:text-blue-700",children:"✕"})]})}),l.jsx(ry,{onFileSelect:W,onSend:H,placeholder:A?"输入分析要求或问题（可选），然后点击发送开始分析...":"上传文件进行分析，或输入问题...",disabled:c}),c&&l.jsxs("div",{className:"flex items-center justify-center gap-2 mt-4 text-blue-600",children:[l.jsx(ba,{}),l.jsx("span",{children:"正在分析文件..."})]})]})]}):l.jsxs("div",{className:"w-full max-w-4xl mx-auto h-full flex flex-col",children:[l.jsxs("div",{className:"mb-6 flex items-center justify-between flex-shrink-0",children:[l.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"📋 分析结果"}),l.jsxs("div",{className:"flex items-center gap-2",children:[l.jsxs(ie,{onClick:ge,variant:"outline",size:"sm",className:"flex items-center gap-2",children:[l.jsx(Ff,{className:"h-4 w-4"}),"导出PDF"]}),l.jsx(ie,{onClick:()=>{O([]),P(""),T(null)},variant:"outline",size:"sm",children:"重新分析"})]})]}),l.jsx("div",{className:"flex-1 overflow-y-auto space-y-6 pr-2","data-analysis-results":!0,children:D.map((R,N)=>l.jsxs("div",{className:"bg-white rounded-lg border border-gray-200 p-6 shadow-sm",children:[l.jsx("div",{className:"mb-4",children:l.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:["问题 ",N+1]})}),l.jsxs("div",{className:"space-y-4",children:[l.jsxs("div",{children:[l.jsx("h4",{className:"text-sm font-semibold text-blue-700 mb-2",children:"📝 原文内容"}),l.jsx("p",{className:"text-gray-700 bg-blue-50 p-3 rounded-md border-l-4 border-blue-400",children:R.origin})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"text-sm font-semibold text-orange-700 mb-2",children:"⚠️ 问题描述"}),l.jsx("p",{className:"text-gray-700 bg-orange-50 p-3 rounded-md border-l-4 border-orange-400",children:R.issueDes})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"text-sm font-semibold text-green-700 mb-2",children:"💡 改进建议"}),l.jsx("p",{className:"text-gray-700 bg-green-50 p-3 rounded-md border-l-4 border-green-400",children:R.suggestion})]}),l.jsxs("div",{children:[l.jsx("h4",{className:"text-sm font-semibold text-purple-700 mb-2",children:"📚 依据说明"}),l.jsx("p",{className:"text-gray-700 bg-purple-50 p-3 rounded-md border-l-4 border-purple-400",children:R.reason})]})]})]},N))})]})})})})]})})}const RS=function(){return l.jsx(iS,{children:l.jsx(fS,{})})};export{RS as component};

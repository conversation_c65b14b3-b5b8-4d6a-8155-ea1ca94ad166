import{h as nr,g as sr}from"./main-Dj4JwwOF.js";import{r as ar}from"./index-DRz5BQNA.js";function or(u,S){for(var O=0;O<S.length;O++){const s=S[O];if(typeof s!="string"&&!Array.isArray(s)){for(const y in s)if(y!=="default"&&!(y in u)){const C=Object.getOwnPropertyDescriptor(s,y);C&&Object.defineProperty(u,y,C.get?C:{enumerable:!0,get:()=>s[y]})}}}return Object.freeze(Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}))}var We={},ke={};/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */var Et;function hr(){return Et||(Et=1,ke.read=function(u,S,O,s,y){var C,g,a=y*8-s-1,B=(1<<a)-1,o=B>>1,T=-7,h=O?y-1:0,z=O?-1:1,k=u[S+h];for(h+=z,C=k&(1<<-T)-1,k>>=-T,T+=a;T>0;C=C*256+u[S+h],h+=z,T-=8);for(g=C&(1<<-T)-1,C>>=-T,T+=s;T>0;g=g*256+u[S+h],h+=z,T-=8);if(C===0)C=1-o;else{if(C===B)return g?NaN:(k?-1:1)*(1/0);g=g+Math.pow(2,s),C=C-o}return(k?-1:1)*g*Math.pow(2,C-s)},ke.write=function(u,S,O,s,y,C){var g,a,B,o=C*8-y-1,T=(1<<o)-1,h=T>>1,z=y===23?Math.pow(2,-24)-Math.pow(2,-77):0,k=s?0:C-1,I=s?1:-1,w=S<0||S===0&&1/S<0?1:0;for(S=Math.abs(S),isNaN(S)||S===1/0?(a=isNaN(S)?1:0,g=T):(g=Math.floor(Math.log(S)/Math.LN2),S*(B=Math.pow(2,-g))<1&&(g--,B*=2),g+h>=1?S+=z/B:S+=z*Math.pow(2,1-h),S*B>=2&&(g++,B/=2),g+h>=T?(a=0,g=T):g+h>=1?(a=(S*B-1)*Math.pow(2,y),g=g+h):(a=S*Math.pow(2,h-1)*Math.pow(2,y),g=0));y>=8;u[O+k]=a&255,k+=I,a/=256,y-=8);for(g=g<<y|a,o+=y;o>0;u[O+k]=g&255,k+=I,g/=256,o-=8);u[O+k-I]|=w*128}),ke}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var _t;function zt(){return _t||(_t=1,function(u){var S=ar(),O=hr(),s=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;u.Buffer=a,u.SlowBuffer=F,u.INSPECT_MAX_BYTES=50;var y=**********;u.kMaxLength=y,a.TYPED_ARRAY_SUPPORT=C(),!a.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function C(){try{var r=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(r,e),r.foo()===42}catch{return!1}}Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}});function g(r){if(r>y)throw new RangeError('The value "'+r+'" is invalid for option "size"');var e=new Uint8Array(r);return Object.setPrototypeOf(e,a.prototype),e}function a(r,e,t){if(typeof r=="number"){if(typeof e=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return h(r)}return B(r,e,t)}a.poolSize=8192;function B(r,e,t){if(typeof r=="string")return z(r,e);if(ArrayBuffer.isView(r))return I(r);if(r==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r);if(oe(r,ArrayBuffer)||r&&oe(r.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(oe(r,SharedArrayBuffer)||r&&oe(r.buffer,SharedArrayBuffer)))return w(r,e,t);if(typeof r=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var n=r.valueOf&&r.valueOf();if(n!=null&&n!==r)return a.from(n,e,t);var l=m(r);if(l)return l;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof r[Symbol.toPrimitive]=="function")return a.from(r[Symbol.toPrimitive]("string"),e,t);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r)}a.from=function(r,e,t){return B(r,e,t)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array);function o(r){if(typeof r!="number")throw new TypeError('"size" argument must be of type number');if(r<0)throw new RangeError('The value "'+r+'" is invalid for option "size"')}function T(r,e,t){return o(r),r<=0?g(r):e!==void 0?typeof t=="string"?g(r).fill(e,t):g(r).fill(e):g(r)}a.alloc=function(r,e,t){return T(r,e,t)};function h(r){return o(r),g(r<0?0:p(r)|0)}a.allocUnsafe=function(r){return h(r)},a.allocUnsafeSlow=function(r){return h(r)};function z(r,e){if((typeof e!="string"||e==="")&&(e="utf8"),!a.isEncoding(e))throw new TypeError("Unknown encoding: "+e);var t=D(r,e)|0,n=g(t),l=n.write(r,e);return l!==t&&(n=n.slice(0,l)),n}function k(r){for(var e=r.length<0?0:p(r.length)|0,t=g(e),n=0;n<e;n+=1)t[n]=r[n]&255;return t}function I(r){if(oe(r,Uint8Array)){var e=new Uint8Array(r);return w(e.buffer,e.byteOffset,e.byteLength)}return k(r)}function w(r,e,t){if(e<0||r.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(r.byteLength<e+(t||0))throw new RangeError('"length" is outside of buffer bounds');var n;return e===void 0&&t===void 0?n=new Uint8Array(r):t===void 0?n=new Uint8Array(r,e):n=new Uint8Array(r,e,t),Object.setPrototypeOf(n,a.prototype),n}function m(r){if(a.isBuffer(r)){var e=p(r.length)|0,t=g(e);return t.length===0||r.copy(t,0,0,e),t}if(r.length!==void 0)return typeof r.length!="number"||Se(r.length)?g(0):k(r);if(r.type==="Buffer"&&Array.isArray(r.data))return k(r.data)}function p(r){if(r>=y)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+y.toString(16)+" bytes");return r|0}function F(r){return+r!=r&&(r=0),a.alloc(+r)}a.isBuffer=function(e){return e!=null&&e._isBuffer===!0&&e!==a.prototype},a.compare=function(e,t){if(oe(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),oe(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var n=e.length,l=t.length,A=0,N=Math.min(n,l);A<N;++A)if(e[A]!==t[A]){n=e[A],l=t[A];break}return n<l?-1:l<n?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(e.length===0)return a.alloc(0);var n;if(t===void 0)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var l=a.allocUnsafe(t),A=0;for(n=0;n<e.length;++n){var N=e[n];if(oe(N,Uint8Array))A+N.length>l.length?a.from(N).copy(l,A):Uint8Array.prototype.set.call(l,N,A);else if(a.isBuffer(N))N.copy(l,A);else throw new TypeError('"list" argument must be an Array of Buffers');A+=N.length}return l};function D(r,e){if(a.isBuffer(r))return r.length;if(ArrayBuffer.isView(r)||oe(r,ArrayBuffer))return r.byteLength;if(typeof r!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof r);var t=r.length,n=arguments.length>2&&arguments[2]===!0;if(!n&&t===0)return 0;for(var l=!1;;)switch(e){case"ascii":case"latin1":case"binary":return t;case"utf8":case"utf-8":return Z(r).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return t*2;case"hex":return t>>>1;case"base64":return le(r).length;default:if(l)return n?-1:Z(r).length;e=(""+e).toLowerCase(),l=!0}}a.byteLength=D;function E(r,e,t){var n=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((t===void 0||t>this.length)&&(t=this.length),t<=0)||(t>>>=0,e>>>=0,t<=e))return"";for(r||(r="utf8");;)switch(r){case"hex":return K(this,e,t);case"utf8":case"utf-8":return R(this,e,t);case"ascii":return q(this,e,t);case"latin1":case"binary":return j(this,e,t);case"base64":return d(this,e,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return G(this,e,t);default:if(n)throw new TypeError("Unknown encoding: "+r);r=(r+"").toLowerCase(),n=!0}}a.prototype._isBuffer=!0;function f(r,e,t){var n=r[e];r[e]=r[t],r[t]=n}a.prototype.swap16=function(){var e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)f(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)f(this,t,t+3),f(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)f(this,t,t+7),f(this,t+1,t+6),f(this,t+2,t+5),f(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return e===0?"":arguments.length===0?R(this,0,e):E.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e?!0:a.compare(this,e)===0},a.prototype.inspect=function(){var e="",t=u.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(e,t,n,l,A){if(oe(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(t===void 0&&(t=0),n===void 0&&(n=e?e.length:0),l===void 0&&(l=0),A===void 0&&(A=this.length),t<0||n>e.length||l<0||A>this.length)throw new RangeError("out of range index");if(l>=A&&t>=n)return 0;if(l>=A)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,l>>>=0,A>>>=0,this===e)return 0;for(var N=A-l,Q=n-t,J=Math.min(N,Q),te=this.slice(l,A),ue=e.slice(t,n),ie=0;ie<J;++ie)if(te[ie]!==ue[ie]){N=te[ie],Q=ue[ie];break}return N<Q?-1:Q<N?1:0};function b(r,e,t,n,l){if(r.length===0)return-1;if(typeof t=="string"?(n=t,t=0):t>**********?t=**********:t<-2147483648&&(t=-2147483648),t=+t,Se(t)&&(t=l?0:r.length-1),t<0&&(t=r.length+t),t>=r.length){if(l)return-1;t=r.length-1}else if(t<0)if(l)t=0;else return-1;if(typeof e=="string"&&(e=a.from(e,n)),a.isBuffer(e))return e.length===0?-1:L(r,e,t,n,l);if(typeof e=="number")return e=e&255,typeof Uint8Array.prototype.indexOf=="function"?l?Uint8Array.prototype.indexOf.call(r,e,t):Uint8Array.prototype.lastIndexOf.call(r,e,t):L(r,[e],t,n,l);throw new TypeError("val must be string, number or Buffer")}function L(r,e,t,n,l){var A=1,N=r.length,Q=e.length;if(n!==void 0&&(n=String(n).toLowerCase(),n==="ucs2"||n==="ucs-2"||n==="utf16le"||n==="utf-16le")){if(r.length<2||e.length<2)return-1;A=2,N/=2,Q/=2,t/=2}function J(me,Fe){return A===1?me[Fe]:me.readUInt16BE(Fe*A)}var te;if(l){var ue=-1;for(te=t;te<N;te++)if(J(r,te)===J(e,ue===-1?0:te-ue)){if(ue===-1&&(ue=te),te-ue+1===Q)return ue*A}else ue!==-1&&(te-=te-ue),ue=-1}else for(t+Q>N&&(t=N-Q),te=t;te>=0;te--){for(var ie=!0,ve=0;ve<Q;ve++)if(J(r,te+ve)!==J(e,ve)){ie=!1;break}if(ie)return te}return-1}a.prototype.includes=function(e,t,n){return this.indexOf(e,t,n)!==-1},a.prototype.indexOf=function(e,t,n){return b(this,e,t,n,!0)},a.prototype.lastIndexOf=function(e,t,n){return b(this,e,t,n,!1)};function U(r,e,t,n){t=Number(t)||0;var l=r.length-t;n?(n=Number(n),n>l&&(n=l)):n=l;var A=e.length;n>A/2&&(n=A/2);for(var N=0;N<n;++N){var Q=parseInt(e.substr(N*2,2),16);if(Se(Q))return N;r[t+N]=Q}return N}function W(r,e,t,n){return we(Z(e,r.length-t),r,t,n)}function P(r,e,t,n){return we(ne(e),r,t,n)}function $(r,e,t,n){return we(le(e),r,t,n)}function c(r,e,t,n){return we(ce(e,r.length-t),r,t,n)}a.prototype.write=function(e,t,n,l){if(t===void 0)l="utf8",n=this.length,t=0;else if(n===void 0&&typeof t=="string")l=t,n=this.length,t=0;else if(isFinite(t))t=t>>>0,isFinite(n)?(n=n>>>0,l===void 0&&(l="utf8")):(l=n,n=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var A=this.length-t;if((n===void 0||n>A)&&(n=A),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");l||(l="utf8");for(var N=!1;;)switch(l){case"hex":return U(this,e,t,n);case"utf8":case"utf-8":return W(this,e,t,n);case"ascii":case"latin1":case"binary":return P(this,e,t,n);case"base64":return $(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return c(this,e,t,n);default:if(N)throw new TypeError("Unknown encoding: "+l);l=(""+l).toLowerCase(),N=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function d(r,e,t){return e===0&&t===r.length?S.fromByteArray(r):S.fromByteArray(r.slice(e,t))}function R(r,e,t){t=Math.min(r.length,t);for(var n=[],l=e;l<t;){var A=r[l],N=null,Q=A>239?4:A>223?3:A>191?2:1;if(l+Q<=t){var J,te,ue,ie;switch(Q){case 1:A<128&&(N=A);break;case 2:J=r[l+1],(J&192)===128&&(ie=(A&31)<<6|J&63,ie>127&&(N=ie));break;case 3:J=r[l+1],te=r[l+2],(J&192)===128&&(te&192)===128&&(ie=(A&15)<<12|(J&63)<<6|te&63,ie>2047&&(ie<55296||ie>57343)&&(N=ie));break;case 4:J=r[l+1],te=r[l+2],ue=r[l+3],(J&192)===128&&(te&192)===128&&(ue&192)===128&&(ie=(A&15)<<18|(J&63)<<12|(te&63)<<6|ue&63,ie>65535&&ie<1114112&&(N=ie))}}N===null?(N=65533,Q=1):N>65535&&(N-=65536,n.push(N>>>10&1023|55296),N=56320|N&1023),n.push(N),l+=Q}return M(n)}var v=4096;function M(r){var e=r.length;if(e<=v)return String.fromCharCode.apply(String,r);for(var t="",n=0;n<e;)t+=String.fromCharCode.apply(String,r.slice(n,n+=v));return t}function q(r,e,t){var n="";t=Math.min(r.length,t);for(var l=e;l<t;++l)n+=String.fromCharCode(r[l]&127);return n}function j(r,e,t){var n="";t=Math.min(r.length,t);for(var l=e;l<t;++l)n+=String.fromCharCode(r[l]);return n}function K(r,e,t){var n=r.length;(!e||e<0)&&(e=0),(!t||t<0||t>n)&&(t=n);for(var l="",A=e;A<t;++A)l+=ye[r[A]];return l}function G(r,e,t){for(var n=r.slice(e,t),l="",A=0;A<n.length-1;A+=2)l+=String.fromCharCode(n[A]+n[A+1]*256);return l}a.prototype.slice=function(e,t){var n=this.length;e=~~e,t=t===void 0?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);var l=this.subarray(e,t);return Object.setPrototypeOf(l,a.prototype),l};function V(r,e,t){if(r%1!==0||r<0)throw new RangeError("offset is not uint");if(r+e>t)throw new RangeError("Trying to access beyond buffer length")}a.prototype.readUintLE=a.prototype.readUIntLE=function(e,t,n){e=e>>>0,t=t>>>0,n||V(e,t,this.length);for(var l=this[e],A=1,N=0;++N<t&&(A*=256);)l+=this[e+N]*A;return l},a.prototype.readUintBE=a.prototype.readUIntBE=function(e,t,n){e=e>>>0,t=t>>>0,n||V(e,t,this.length);for(var l=this[e+--t],A=1;t>0&&(A*=256);)l+=this[e+--t]*A;return l},a.prototype.readUint8=a.prototype.readUInt8=function(e,t){return e=e>>>0,t||V(e,1,this.length),this[e]},a.prototype.readUint16LE=a.prototype.readUInt16LE=function(e,t){return e=e>>>0,t||V(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUint16BE=a.prototype.readUInt16BE=function(e,t){return e=e>>>0,t||V(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUint32LE=a.prototype.readUInt32LE=function(e,t){return e=e>>>0,t||V(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+this[e+3]*16777216},a.prototype.readUint32BE=a.prototype.readUInt32BE=function(e,t){return e=e>>>0,t||V(e,4,this.length),this[e]*16777216+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,n){e=e>>>0,t=t>>>0,n||V(e,t,this.length);for(var l=this[e],A=1,N=0;++N<t&&(A*=256);)l+=this[e+N]*A;return A*=128,l>=A&&(l-=Math.pow(2,8*t)),l},a.prototype.readIntBE=function(e,t,n){e=e>>>0,t=t>>>0,n||V(e,t,this.length);for(var l=t,A=1,N=this[e+--l];l>0&&(A*=256);)N+=this[e+--l]*A;return A*=128,N>=A&&(N-=Math.pow(2,8*t)),N},a.prototype.readInt8=function(e,t){return e=e>>>0,t||V(e,1,this.length),this[e]&128?(255-this[e]+1)*-1:this[e]},a.prototype.readInt16LE=function(e,t){e=e>>>0,t||V(e,2,this.length);var n=this[e]|this[e+1]<<8;return n&32768?n|4294901760:n},a.prototype.readInt16BE=function(e,t){e=e>>>0,t||V(e,2,this.length);var n=this[e+1]|this[e]<<8;return n&32768?n|4294901760:n},a.prototype.readInt32LE=function(e,t){return e=e>>>0,t||V(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e=e>>>0,t||V(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e=e>>>0,t||V(e,4,this.length),O.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e=e>>>0,t||V(e,4,this.length),O.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e=e>>>0,t||V(e,8,this.length),O.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e=e>>>0,t||V(e,8,this.length),O.read(this,e,!1,52,8)};function X(r,e,t,n,l,A){if(!a.isBuffer(r))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>l||e<A)throw new RangeError('"value" argument is out of bounds');if(t+n>r.length)throw new RangeError("Index out of range")}a.prototype.writeUintLE=a.prototype.writeUIntLE=function(e,t,n,l){if(e=+e,t=t>>>0,n=n>>>0,!l){var A=Math.pow(2,8*n)-1;X(this,e,t,n,A,0)}var N=1,Q=0;for(this[t]=e&255;++Q<n&&(N*=256);)this[t+Q]=e/N&255;return t+n},a.prototype.writeUintBE=a.prototype.writeUIntBE=function(e,t,n,l){if(e=+e,t=t>>>0,n=n>>>0,!l){var A=Math.pow(2,8*n)-1;X(this,e,t,n,A,0)}var N=n-1,Q=1;for(this[t+N]=e&255;--N>=0&&(Q*=256);)this[t+N]=e/Q&255;return t+n},a.prototype.writeUint8=a.prototype.writeUInt8=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,1,255,0),this[t]=e&255,t+1},a.prototype.writeUint16LE=a.prototype.writeUInt16LE=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,2,65535,0),this[t]=e&255,this[t+1]=e>>>8,t+2},a.prototype.writeUint16BE=a.prototype.writeUInt16BE=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=e&255,t+2},a.prototype.writeUint32LE=a.prototype.writeUInt32LE=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=e&255,t+4},a.prototype.writeUint32BE=a.prototype.writeUInt32BE=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4},a.prototype.writeIntLE=function(e,t,n,l){if(e=+e,t=t>>>0,!l){var A=Math.pow(2,8*n-1);X(this,e,t,n,A-1,-A)}var N=0,Q=1,J=0;for(this[t]=e&255;++N<n&&(Q*=256);)e<0&&J===0&&this[t+N-1]!==0&&(J=1),this[t+N]=(e/Q>>0)-J&255;return t+n},a.prototype.writeIntBE=function(e,t,n,l){if(e=+e,t=t>>>0,!l){var A=Math.pow(2,8*n-1);X(this,e,t,n,A-1,-A)}var N=n-1,Q=1,J=0;for(this[t+N]=e&255;--N>=0&&(Q*=256);)e<0&&J===0&&this[t+N+1]!==0&&(J=1),this[t+N]=(e/Q>>0)-J&255;return t+n},a.prototype.writeInt8=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=e&255,t+1},a.prototype.writeInt16LE=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,2,32767,-32768),this[t]=e&255,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=e&255,t+2},a.prototype.writeInt32LE=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,4,**********,-2147483648),this[t]=e&255,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,n){return e=+e,t=t>>>0,n||X(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4};function se(r,e,t,n,l,A){if(t+n>r.length)throw new RangeError("Index out of range");if(t<0)throw new RangeError("Index out of range")}function ae(r,e,t,n,l){return e=+e,t=t>>>0,l||se(r,e,t,4),O.write(r,e,t,n,23,4),t+4}a.prototype.writeFloatLE=function(e,t,n){return ae(this,e,t,!0,n)},a.prototype.writeFloatBE=function(e,t,n){return ae(this,e,t,!1,n)};function fe(r,e,t,n,l){return e=+e,t=t>>>0,l||se(r,e,t,8),O.write(r,e,t,n,52,8),t+8}a.prototype.writeDoubleLE=function(e,t,n){return fe(this,e,t,!0,n)},a.prototype.writeDoubleBE=function(e,t,n){return fe(this,e,t,!1,n)},a.prototype.copy=function(e,t,n,l){if(!a.isBuffer(e))throw new TypeError("argument should be a Buffer");if(n||(n=0),!l&&l!==0&&(l=this.length),t>=e.length&&(t=e.length),t||(t=0),l>0&&l<n&&(l=n),l===n||e.length===0||this.length===0)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(l<0)throw new RangeError("sourceEnd out of bounds");l>this.length&&(l=this.length),e.length-t<l-n&&(l=e.length-t+n);var A=l-n;return this===e&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(t,n,l):Uint8Array.prototype.set.call(e,this.subarray(n,l),t),A},a.prototype.fill=function(e,t,n,l){if(typeof e=="string"){if(typeof t=="string"?(l=t,t=0,n=this.length):typeof n=="string"&&(l=n,n=this.length),l!==void 0&&typeof l!="string")throw new TypeError("encoding must be a string");if(typeof l=="string"&&!a.isEncoding(l))throw new TypeError("Unknown encoding: "+l);if(e.length===1){var A=e.charCodeAt(0);(l==="utf8"&&A<128||l==="latin1")&&(e=A)}}else typeof e=="number"?e=e&255:typeof e=="boolean"&&(e=Number(e));if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t=t>>>0,n=n===void 0?this.length:n>>>0,e||(e=0);var N;if(typeof e=="number")for(N=t;N<n;++N)this[N]=e;else{var Q=a.isBuffer(e)?e:a.from(e,l),J=Q.length;if(J===0)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(N=0;N<n-t;++N)this[N+t]=Q[N%J]}return this};var Ee=/[^+/0-9A-Za-z-_]/g;function re(r){if(r=r.split("=")[0],r=r.trim().replace(Ee,""),r.length<2)return"";for(;r.length%4!==0;)r=r+"=";return r}function Z(r,e){e=e||1/0;for(var t,n=r.length,l=null,A=[],N=0;N<n;++N){if(t=r.charCodeAt(N),t>55295&&t<57344){if(!l){if(t>56319){(e-=3)>-1&&A.push(239,191,189);continue}else if(N+1===n){(e-=3)>-1&&A.push(239,191,189);continue}l=t;continue}if(t<56320){(e-=3)>-1&&A.push(239,191,189),l=t;continue}t=(l-55296<<10|t-56320)+65536}else l&&(e-=3)>-1&&A.push(239,191,189);if(l=null,t<128){if((e-=1)<0)break;A.push(t)}else if(t<2048){if((e-=2)<0)break;A.push(t>>6|192,t&63|128)}else if(t<65536){if((e-=3)<0)break;A.push(t>>12|224,t>>6&63|128,t&63|128)}else if(t<1114112){if((e-=4)<0)break;A.push(t>>18|240,t>>12&63|128,t>>6&63|128,t&63|128)}else throw new Error("Invalid code point")}return A}function ne(r){for(var e=[],t=0;t<r.length;++t)e.push(r.charCodeAt(t)&255);return e}function ce(r,e){for(var t,n,l,A=[],N=0;N<r.length&&!((e-=2)<0);++N)t=r.charCodeAt(N),n=t>>8,l=t%256,A.push(l),A.push(n);return A}function le(r){return S.toByteArray(re(r))}function we(r,e,t,n){for(var l=0;l<n&&!(l+t>=e.length||l>=r.length);++l)e[l+t]=r[l];return l}function oe(r,e){return r instanceof e||r!=null&&r.constructor!=null&&r.constructor.name!=null&&r.constructor.name===e.name}function Se(r){return r!==r}var ye=function(){for(var r="**********abcdef",e=new Array(256),t=0;t<16;++t)for(var n=t*16,l=0;l<16;++l)e[n+l]=r[t]+r[l];return e}()}(We)),We}var $e,gt;function cr(){if(gt)return $e;gt=1;const u=Buffer.from("D0CF11E0A1B11AE1","hex");class S{constructor(){}load(s){for(let y=0;y<u.length;y++)if(u[y]!=s[y])return!1;this.secSize=1<<s.readInt16LE(30),this.shortSecSize=1<<s.readInt16LE(32),this.SATSize=s.readInt32LE(44),this.dirSecId=s.readInt32LE(48),this.shortStreamMax=s.readInt32LE(56),this.SSATSecId=s.readInt32LE(60),this.SSATSize=s.readInt32LE(64),this.MSATSecId=s.readInt32LE(68),this.MSATSize=s.readInt32LE(72),this.partialMSAT=new Array(109);for(let y=0;y<109;y++)this.partialMSAT[y]=s.readInt32LE(76+y*4);return!0}}return $e=S,$e}var Xe,wt;function ur(){if(wt)return Xe;wt=1;const u=-1;class S{constructor(s){this._doc=s}load(s){const y=this._doc,C=y._header;return this._table=new Array(s.length*(C.secSize/4)),y._readSectors(s).then(g=>{for(let a=0;a<g.length/4;a++)this._table[a]=g.readInt32LE(a*4)})}getSecIdChain(s){let y=s;const C=[];for(;y>u;){C.push(y);const g=y;if(y=this._table[y],y===g)break}return C}}return Xe=S,Xe}var je,yt;function fr(){if(yt)return je;yt=1;const u=1,S=2,O=5,s=-1;class y{constructor(g){this._doc=g}load(g){return this._doc._readSectors(g).then(B=>{const o=B.length/128;this._entries=new Array(o);for(let T=0;T<o;T++){const h=T*128,z=Math.max(B.readInt16LE(64+h)-1,0),k={};k.name=B.toString("utf16le",0+h,z+h),k.type=B.readInt8(66+h),k.nodeColor=B.readInt8(67+h),k.left=B.readInt32LE(68+h),k.right=B.readInt32LE(72+h),k.storageDirId=B.readInt32LE(76+h),k.secId=B.readInt32LE(116+h),k.size=B.readInt32LE(120+h),this._entries[T]=k}this.root=this._entries.find(T=>T.type===O),this._buildHierarchy(this.root)})}_buildHierarchy(g){const a=this._getChildIds(g);g.storages={},g.streams={};for(const B of a){const o=this._entries[B],T=o.name;o.type===u&&(g.storages[T]=o),o.type===S&&(g.streams[T]=o)}for(const B in g.storages)this._buildHierarchy(g.storages[B])}_getChildIds(g){const a=[],B=o=>{o.left!==s&&(a.push(o.left),B(this._entries[o.left])),o.right!==s&&(a.push(o.right),B(this._entries[o.right]))};if(g.storageDirId>-1){a.push(g.storageDirId);const o=this._entries[g.storageDirId];B(o)}return a}}return je=y,je}const lr={},dr=Object.freeze(Object.defineProperty({__proto__:null,default:lr},Symbol.toStringTag,{value:"Module"})),_e=nr(dr);var Ve,vt;function xr(){if(vt)return Ve;vt=1;const{Readable:u}=_e;class S extends u{constructor(s,y){super(),this._doc=s,this._streamEntry=y,this.initialize()}initialize(){if(this._index=0,this._done=!0,!this._streamEntry)return;const s=this._doc;this._bytes=this._streamEntry.size,this._allocationTable=s._SAT,this._shortStream=!1,this._bytes<s._header.shortStreamMax&&(this._shortStream=!0,this._allocationTable=s._SSAT),this._secIds=this._allocationTable.getSecIdChain(this._streamEntry.secId),this._done=!1}_readSector(s){return this._shortStream?this._doc._readShortSector(s):this._doc._readSector(s)}_read(){return this._done?this.push(null):this._index>=this._secIds.length?(this._done=!0,this.push(null)):this._readSector(this._secIds[this._index]).then(s=>{this._bytes-s.length<0&&(s=s.slice(0,this._bytes)),this._bytes-=s.length,this._index++,this.push(s)})}}return Ve=S,Ve}var Ye,bt;function pr(){if(bt)return Ye;bt=1;const u=xr();class S{constructor(s,y){this._doc=s,this._dirEntry=y}storage(s){return new S(this._doc,this._dirEntry.storages[s])}stream(s){return new u(this._doc,this._dirEntry.streams[s])}}return Ye=S,Ye}var Ge,St;function mr(){if(St)return Ge;St=1;const u=cr(),S=ur(),O=fr(),s=pr();class y{constructor(g){this._reader=g,this._skipBytes=0}read(){return Promise.resolve().then(()=>this._readHeader()).then(()=>this._readMSAT()).then(()=>this._readSAT()).then(()=>this._readSSAT()).then(()=>this._readDirectoryTree()).then(()=>{if(this._skipBytes!=0)return this._readCustomHeader()}).then(()=>this)}_readCustomHeader(){const g=Buffer.alloc(this._skipBytes);return this._reader.read(g,0,this._skipBytes,0).then(a=>{this._customHeaderCallback(a)})}_readHeader(){const g=Buffer.alloc(512);return this._reader.read(g,0,512,0+this._skipBytes).then(a=>{if(!(this._header=new u).load(a))throw new Error("Not a valid compound document")})}_readMSAT(){const g=this._header;if(this._MSAT=g.partialMSAT.slice(0),this._MSAT.length=g.SATSize,g.SATSize<=109||g.MSATSize==0)return Promise.resolve();let a=109,B=0;const o=(T,h,z)=>T>=g.MSATSize?Promise.resolve():this._readSector(z).then(k=>{let I;for(I=0;I<g.secSize-4&&!(h>=g.SATSize);I+=4)this._MSAT[h]=k.readInt32LE(I),h++;return z=k.readInt32LE(g.secSize-4),o(T+1,h,z)});return o(B,a,g.MSATSecId)}_readSector(g){return this._readSectors([g])}_readSectors(g){const a=this._header,B=Buffer.alloc(g.length*a.secSize),o=T=>{if(T>=g.length)return Promise.resolve(B);const h=T*a.secSize,z=this._getFileOffsetForSec(g[T]);return this._reader.read(B,h,a.secSize,z).then(()=>o(T+1))};return o(0)}_readShortSector(g){return this._readShortSectors([g])}_readShortSectors(g){const a=this._header,B=Buffer.alloc(g.length*a.shortSecSize),o=T=>{if(T>=g.length)return Promise.resolve(B);const h=T*a.shortSecSize,z=this._getFileOffsetForShortSec(g[T]);return this._reader.read(B,h,a.shortSecSize,z).then(()=>o(T+1))};return o(0)}_readSAT(){return this._SAT=new S(this),this._SAT.load(this._MSAT)}_readSSAT(){const g=this._header,a=this._SAT.getSecIdChain(g.SSATSecId);return a.length!=g.SSATSize?Promise.reject(new Error("Invalid Short Sector Allocation Table")):(this._SSAT=new S(this),this._SSAT.load(a))}_readDirectoryTree(){const g=this._header;this._directoryTree=new O(this);const a=this._SAT.getSecIdChain(g.dirSecId);return this._directoryTree.load(a).then(()=>{const B=this._directoryTree.root;this._rootStorage=new s(this,B),this._shortStreamSecIds=this._SAT.getSecIdChain(B.secId)})}_getFileOffsetForSec(g){const a=this._header.secSize;return this._skipBytes+(g+1)*a}_getFileOffsetForShortSec(g){const a=this._header.shortSecSize,B=g*a,o=this._header.secSize,T=Math.floor(B/o),h=B%o,z=this._shortStreamSecIds[T];return this._getFileOffsetForSec(z)+h}storage(g){return this._rootStorage.storage(g)}stream(g){return this._rootStorage.stream(g)}}return Ge=y,Ge}var Be={},Ct;function qt(){if(Ct)return Be;Ct=1;const u=[];u[2]="\0",u[5]="\0",u[7]="	",u[8]="\0",u[10]=`
`,u[11]=`
`,u[12]=`
`,u[13]=`
`,u[30]="‑";const S=[];S[130]="‚",S[131]="ƒ",S[132]="„",S[133]="…",S[134]="†",S[135]="‡",S[136]="ˆ",S[137]="‰",S[138]="Š",S[139]="‹",S[140]="Œ",S[142]="Ž",S[145]="‘",S[146]="’",S[147]="“",S[148]="”",S[149]="•",S[150]="–",S[151]="—",S[152]="˜",S[153]="™",S[154]="š",S[155]="›",S[156]="œ",S[158]="ž",S[159]="Ÿ",Be.binaryToUnicode=s=>s.replace(/([\x80-\x9f])/g,y=>S[y.charCodeAt(0)]),Be.clean=s=>{s=s.replace(/([\x02\x05\x07\x08\x0a\x0b\x0c\x0d\x1f])/g,C=>u[C.charCodeAt(0)]);let y=!0;for(;y;)y=!1,s=s.replace(/(?:\x13[^\x13\x14\x15]*\x14?([^\x13\x14\x15]*)\x15)/g,(C,g)=>(y=!0,g));return s.replace(/[\x00-\x07]/g,"")};const O=[];return O[8194]=" ",O[8195]=" ",O[8210]="-",O[8211]="-",O[8212]="-",O[8216]="'",O[8217]="'",O[8220]='"',O[8221]='"',Be.filter=s=>s.replace(/[\u2002\u2003\u2012\u2013\u2014\u2018\u2019\u201c\u201d]/g,y=>O[y.charCodeAt(0)]),Be}var Qe,Ft;function Wt(){if(Ft)return Qe;Ft=1;const{filter:u}=qt();class S{constructor(){this._body="",this._footnotes="",this._endnotes="",this._headers="",this._footers="",this._annotations="",this._textboxes="",this._headerTextboxes=""}getBody(s){s=s||{};const y=this._body;return s.filterUnicode==!1?y:u(y)}getFootnotes(s){s=s||{};const y=this._footnotes;return s.filterUnicode==!1?y:u(y)}getEndnotes(s){s=s||{};const y=this._endnotes;return s.filterUnicode==!1?y:u(y)}getHeaders(s){s=s||{};const y=this._headers+(s.includeFooters==!1?"":this._footers);return s.filterUnicode==!1?y:u(y)}getFooters(s){s=s||{};const y=this._footers;return s.filterUnicode==!1?y:u(y)}getAnnotations(s){s=s||{};const y=this._annotations;return s.filterUnicode==!1?y:u(y)}getTextboxes(s){s=s||{};const y=[];s.includeBody!=!1&&y.push(this._textboxes),s.includeHeadersAndFooters!=!1&&y.push(this._headerTextboxes);const C=y.join(`
`);return s.filterUnicode==!1?C:u(C)}}return Qe=S,Qe}var Ke,Tt;function Er(){if(Tt)return Ke;Tt=1;const u=mr(),S=Wt(),{binaryToUnicode:O,clean:s}=qt(),y=0,C=(w,m)=>{for(let p=0;p<w.length;p++){const F=w[p];if(m<=F.endCp)return p}},g=(w,m)=>{for(let p=0;p<w.length;p++){const F=w[p];if(m<=F.endFilePos)return p}};function a(w,m,p){const F=C(w,m),D=C(w,p),E=[];for(let f=F,b=D;f<=b;f++){const L=w[f],U=f===F?m-L.startCp:0,W=f===D?p-L.startCp:L.endCp;E.push(L.text.substring(U,W))}return E.join("")}function B(w,m,p,F){const D=w.startCp,E=D+w.length,f=w.text;m<D&&(m=D),p>E&&(p=E);const b=(m==D?"":f.slice(0,m-D))+"".padStart(p-m,F)+(p==E?"":f.slice(p-E));w.text=b}function o(w,m,p,F){const D=w.startFilePos,E=D+w.size,f=w.text;m<D&&(m=D),p>E&&(p=E);const b=(m==D?"":f.slice(0,(m-D)/w.bpc))+"".padStart((p-m)/w.bpc,F)+(p==E?"":f.slice((p-E)/w.bpc));w.text=b}function T(w,m,p,F){const D=C(w,m),E=C(w,p);for(let f=D,b=E;f<=b;f++){const L=w[f];B(L,m,p,F)}}function h(w,m,p,F){const D=g(w,m),E=g(w,p);for(let f=D,b=E;f<=b;f++){const L=w[f];o(L,m,p,F)}}function z(w,m,p){h(w,m,p,"\0")}const k=(w,m,p)=>{for(;m<w.length-1;){const F=w.readUInt16LE(m),D=F&31,E=F>>9&1,f=F>>10&7,b=F>>13&7;if(m+=2,p(w,m,F,D,E,f,b),b===0){m+=1;continue}else if(b===1){m+=1;continue}else if(b===2){m+=2;continue}else if(b===3){m+=4;continue}else if(b===4||b===5){m+=2;continue}else if(b===6){m+=w.readUInt8(m)+1;continue}else if(b===7){m+=3;continue}else throw new Error("Unparsed sprm")}};class I{constructor(){this._pieces=[],this._bookmarks={},this._boundaries={},this._taggedHeaders=[]}extract(m){const p=new u(m);return p.read().then(()=>this.documentStream(p,"WordDocument").then(F=>this.streamBuffer(F)).then(F=>this.extractWordDocument(p,F)))}buildDocument(){const m=new S,p=this._pieces;let F=0;return m._body=s(a(p,F,F+this._boundaries.ccpText)),F+=this._boundaries.ccpText,this._boundaries.ccpFtn&&(m._footnotes=s(a(p,F,F+this._boundaries.ccpFtn-1)),F+=this._boundaries.ccpFtn),this._boundaries.ccpHdd&&(m._headers=s(this._taggedHeaders.filter(D=>D.type==="headers").map(D=>D.text).join("")),m._footers=s(this._taggedHeaders.filter(D=>D.type==="footers").map(D=>D.text).join("")),F+=this._boundaries.ccpHdd),this._boundaries.ccpAtn&&(m._annotations=s(a(p,F,F+this._boundaries.ccpAtn-1)),F+=this._boundaries.ccpAtn),this._boundaries.ccpEdn&&(m._endnotes=s(a(p,F,F+this._boundaries.ccpEdn-1)),F+=this._boundaries.ccpEdn),this._boundaries.ccpTxbx&&(m._textboxes=s(a(p,F,F+this._boundaries.ccpTxbx-1)),F+=this._boundaries.ccpTxbx),this._boundaries.ccpHdrTxbx&&(m._headerTextboxes=s(a(p,F,F+this._boundaries.ccpHdrTxbx-1)),F+=this._boundaries.ccpHdrTxbx),m}extractWordDocument(m,p){const F=p.readUInt16LE(0);if(F!==42476)return Promise.reject(new Error(`This does not seem to be a Word document: Invalid magic number: ${F.toString(16)}`));const E=(p.readUInt16LE(10)&512)!==0?"1Table":"0Table";return this.documentStream(m,E).then(f=>this.streamBuffer(f)).then(f=>(this._boundaries.fcMin=p.readUInt32LE(24),this._boundaries.ccpText=p.readUInt32LE(76),this._boundaries.ccpFtn=p.readUInt32LE(80),this._boundaries.ccpHdd=p.readUInt32LE(84),this._boundaries.ccpAtn=p.readUInt32LE(92),this._boundaries.ccpEdn=p.readUInt32LE(96),this._boundaries.ccpTxbx=p.readUInt32LE(100),this._boundaries.ccpHdrTxbx=p.readUInt32LE(104),this.writeBookmarks(p,f),this.writePieces(p,f),this.writeCharacterProperties(p,f),this.writeParagraphProperties(p,f),this.normalizeHeaders(p,f),this.buildDocument()))}documentStream(m,p){return Promise.resolve(m.stream(p))}streamBuffer(m){return new Promise((p,F)=>{const D=[];return m.on("data",E=>D.push(E)),m.on("error",E=>F(E)),m.on("end",()=>p(Buffer.concat(D))),m})}writeFields(m,p,F){const D=m.readInt32LE(282),E=m.readUInt32LE(286);if(E==0)return;const f=(E-4)/6,b=(f+1)*4,L=p.slice(D,D+E);for(let U=0;U<f;U++)L.readUInt32LE(U*4),L.readUInt16LE(b+U*2)}writeBookmarks(m,p){const F=m.readUInt32LE(322),D=m.readUInt32LE(326),E=m.readUInt32LE(330),f=m.readUInt32LE(334),b=m.readUInt32LE(338),L=m.readUInt32LE(342);if(D===0)return;const U=p.slice(F,F+D),W=p.slice(E,E+f),P=p.slice(b,b+L),$=U.readUInt16LE(0);if(U.readUInt16LE(2),U.readUInt16LE(4),$!==65535)throw new Error("Internal error: unexpected single-byte bookmark data");let c=6;const d=0;for(;c<D;){let R=U.readUInt16LE(c);R=R*2;const v=U.slice(c+2,c+2+R),M=W.readUInt32LE(d*4),q=P.readUInt32LE(d*4);this._bookmarks[v]={start:M,end:q},c=c+R+2}}writePieces(m,p){let F,D=m.readUInt32LE(418);for(;F=p.readUInt8(D),F===1;){D=D+1;const U=p.readUInt16LE(D);D=D+2+U}if(F=p.readUInt8(D),D=D+1,F!==2)throw new Error("Internal error: ccorrupted Word file");const E=p.readUInt32LE(D);D=D+4;const f=(E-4)/12;let b=0,L=0;for(let U=0,W=f-1;U<=W;U++){const P=D+(f+1)*4+U*8+2;let $=p.readUInt32LE(P),c=!1;($&1073741824)===0?c=!0:($=$&-1073741825,$=Math.floor($/2));const d=p.readUInt32LE(D+U*4),R=p.readUInt32LE(D+(U+1)*4),v=R-d,M={startCp:b,startStream:L,totLength:v,startFilePos:$,unicode:c,bpc:c?2:1};M.size=M.bpc*(R-d);const q=m.slice($,$+M.size);c?M.text=q.toString("ucs2"):M.text=O(q.toString("binary")),M.length=M.text.length,M.endCp=M.startCp+M.length,M.endStream=M.startStream+M.size,M.endFilePos=M.startFilePos+M.size,b=M.endCp,L=M.endStream,this._pieces.push(M)}}normalizeHeaders(m,p){const F=this._pieces,D=m.readUInt32LE(242),E=m.readUInt32LE(246);if(E<8)return;const f=this._boundaries.ccpText+this._boundaries.ccpFtn,b=this._boundaries.ccpHdd,L=p.slice(D,D+E),U=E/4;let W=f+L.readUInt32LE(0);for(let P=1;P<U;P++){let $=f+L.readUInt32LE(P*4);$>f+b&&($=f+b);const c=a(F,W,$),d=P-1;[0,1,2].includes(d)?this._taggedHeaders.push({type:"footnoteSeparators",text:c}):[3,4,5].includes(d)?this._taggedHeaders.push({type:"endSeparators",text:c}):[0,1,4].includes(d%6)?this._taggedHeaders.push({type:"headers",text:c}):[2,3,5].includes(d%6)&&this._taggedHeaders.push({type:"footers",text:c}),/[^\r\n\u0002-\u0008]/.test(c)?T(F,$-1,$,"\0"):T(F,W,$,"\0"),W=$}}writeParagraphProperties(m,p){const F=this._pieces,D=m.readUInt32LE(258),E=m.readUInt32LE(262),f=(E-4)/8,b=(f+1)*4,L=p.slice(D,D+E);for(let U=0;U<f;U++){L.readUInt32LE(U*4);const W=L.readUInt32LE(b+U*4),P=m.slice(W*512,(W+1)*512),$=P.readUInt8(511);for(let c=0;c<$;c++){const d=P.readUInt32LE(c*4),R=P.readUInt32LE((c+1)*4),v=($+1)*4+c*13,M=P.readUInt8(v)*2,q=P.readUInt8(M);let j=null;if(q!==0)j=P.slice(M+1,M+1+2*q-1);else{const K=P.readUInt8(M+1);j=P.slice(M+2,M+2+2*K)}j.readUInt16LE(0),k(j,2,(K,G,V,X,se,ae,fe)=>{V===9239&&h(F,d,R,`
`)})}}}writeCharacterProperties(m,p){const F=this._pieces,D=m.readUInt32LE(250),E=m.readUInt32LE(254),f=(E-4)/8,b=(f+1)*4,L=p.slice(D,D+E);let U=null;for(let W=0;W<f;W++){L.readUInt32LE(W*4);const P=L.readUInt32LE(b+W*4),$=m.slice(P*512,(P+1)*512),c=$.readUInt8(511);for(let d=0;d<c;d++){const R=$.readUInt32LE(d*4),v=$.readUInt32LE((d+1)*4),M=$.readUInt8((c+1)*4+d);if(M==0)continue;const q=M*2,j=$.readUInt8(q),K=$.slice(q+1,q+1+j);k(K,0,(G,V,X,se)=>{if(se===y){if((G[V]&1)!=1)return;U===R?z(F,U,v):z(F,R,v),U=v}})}}}}return Ke=I,Ke}var Ue={},Ze={},At;function _r(){return At||(At=1,function(u){/**
 * Character classes and associated utilities for the 5th edition of XML 1.0.
 *
 * <AUTHOR> Dubeau
 * @license MIT
 * @copyright Louis-Dominique Dubeau
 */Object.defineProperty(u,"__esModule",{value:!0}),u.CHAR=`	
\r -퟿-�𐀀-􏿿`,u.S=` 	\r
`,u.NAME_START_CHAR=":A-Z_a-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-󯿿",u.NAME_CHAR="-"+u.NAME_START_CHAR+".0-9·̀-ͯ‿-⁀",u.CHAR_RE=new RegExp("^["+u.CHAR+"]$","u"),u.S_RE=new RegExp("^["+u.S+"]+$","u"),u.NAME_START_CHAR_RE=new RegExp("^["+u.NAME_START_CHAR+"]$","u"),u.NAME_CHAR_RE=new RegExp("^["+u.NAME_CHAR+"]$","u"),u.NAME_RE=new RegExp("^["+u.NAME_START_CHAR+"]["+u.NAME_CHAR+"]*$","u"),u.NMTOKEN_RE=new RegExp("^["+u.NAME_CHAR+"]+$","u");var S=9,O=10,s=13,y=32;u.S_LIST=[y,O,s,S];function C(o){return o>=y&&o<=55295||o===O||o===s||o===S||o>=57344&&o<=65533||o>=65536&&o<=1114111}u.isChar=C;function g(o){return o===y||o===O||o===s||o===S}u.isS=g;function a(o){return o>=65&&o<=90||o>=97&&o<=122||o===58||o===95||o===8204||o===8205||o>=192&&o<=214||o>=216&&o<=246||o>=248&&o<=767||o>=880&&o<=893||o>=895&&o<=8191||o>=8304&&o<=8591||o>=11264&&o<=12271||o>=12289&&o<=55295||o>=63744&&o<=64975||o>=65008&&o<=65533||o>=65536&&o<=983039}u.isNameStartChar=a;function B(o){return a(o)||o>=48&&o<=57||o===45||o===46||o===183||o>=768&&o<=879||o>=8255&&o<=8256}u.isNameChar=B}(Ze)),Ze}var Je={},It;function gr(){return It||(It=1,function(u){/**
 * Character classes and associated utilities for the 2nd edition of XML 1.1.
 *
 * <AUTHOR> Dubeau
 * @license MIT
 * @copyright Louis-Dominique Dubeau
 */Object.defineProperty(u,"__esModule",{value:!0}),u.CHAR="-퟿-�𐀀-􏿿",u.RESTRICTED_CHAR="-\b\v\f---",u.S=` 	\r
`,u.NAME_START_CHAR=":A-Z_a-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-󯿿",u.NAME_CHAR="-"+u.NAME_START_CHAR+".0-9·̀-ͯ‿-⁀",u.CHAR_RE=new RegExp("^["+u.CHAR+"]$","u"),u.RESTRICTED_CHAR_RE=new RegExp("^["+u.RESTRICTED_CHAR+"]$","u"),u.S_RE=new RegExp("^["+u.S+"]+$","u"),u.NAME_START_CHAR_RE=new RegExp("^["+u.NAME_START_CHAR+"]$","u"),u.NAME_CHAR_RE=new RegExp("^["+u.NAME_CHAR+"]$","u"),u.NAME_RE=new RegExp("^["+u.NAME_START_CHAR+"]["+u.NAME_CHAR+"]*$","u"),u.NMTOKEN_RE=new RegExp("^["+u.NAME_CHAR+"]+$","u");var S=9,O=10,s=13,y=32;u.S_LIST=[y,O,s,S];function C(h){return h>=1&&h<=55295||h>=57344&&h<=65533||h>=65536&&h<=1114111}u.isChar=C;function g(h){return h>=1&&h<=8||h===11||h===12||h>=14&&h<=31||h>=127&&h<=132||h>=134&&h<=159}u.isRestrictedChar=g;function a(h){return h===9||h===10||h===13||h>31&&h<127||h===133||h>159&&h<=55295||h>=57344&&h<=65533||h>=65536&&h<=1114111}u.isCharAndNotRestricted=a;function B(h){return h===y||h===O||h===s||h===S}u.isS=B;function o(h){return h>=65&&h<=90||h>=97&&h<=122||h===58||h===95||h===8204||h===8205||h>=192&&h<=214||h>=216&&h<=246||h>=248&&h<=767||h>=880&&h<=893||h>=895&&h<=8191||h>=8304&&h<=8591||h>=11264&&h<=12271||h>=12289&&h<=55295||h>=63744&&h<=64975||h>=65008&&h<=65533||h>=65536&&h<=983039}u.isNameStartChar=o;function T(h){return o(h)||h>=48&&h<=57||h===45||h===46||h===183||h>=768&&h<=879||h>=8255&&h<=8256}u.isNameChar=T}(Je)),Je}var et={},Dt;function wr(){return Dt||(Dt=1,function(u){/**
 * Character class utilities for XML NS 1.0 edition 3.
 *
 * <AUTHOR> Dubeau
 * @license MIT
 * @copyright Louis-Dominique Dubeau
 */Object.defineProperty(u,"__esModule",{value:!0}),u.NC_NAME_START_CHAR="A-Z_a-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-󯿿",u.NC_NAME_CHAR="-"+u.NC_NAME_START_CHAR+".0-9·̀-ͯ‿-⁀",u.NC_NAME_START_CHAR_RE=new RegExp("^["+u.NC_NAME_START_CHAR+"]$","u"),u.NC_NAME_CHAR_RE=new RegExp("^["+u.NC_NAME_CHAR+"]$","u"),u.NC_NAME_RE=new RegExp("^["+u.NC_NAME_START_CHAR+"]["+u.NC_NAME_CHAR+"]*$","u");function S(s){return s>=65&&s<=90||s===95||s>=97&&s<=122||s>=192&&s<=214||s>=216&&s<=246||s>=248&&s<=767||s>=880&&s<=893||s>=895&&s<=8191||s>=8204&&s<=8205||s>=8304&&s<=8591||s>=11264&&s<=12271||s>=12289&&s<=55295||s>=63744&&s<=64975||s>=65008&&s<=65533||s>=65536&&s<=983039}u.isNCNameStartChar=S;function O(s){return S(s)||s===45||s===46||s>=48&&s<=57||s===183||s>=768&&s<=879||s>=8255&&s<=8256}u.isNCNameChar=O}(et)),et}var Rt;function yr(){if(Rt)return Ue;Rt=1,Object.defineProperty(Ue,"__esModule",{value:!0});const u=_r(),S=gr(),O=wr();var s=u.isS,y=u.isChar,C=u.isNameStartChar,g=u.isNameChar,a=u.S_LIST,B=u.NAME_RE,o=S.isChar,T=O.isNCNameStartChar,h=O.isNCNameChar,z=O.NC_NAME_RE;const k="http://www.w3.org/XML/1998/namespace",I="http://www.w3.org/2000/xmlns/",w={__proto__:null,xml:k,xmlns:I},m={__proto__:null,amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},p=-1,F=-2,D=0,E=1,f=2,b=3,L=4,U=5,W=6,P=7,$=8,c=9,d=10,R=11,v=12,M=13,q=14,j=15,K=16,G=17,V=18,X=19,se=20,ae=21,fe=22,Ee=23,re=24,Z=25,ne=26,ce=27,le=28,we=29,oe=30,Se=31,ye=32,r=33,e=34,t=35,n=36,l=37,A=38,N=39,Q=40,J=41,te=42,ue=43,ie=44,ve=9,me=10,Fe=13,ot=32,ht=33,ct=34,Le=38,ut=39,Me=45,Oe=47,Yt=59,be=60,Te=61,he=62,de=63,ft=91,Ae=93,lt=133,dt=8232,Pe=pe=>pe===ct||pe===ut,xt=[ct,ut],Gt=[...xt,ft,he],Qt=[...xt,be,Ae],Kt=[Te,de,...a],Zt=[...a,he,Le,be];function ze(pe,i,x){switch(i){case"xml":x!==k&&pe.fail(`xml prefix must be bound to ${k}.`);break;case"xmlns":x!==I&&pe.fail(`xmlns prefix must be bound to ${I}.`);break}switch(x){case I:pe.fail(i===""?`the default namespace may not be set to ${x}.`:`may not assign a prefix (even "xmlns") to the URI ${I}.`);break;case k:switch(i){case"xml":break;case"":pe.fail(`the default namespace may not be set to ${x}.`);break;default:pe.fail("may not assign the xml namespace to another prefix.")}break}}function Jt(pe,i){for(const x of Object.keys(i))ze(pe,x,i[x])}const er=pe=>z.test(pe),tr=pe=>B.test(pe),Ce=0,pt=1,qe=2;Ue.EVENTS=["xmldecl","text","processinginstruction","doctype","comment","opentagstart","attribute","opentag","closetag","cdata","error","end","ready"];const mt={xmldecl:"xmldeclHandler",text:"textHandler",processinginstruction:"piHandler",doctype:"doctypeHandler",comment:"commentHandler",opentagstart:"openTagStartHandler",attribute:"attributeHandler",opentag:"openTagHandler",closetag:"closeTagHandler",cdata:"cdataHandler",error:"errorHandler",end:"endHandler",ready:"readyHandler"};class rr{constructor(i){this.opt=i??{},this.fragmentOpt=!!this.opt.fragment;const x=this.xmlnsOpt=!!this.opt.xmlns;if(this.trackPosition=this.opt.position!==!1,this.fileName=this.opt.fileName,x){this.nameStartCheck=T,this.nameCheck=h,this.isName=er,this.processAttribs=this.processAttribsNS,this.pushAttrib=this.pushAttribNS,this.ns=Object.assign({__proto__:null},w);const _=this.opt.additionalNamespaces;_!=null&&(Jt(this,_),Object.assign(this.ns,_))}else this.nameStartCheck=C,this.nameCheck=g,this.isName=tr,this.processAttribs=this.processAttribsPlain,this.pushAttrib=this.pushAttribPlain;this.stateTable=[this.sBegin,this.sBeginWhitespace,this.sDoctype,this.sDoctypeQuote,this.sDTD,this.sDTDQuoted,this.sDTDOpenWaka,this.sDTDOpenWakaBang,this.sDTDComment,this.sDTDCommentEnding,this.sDTDCommentEnded,this.sDTDPI,this.sDTDPIEnding,this.sText,this.sEntity,this.sOpenWaka,this.sOpenWakaBang,this.sComment,this.sCommentEnding,this.sCommentEnded,this.sCData,this.sCDataEnding,this.sCDataEnding2,this.sPIFirstChar,this.sPIRest,this.sPIBody,this.sPIEnding,this.sXMLDeclNameStart,this.sXMLDeclName,this.sXMLDeclEq,this.sXMLDeclValueStart,this.sXMLDeclValue,this.sXMLDeclSeparator,this.sXMLDeclEnding,this.sOpenTag,this.sOpenTagSlash,this.sAttrib,this.sAttribName,this.sAttribNameSawWhite,this.sAttribValue,this.sAttribValueQuoted,this.sAttribValueClosed,this.sAttribValueUnquoted,this.sCloseTag,this.sCloseTagSawWhite],this._init()}get closed(){return this._closed}_init(){var i;this.openWakaBang="",this.text="",this.name="",this.piTarget="",this.entity="",this.q=null,this.tags=[],this.tag=null,this.topNS=null,this.chunk="",this.chunkPosition=0,this.i=0,this.prevI=0,this.carriedFromPrevious=void 0,this.forbiddenState=Ce,this.attribList=[];const{fragmentOpt:x}=this;this.state=x?M:D,this.reportedTextBeforeRoot=this.reportedTextAfterRoot=this.closedRoot=this.sawRoot=x,this.xmlDeclPossible=!x,this.xmlDeclExpects=["version"],this.entityReturnState=void 0;let{defaultXMLVersion:_}=this.opt;if(_===void 0){if(this.opt.forceXMLVersion===!0)throw new Error("forceXMLVersion set but defaultXMLVersion is not set");_="1.0"}this.setXMLVersion(_),this.positionAtNewLine=0,this.doctype=!1,this._closed=!1,this.xmlDecl={version:void 0,encoding:void 0,standalone:void 0},this.line=1,this.column=0,this.ENTITIES=Object.create(m),(i=this.readyHandler)===null||i===void 0||i.call(this)}get position(){return this.chunkPosition+this.i}get columnIndex(){return this.position-this.positionAtNewLine}on(i,x){this[mt[i]]=x}off(i){this[mt[i]]=void 0}makeError(i){var x;let _=(x=this.fileName)!==null&&x!==void 0?x:"";return this.trackPosition&&(_.length>0&&(_+=":"),_+=`${this.line}:${this.column}`),_.length>0&&(_+=": "),new Error(_+i)}fail(i){const x=this.makeError(i),_=this.errorHandler;if(_===void 0)throw x;return _(x),this}write(i){if(this.closed)return this.fail("cannot write after close; assign an onready handler.");let x=!1;i===null?(x=!0,i=""):typeof i=="object"&&(i=i.toString()),this.carriedFromPrevious!==void 0&&(i=`${this.carriedFromPrevious}${i}`,this.carriedFromPrevious=void 0);let _=i.length;const H=i.charCodeAt(_-1);!x&&(H===Fe||H>=55296&&H<=56319)&&(this.carriedFromPrevious=i[_-1],_--,i=i.slice(0,_));const{stateTable:Y}=this;for(this.chunk=i,this.i=0;this.i<_;)Y[this.state].call(this);return this.chunkPosition+=_,x?this.end():this}close(){return this.write(null)}getCode10(){const{chunk:i,i:x}=this;if(this.prevI=x,this.i=x+1,x>=i.length)return p;const _=i.charCodeAt(x);if(this.column++,_<55296){if(_>=ot||_===ve)return _;switch(_){case me:return this.line++,this.column=0,this.positionAtNewLine=this.position,me;case Fe:return i.charCodeAt(x+1)===me&&(this.i=x+2),this.line++,this.column=0,this.positionAtNewLine=this.position,F;default:return this.fail("disallowed character."),_}}if(_>56319)return _>=57344&&_<=65533||this.fail("disallowed character."),_;const H=65536+(_-55296)*1024+(i.charCodeAt(x+1)-56320);return this.i=x+2,H>1114111&&this.fail("disallowed character."),H}getCode11(){const{chunk:i,i:x}=this;if(this.prevI=x,this.i=x+1,x>=i.length)return p;const _=i.charCodeAt(x);if(this.column++,_<55296){if(_>31&&_<127||_>159&&_!==dt||_===ve)return _;switch(_){case me:return this.line++,this.column=0,this.positionAtNewLine=this.position,me;case Fe:{const Y=i.charCodeAt(x+1);(Y===me||Y===lt)&&(this.i=x+2)}case lt:case dt:return this.line++,this.column=0,this.positionAtNewLine=this.position,F;default:return this.fail("disallowed character."),_}}if(_>56319)return _>=57344&&_<=65533||this.fail("disallowed character."),_;const H=65536+(_-55296)*1024+(i.charCodeAt(x+1)-56320);return this.i=x+2,H>1114111&&this.fail("disallowed character."),H}getCodeNorm(){const i=this.getCode();return i===F?me:i}unget(){this.i=this.prevI,this.column--}captureTo(i){let{i:x}=this;const{chunk:_}=this;for(;;){const H=this.getCode(),Y=H===F,ee=Y?me:H;if(ee===p||i.includes(ee))return this.text+=_.slice(x,this.prevI),ee;Y&&(this.text+=`${_.slice(x,this.prevI)}
`,x=this.i)}}captureToChar(i){let{i:x}=this;const{chunk:_}=this;for(;;){let H=this.getCode();switch(H){case F:this.text+=`${_.slice(x,this.prevI)}
`,x=this.i,H=me;break;case p:return this.text+=_.slice(x),!1}if(H===i)return this.text+=_.slice(x,this.prevI),!0}}captureNameChars(){const{chunk:i,i:x}=this;for(;;){const _=this.getCode();if(_===p)return this.name+=i.slice(x),p;if(!g(_))return this.name+=i.slice(x,this.prevI),_===F?me:_}}skipSpaces(){for(;;){const i=this.getCodeNorm();if(i===p||!s(i))return i}}setXMLVersion(i){this.currentXMLVersion=i,i==="1.0"?(this.isChar=y,this.getCode=this.getCode10):(this.isChar=o,this.getCode=this.getCode11)}sBegin(){this.chunk.charCodeAt(0)===65279&&(this.i++,this.column++),this.state=E}sBeginWhitespace(){const i=this.i,x=this.skipSpaces();switch(this.prevI!==i&&(this.xmlDeclPossible=!1),x){case be:if(this.state=j,this.text.length!==0)throw new Error("no-empty text at start");break;case p:break;default:this.unget(),this.state=M,this.xmlDeclPossible=!1}}sDoctype(){var i;const x=this.captureTo(Gt);switch(x){case he:{(i=this.doctypeHandler)===null||i===void 0||i.call(this,this.text),this.text="",this.state=M,this.doctype=!0;break}case p:break;default:this.text+=String.fromCodePoint(x),x===ft?this.state=L:Pe(x)&&(this.state=b,this.q=x)}}sDoctypeQuote(){const i=this.q;this.captureToChar(i)&&(this.text+=String.fromCodePoint(i),this.q=null,this.state=f)}sDTD(){const i=this.captureTo(Qt);i!==p&&(this.text+=String.fromCodePoint(i),i===Ae?this.state=f:i===be?this.state=W:Pe(i)&&(this.state=U,this.q=i))}sDTDQuoted(){const i=this.q;this.captureToChar(i)&&(this.text+=String.fromCodePoint(i),this.state=L,this.q=null)}sDTDOpenWaka(){const i=this.getCodeNorm();switch(this.text+=String.fromCodePoint(i),i){case ht:this.state=P,this.openWakaBang="";break;case de:this.state=R;break;default:this.state=L}}sDTDOpenWakaBang(){const i=String.fromCodePoint(this.getCodeNorm()),x=this.openWakaBang+=i;this.text+=i,x!=="-"&&(this.state=x==="--"?$:L,this.openWakaBang="")}sDTDComment(){this.captureToChar(Me)&&(this.text+="-",this.state=c)}sDTDCommentEnding(){const i=this.getCodeNorm();this.text+=String.fromCodePoint(i),this.state=i===Me?d:$}sDTDCommentEnded(){const i=this.getCodeNorm();this.text+=String.fromCodePoint(i),i===he?this.state=L:(this.fail("malformed comment."),this.state=$)}sDTDPI(){this.captureToChar(de)&&(this.text+="?",this.state=v)}sDTDPIEnding(){const i=this.getCodeNorm();this.text+=String.fromCodePoint(i),i===he&&(this.state=L)}sText(){this.tags.length!==0?this.handleTextInRoot():this.handleTextOutsideRoot()}sEntity(){let{i}=this;const{chunk:x}=this;e:for(;;)switch(this.getCode()){case F:this.entity+=`${x.slice(i,this.prevI)}
`,i=this.i;break;case Yt:{const{entityReturnState:_}=this,H=this.entity+x.slice(i,this.prevI);this.state=_;let Y;H===""?(this.fail("empty entity name."),Y="&;"):(Y=this.parseEntity(H),this.entity=""),(_!==M||this.textHandler!==void 0)&&(this.text+=Y);break e}case p:this.entity+=x.slice(i);break e}}sOpenWaka(){const i=this.getCode();if(C(i))this.state=e,this.unget(),this.xmlDeclPossible=!1;else switch(i){case Oe:this.state=ue,this.xmlDeclPossible=!1;break;case ht:this.state=K,this.openWakaBang="",this.xmlDeclPossible=!1;break;case de:this.state=Ee;break;default:this.fail("disallowed character in tag name"),this.state=M,this.xmlDeclPossible=!1}}sOpenWakaBang(){switch(this.openWakaBang+=String.fromCodePoint(this.getCodeNorm()),this.openWakaBang){case"[CDATA[":!this.sawRoot&&!this.reportedTextBeforeRoot&&(this.fail("text data outside of root node."),this.reportedTextBeforeRoot=!0),this.closedRoot&&!this.reportedTextAfterRoot&&(this.fail("text data outside of root node."),this.reportedTextAfterRoot=!0),this.state=se,this.openWakaBang="";break;case"--":this.state=G,this.openWakaBang="";break;case"DOCTYPE":this.state=f,(this.doctype||this.sawRoot)&&this.fail("inappropriately located doctype declaration."),this.openWakaBang="";break;default:this.openWakaBang.length>=7&&this.fail("incorrect syntax.")}}sComment(){this.captureToChar(Me)&&(this.state=V)}sCommentEnding(){var i;const x=this.getCodeNorm();x===Me?(this.state=X,(i=this.commentHandler)===null||i===void 0||i.call(this,this.text),this.text=""):(this.text+=`-${String.fromCodePoint(x)}`,this.state=G)}sCommentEnded(){const i=this.getCodeNorm();i!==he?(this.fail("malformed comment."),this.text+=`--${String.fromCodePoint(i)}`,this.state=G):this.state=M}sCData(){this.captureToChar(Ae)&&(this.state=ae)}sCDataEnding(){const i=this.getCodeNorm();i===Ae?this.state=fe:(this.text+=`]${String.fromCodePoint(i)}`,this.state=se)}sCDataEnding2(){var i;const x=this.getCodeNorm();switch(x){case he:{(i=this.cdataHandler)===null||i===void 0||i.call(this,this.text),this.text="",this.state=M;break}case Ae:this.text+="]";break;default:this.text+=`]]${String.fromCodePoint(x)}`,this.state=se}}sPIFirstChar(){const i=this.getCodeNorm();this.nameStartCheck(i)?(this.piTarget+=String.fromCodePoint(i),this.state=re):i===de||s(i)?(this.fail("processing instruction without a target."),this.state=i===de?ne:Z):(this.fail("disallowed character in processing instruction name."),this.piTarget+=String.fromCodePoint(i),this.state=re)}sPIRest(){const{chunk:i,i:x}=this;for(;;){const _=this.getCodeNorm();if(_===p){this.piTarget+=i.slice(x);return}if(!this.nameCheck(_)){this.piTarget+=i.slice(x,this.prevI);const H=_===de;H||s(_)?this.piTarget==="xml"?(this.xmlDeclPossible||this.fail("an XML declaration must be at the start of the document."),this.state=H?r:ce):this.state=H?ne:Z:(this.fail("disallowed character in processing instruction name."),this.piTarget+=String.fromCodePoint(_));break}}}sPIBody(){if(this.text.length===0){const i=this.getCodeNorm();i===de?this.state=ne:s(i)||(this.text=String.fromCodePoint(i))}else this.captureToChar(de)&&(this.state=ne)}sPIEnding(){var i;const x=this.getCodeNorm();if(x===he){const{piTarget:_}=this;_.toLowerCase()==="xml"&&this.fail("the XML declaration must appear at the start of the document."),(i=this.piHandler)===null||i===void 0||i.call(this,{target:_,body:this.text}),this.piTarget=this.text="",this.state=M}else x===de?this.text+="?":(this.text+=`?${String.fromCodePoint(x)}`,this.state=Z);this.xmlDeclPossible=!1}sXMLDeclNameStart(){const i=this.skipSpaces();if(i===de){this.state=r;return}i!==p&&(this.state=le,this.name=String.fromCodePoint(i))}sXMLDeclName(){const i=this.captureTo(Kt);if(i===de){this.state=r,this.name+=this.text,this.text="",this.fail("XML declaration is incomplete.");return}if(s(i)||i===Te){if(this.name+=this.text,this.text="",!this.xmlDeclExpects.includes(this.name))switch(this.name.length){case 0:this.fail("did not expect any more name/value pairs.");break;case 1:this.fail(`expected the name ${this.xmlDeclExpects[0]}.`);break;default:this.fail(`expected one of ${this.xmlDeclExpects.join(", ")}`)}this.state=i===Te?oe:we}}sXMLDeclEq(){const i=this.getCodeNorm();if(i===de){this.state=r,this.fail("XML declaration is incomplete.");return}s(i)||(i!==Te&&this.fail("value required."),this.state=oe)}sXMLDeclValueStart(){const i=this.getCodeNorm();if(i===de){this.state=r,this.fail("XML declaration is incomplete.");return}s(i)||(Pe(i)?this.q=i:(this.fail("value must be quoted."),this.q=ot),this.state=Se)}sXMLDeclValue(){const i=this.captureTo([this.q,de]);if(i===de){this.state=r,this.text="",this.fail("XML declaration is incomplete.");return}if(i===p)return;const x=this.text;switch(this.text="",this.name){case"version":{this.xmlDeclExpects=["encoding","standalone"];const _=x;this.xmlDecl.version=_,/^1\.[0-9]+$/.test(_)?this.opt.forceXMLVersion||this.setXMLVersion(_):this.fail("version number must match /^1\\.[0-9]+$/.");break}case"encoding":/^[A-Za-z][A-Za-z0-9._-]*$/.test(x)||this.fail("encoding value must match 	/^[A-Za-z0-9][A-Za-z0-9._-]*$/."),this.xmlDeclExpects=["standalone"],this.xmlDecl.encoding=x;break;case"standalone":x!=="yes"&&x!=="no"&&this.fail('standalone value must match "yes" or "no".'),this.xmlDeclExpects=[],this.xmlDecl.standalone=x;break}this.name="",this.state=ye}sXMLDeclSeparator(){const i=this.getCodeNorm();if(i===de){this.state=r;return}s(i)||(this.fail("whitespace required."),this.unget()),this.state=ce}sXMLDeclEnding(){var i;this.getCodeNorm()===he?(this.piTarget!=="xml"?this.fail("processing instructions are not allowed before root."):this.name!=="version"&&this.xmlDeclExpects.includes("version")&&this.fail("XML declaration must contain a version."),(i=this.xmldeclHandler)===null||i===void 0||i.call(this,this.xmlDecl),this.name="",this.piTarget=this.text="",this.state=M):this.fail("The character ? is disallowed anywhere in XML declarations."),this.xmlDeclPossible=!1}sOpenTag(){var i;const x=this.captureNameChars();if(x===p)return;const _=this.tag={name:this.name,attributes:Object.create(null)};switch(this.name="",this.xmlnsOpt&&(this.topNS=_.ns=Object.create(null)),(i=this.openTagStartHandler)===null||i===void 0||i.call(this,_),this.sawRoot=!0,!this.fragmentOpt&&this.closedRoot&&this.fail("documents may contain only one root."),x){case he:this.openTag();break;case Oe:this.state=t;break;default:s(x)||this.fail("disallowed character in tag name."),this.state=n}}sOpenTagSlash(){this.getCode()===he?this.openSelfClosingTag():(this.fail("forward-slash in opening tag not followed by >."),this.state=n)}sAttrib(){const i=this.skipSpaces();i!==p&&(C(i)?(this.unget(),this.state=l):i===he?this.openTag():i===Oe?this.state=t:this.fail("disallowed character in attribute name."))}sAttribName(){const i=this.captureNameChars();i===Te?this.state=N:s(i)?this.state=A:i===he?(this.fail("attribute without value."),this.pushAttrib(this.name,this.name),this.name=this.text="",this.openTag()):i!==p&&this.fail("disallowed character in attribute name.")}sAttribNameSawWhite(){const i=this.skipSpaces();switch(i){case p:return;case Te:this.state=N;break;default:this.fail("attribute without value."),this.text="",this.name="",i===he?this.openTag():C(i)?(this.unget(),this.state=l):(this.fail("disallowed character in attribute name."),this.state=n)}}sAttribValue(){const i=this.getCodeNorm();Pe(i)?(this.q=i,this.state=Q):s(i)||(this.fail("unquoted attribute value."),this.state=te,this.unget())}sAttribValueQuoted(){const{q:i,chunk:x}=this;let{i:_}=this;for(;;)switch(this.getCode()){case i:this.pushAttrib(this.name,this.text+x.slice(_,this.prevI)),this.name=this.text="",this.q=null,this.state=J;return;case Le:this.text+=x.slice(_,this.prevI),this.state=q,this.entityReturnState=Q;return;case me:case F:case ve:this.text+=`${x.slice(_,this.prevI)} `,_=this.i;break;case be:this.text+=x.slice(_,this.prevI),this.fail("disallowed character.");return;case p:this.text+=x.slice(_);return}}sAttribValueClosed(){const i=this.getCodeNorm();s(i)?this.state=n:i===he?this.openTag():i===Oe?this.state=t:C(i)?(this.fail("no whitespace between attributes."),this.unget(),this.state=l):this.fail("disallowed character in attribute name.")}sAttribValueUnquoted(){const i=this.captureTo(Zt);switch(i){case Le:this.state=q,this.entityReturnState=te;break;case be:this.fail("disallowed character.");break;case p:break;default:this.text.includes("]]>")&&this.fail('the string "]]>" is disallowed in char data.'),this.pushAttrib(this.name,this.text),this.name=this.text="",i===he?this.openTag():this.state=n}}sCloseTag(){const i=this.captureNameChars();i===he?this.closeTag():s(i)?this.state=ie:i!==p&&this.fail("disallowed character in closing tag.")}sCloseTagSawWhite(){switch(this.skipSpaces()){case he:this.closeTag();break;case p:break;default:this.fail("disallowed character in closing tag.")}}handleTextInRoot(){let{i,forbiddenState:x}=this;const{chunk:_,textHandler:H}=this;e:for(;;)switch(this.getCode()){case be:{if(this.state=j,H!==void 0){const{text:Y}=this,ee=_.slice(i,this.prevI);Y.length!==0?(H(Y+ee),this.text=""):ee.length!==0&&H(ee)}x=Ce;break e}case Le:this.state=q,this.entityReturnState=M,H!==void 0&&(this.text+=_.slice(i,this.prevI)),x=Ce;break e;case Ae:switch(x){case Ce:x=pt;break;case pt:x=qe;break;case qe:break;default:throw new Error("impossible state")}break;case he:x===qe&&this.fail('the string "]]>" is disallowed in char data.'),x=Ce;break;case F:H!==void 0&&(this.text+=`${_.slice(i,this.prevI)}
`),i=this.i,x=Ce;break;case p:H!==void 0&&(this.text+=_.slice(i));break e;default:x=Ce}this.forbiddenState=x}handleTextOutsideRoot(){let{i}=this;const{chunk:x,textHandler:_}=this;let H=!1;e:for(;;){const Y=this.getCode();switch(Y){case be:{if(this.state=j,_!==void 0){const{text:ee}=this,xe=x.slice(i,this.prevI);ee.length!==0?(_(ee+xe),this.text=""):xe.length!==0&&_(xe)}break e}case Le:this.state=q,this.entityReturnState=M,_!==void 0&&(this.text+=x.slice(i,this.prevI)),H=!0;break e;case F:_!==void 0&&(this.text+=`${x.slice(i,this.prevI)}
`),i=this.i;break;case p:_!==void 0&&(this.text+=x.slice(i));break e;default:s(Y)||(H=!0)}}H&&(!this.sawRoot&&!this.reportedTextBeforeRoot&&(this.fail("text data outside of root node."),this.reportedTextBeforeRoot=!0),this.closedRoot&&!this.reportedTextAfterRoot&&(this.fail("text data outside of root node."),this.reportedTextAfterRoot=!0))}pushAttribNS(i,x){var _;const{prefix:H,local:Y}=this.qname(i),ee={name:i,prefix:H,local:Y,value:x};if(this.attribList.push(ee),(_=this.attributeHandler)===null||_===void 0||_.call(this,ee),H==="xmlns"){const xe=x.trim();this.currentXMLVersion==="1.0"&&xe===""&&this.fail("invalid attempt to undefine prefix in XML 1.0"),this.topNS[Y]=xe,ze(this,Y,xe)}else if(i==="xmlns"){const xe=x.trim();this.topNS[""]=xe,ze(this,"",xe)}}pushAttribPlain(i,x){var _;const H={name:i,value:x};this.attribList.push(H),(_=this.attributeHandler)===null||_===void 0||_.call(this,H)}end(){var i,x;this.sawRoot||this.fail("document must contain a root element.");const{tags:_}=this;for(;_.length>0;){const Y=_.pop();this.fail(`unclosed tag: ${Y.name}`)}this.state!==D&&this.state!==M&&this.fail("unexpected end.");const{text:H}=this;return H.length!==0&&((i=this.textHandler)===null||i===void 0||i.call(this,H),this.text=""),this._closed=!0,(x=this.endHandler)===null||x===void 0||x.call(this),this._init(),this}resolve(i){var x,_;let H=this.topNS[i];if(H!==void 0)return H;const{tags:Y}=this;for(let ee=Y.length-1;ee>=0;ee--)if(H=Y[ee].ns[i],H!==void 0)return H;return H=this.ns[i],H!==void 0?H:(_=(x=this.opt).resolvePrefix)===null||_===void 0?void 0:_.call(x,i)}qname(i){const x=i.indexOf(":");if(x===-1)return{prefix:"",local:i};const _=i.slice(x+1),H=i.slice(0,x);return(H===""||_===""||_.includes(":"))&&this.fail(`malformed name: ${i}.`),{prefix:H,local:_}}processAttribsNS(){var i;const{attribList:x}=this,_=this.tag;{const{prefix:ee,local:xe}=this.qname(_.name);_.prefix=ee,_.local=xe;const Ie=_.uri=(i=this.resolve(ee))!==null&&i!==void 0?i:"";ee!==""&&(ee==="xmlns"&&this.fail('tags may not have "xmlns" as prefix.'),Ie===""&&(this.fail(`unbound namespace prefix: ${JSON.stringify(ee)}.`),_.uri=ee))}if(x.length===0)return;const{attributes:H}=_,Y=new Set;for(const ee of x){const{name:xe,prefix:Ie,local:ir}=ee;let De,Ne;Ie===""?(De=xe==="xmlns"?I:"",Ne=xe):(De=this.resolve(Ie),De===void 0&&(this.fail(`unbound namespace prefix: ${JSON.stringify(Ie)}.`),De=Ie),Ne=`{${De}}${ir}`),Y.has(Ne)&&this.fail(`duplicate attribute: ${Ne}.`),Y.add(Ne),ee.uri=De,H[xe]=ee}this.attribList=[]}processAttribsPlain(){const{attribList:i}=this,x=this.tag.attributes;for(const{name:_,value:H}of i)x[_]!==void 0&&this.fail(`duplicate attribute: ${_}.`),x[_]=H;this.attribList=[]}openTag(){var i;this.processAttribs();const{tags:x}=this,_=this.tag;_.isSelfClosing=!1,(i=this.openTagHandler)===null||i===void 0||i.call(this,_),x.push(_),this.state=M,this.name=""}openSelfClosingTag(){var i,x,_;this.processAttribs();const{tags:H}=this,Y=this.tag;Y.isSelfClosing=!0,(i=this.openTagHandler)===null||i===void 0||i.call(this,Y),(x=this.closeTagHandler)===null||x===void 0||x.call(this,Y),(this.tag=(_=H[H.length-1])!==null&&_!==void 0?_:null)===null&&(this.closedRoot=!0),this.state=M,this.name=""}closeTag(){const{tags:i,name:x}=this;if(this.state=M,this.name="",x===""){this.fail("weird empty close tag."),this.text+="</>";return}const _=this.closeTagHandler;let H=i.length;for(;H-- >0;){const Y=this.tag=i.pop();if(this.topNS=Y.ns,_?.(Y),Y.name===x)break;this.fail("unexpected close tag.")}H===0?this.closedRoot=!0:H<0&&(this.fail(`unmatched closing tag: ${x}.`),this.text+=`</${x}>`)}parseEntity(i){if(i[0]!=="#"){const _=this.ENTITIES[i];return _!==void 0?_:(this.fail(this.isName(i)?"undefined entity.":"disallowed character in entity name."),`&${i};`)}let x=NaN;return i[1]==="x"&&/^#x[0-9a-f]+$/i.test(i)?x=parseInt(i.slice(2),16):/^#[0-9]+$/.test(i)&&(x=parseInt(i.slice(1),10)),this.isChar(x)?String.fromCodePoint(x):(this.fail("malformed character entity."),`&${i};`)}}return Ue.SaxesParser=rr,Ue}var ge={},Re={},tt,Lt;function vr(){if(Lt)return tt;Lt=1,tt=u;function u(){this.pending=0,this.max=1/0,this.listeners=[],this.waiting=[],this.error=null}u.prototype.go=function(s){this.pending<this.max?O(this,s):this.waiting.push(s)},u.prototype.wait=function(s){this.pending===0?s(this.error):this.listeners.push(s)},u.prototype.hold=function(){return S(this)};function S(s){s.pending+=1;var y=!1;return C;function C(a){if(y)throw new Error("callback called twice");if(y=!0,s.error=s.error||a,s.pending-=1,s.waiting.length>0&&s.pending<s.max)O(s,s.waiting.shift());else if(s.pending===0){var B=s.listeners;s.listeners=[],B.forEach(g)}}function g(a){a(s.error)}}function O(s,y){y(S(s))}return tt}var He={exports:{}},Nt;function $t(){if(Nt)return He.exports;Nt=1;var u=typeof Reflect=="object"?Reflect:null,S=u&&typeof u.apply=="function"?u.apply:function(f,b,L){return Function.prototype.apply.call(f,b,L)},O;u&&typeof u.ownKeys=="function"?O=u.ownKeys:Object.getOwnPropertySymbols?O=function(f){return Object.getOwnPropertyNames(f).concat(Object.getOwnPropertySymbols(f))}:O=function(f){return Object.getOwnPropertyNames(f)};function s(E){console&&console.warn&&console.warn(E)}var y=Number.isNaN||function(f){return f!==f};function C(){C.init.call(this)}He.exports=C,He.exports.once=p,C.EventEmitter=C,C.prototype._events=void 0,C.prototype._eventsCount=0,C.prototype._maxListeners=void 0;var g=10;function a(E){if(typeof E!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof E)}Object.defineProperty(C,"defaultMaxListeners",{enumerable:!0,get:function(){return g},set:function(E){if(typeof E!="number"||E<0||y(E))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+E+".");g=E}}),C.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},C.prototype.setMaxListeners=function(f){if(typeof f!="number"||f<0||y(f))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+f+".");return this._maxListeners=f,this};function B(E){return E._maxListeners===void 0?C.defaultMaxListeners:E._maxListeners}C.prototype.getMaxListeners=function(){return B(this)},C.prototype.emit=function(f){for(var b=[],L=1;L<arguments.length;L++)b.push(arguments[L]);var U=f==="error",W=this._events;if(W!==void 0)U=U&&W.error===void 0;else if(!U)return!1;if(U){var P;if(b.length>0&&(P=b[0]),P instanceof Error)throw P;var $=new Error("Unhandled error."+(P?" ("+P.message+")":""));throw $.context=P,$}var c=W[f];if(c===void 0)return!1;if(typeof c=="function")S(c,this,b);else for(var d=c.length,R=I(c,d),L=0;L<d;++L)S(R[L],this,b);return!0};function o(E,f,b,L){var U,W,P;if(a(b),W=E._events,W===void 0?(W=E._events=Object.create(null),E._eventsCount=0):(W.newListener!==void 0&&(E.emit("newListener",f,b.listener?b.listener:b),W=E._events),P=W[f]),P===void 0)P=W[f]=b,++E._eventsCount;else if(typeof P=="function"?P=W[f]=L?[b,P]:[P,b]:L?P.unshift(b):P.push(b),U=B(E),U>0&&P.length>U&&!P.warned){P.warned=!0;var $=new Error("Possible EventEmitter memory leak detected. "+P.length+" "+String(f)+" listeners added. Use emitter.setMaxListeners() to increase limit");$.name="MaxListenersExceededWarning",$.emitter=E,$.type=f,$.count=P.length,s($)}return E}C.prototype.addListener=function(f,b){return o(this,f,b,!1)},C.prototype.on=C.prototype.addListener,C.prototype.prependListener=function(f,b){return o(this,f,b,!0)};function T(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(E,f,b){var L={fired:!1,wrapFn:void 0,target:E,type:f,listener:b},U=T.bind(L);return U.listener=b,L.wrapFn=U,U}C.prototype.once=function(f,b){return a(b),this.on(f,h(this,f,b)),this},C.prototype.prependOnceListener=function(f,b){return a(b),this.prependListener(f,h(this,f,b)),this},C.prototype.removeListener=function(f,b){var L,U,W,P,$;if(a(b),U=this._events,U===void 0)return this;if(L=U[f],L===void 0)return this;if(L===b||L.listener===b)--this._eventsCount===0?this._events=Object.create(null):(delete U[f],U.removeListener&&this.emit("removeListener",f,L.listener||b));else if(typeof L!="function"){for(W=-1,P=L.length-1;P>=0;P--)if(L[P]===b||L[P].listener===b){$=L[P].listener,W=P;break}if(W<0)return this;W===0?L.shift():w(L,W),L.length===1&&(U[f]=L[0]),U.removeListener!==void 0&&this.emit("removeListener",f,$||b)}return this},C.prototype.off=C.prototype.removeListener,C.prototype.removeAllListeners=function(f){var b,L,U;if(L=this._events,L===void 0)return this;if(L.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):L[f]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete L[f]),this;if(arguments.length===0){var W=Object.keys(L),P;for(U=0;U<W.length;++U)P=W[U],P!=="removeListener"&&this.removeAllListeners(P);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(b=L[f],typeof b=="function")this.removeListener(f,b);else if(b!==void 0)for(U=b.length-1;U>=0;U--)this.removeListener(f,b[U]);return this};function z(E,f,b){var L=E._events;if(L===void 0)return[];var U=L[f];return U===void 0?[]:typeof U=="function"?b?[U.listener||U]:[U]:b?m(U):I(U,U.length)}C.prototype.listeners=function(f){return z(this,f,!0)},C.prototype.rawListeners=function(f){return z(this,f,!1)},C.listenerCount=function(E,f){return typeof E.listenerCount=="function"?E.listenerCount(f):k.call(E,f)},C.prototype.listenerCount=k;function k(E){var f=this._events;if(f!==void 0){var b=f[E];if(typeof b=="function")return 1;if(b!==void 0)return b.length}return 0}C.prototype.eventNames=function(){return this._eventsCount>0?O(this._events):[]};function I(E,f){for(var b=new Array(f),L=0;L<f;++L)b[L]=E[L];return b}function w(E,f){for(;f+1<E.length;f++)E[f]=E[f+1];E.pop()}function m(E){for(var f=new Array(E.length),b=0;b<f.length;++b)f[b]=E[b].listener||E[b];return f}function p(E,f){return new Promise(function(b,L){function U(P){E.removeListener(f,W),L(P)}function W(){typeof E.removeListener=="function"&&E.removeListener("error",U),b([].slice.call(arguments))}D(E,f,W,{once:!0}),f!=="error"&&F(E,U,{once:!0})})}function F(E,f,b){typeof E.on=="function"&&D(E,"error",f,b)}function D(E,f,b,L){if(typeof E.on=="function")L.once?E.once(f,b):E.on(f,b);else if(typeof E.addEventListener=="function")E.addEventListener(f,function U(W){L.once&&E.removeEventListener(f,U),b(W)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof E)}return He.exports}var Bt;function br(){if(Bt)return Re;Bt=1;var u=_e,S=_e,O=_e,s=O.Readable,y=O.Writable,C=O.PassThrough,g=vr(),a=$t().EventEmitter;Re.createFromBuffer=z,Re.createFromFd=k,Re.BufferSlicer=h,Re.FdSlicer=B,S.inherits(B,a);function B(I,w){w=w||{},a.call(this),this.fd=I,this.pend=new g,this.pend.max=1,this.refCount=0,this.autoClose=!!w.autoClose}B.prototype.read=function(I,w,m,p,F){var D=this;D.pend.go(function(E){u.read(D.fd,I,w,m,p,function(f,b,L){E(),F(f,b,L)})})},B.prototype.write=function(I,w,m,p,F){var D=this;D.pend.go(function(E){u.write(D.fd,I,w,m,p,function(f,b,L){E(),F(f,b,L)})})},B.prototype.createReadStream=function(I){return new o(this,I)},B.prototype.createWriteStream=function(I){return new T(this,I)},B.prototype.ref=function(){this.refCount+=1},B.prototype.unref=function(){var I=this;if(I.refCount-=1,I.refCount>0)return;if(I.refCount<0)throw new Error("invalid unref");I.autoClose&&u.close(I.fd,w);function w(m){m?I.emit("error",m):I.emit("close")}},S.inherits(o,s);function o(I,w){w=w||{},s.call(this,w),this.context=I,this.context.ref(),this.start=w.start||0,this.endOffset=w.end,this.pos=this.start,this.destroyed=!1}o.prototype._read=function(I){var w=this;if(!w.destroyed){var m=Math.min(w._readableState.highWaterMark,I);if(w.endOffset!=null&&(m=Math.min(m,w.endOffset-w.pos)),m<=0){w.destroyed=!0,w.push(null),w.context.unref();return}w.context.pend.go(function(p){if(w.destroyed)return p();var F=new Buffer(m);u.read(w.context.fd,F,0,m,w.pos,function(D,E){D?w.destroy(D):E===0?(w.destroyed=!0,w.push(null),w.context.unref()):(w.pos+=E,w.push(F.slice(0,E))),p()})})}},o.prototype.destroy=function(I){this.destroyed||(I=I||new Error("stream destroyed"),this.destroyed=!0,this.emit("error",I),this.context.unref())},S.inherits(T,y);function T(I,w){w=w||{},y.call(this,w),this.context=I,this.context.ref(),this.start=w.start||0,this.endOffset=w.end==null?1/0:+w.end,this.bytesWritten=0,this.pos=this.start,this.destroyed=!1,this.on("finish",this.destroy.bind(this))}T.prototype._write=function(I,w,m){var p=this;if(!p.destroyed){if(p.pos+I.length>p.endOffset){var F=new Error("maximum file length exceeded");F.code="ETOOBIG",p.destroy(),m(F);return}p.context.pend.go(function(D){if(p.destroyed)return D();u.write(p.context.fd,I,0,I.length,p.pos,function(E,f){E?(p.destroy(),D(),m(E)):(p.bytesWritten+=f,p.pos+=f,p.emit("progress"),D(),m())})})}},T.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.context.unref())},S.inherits(h,a);function h(I,w){a.call(this),w=w||{},this.refCount=0,this.buffer=I,this.maxChunkSize=w.maxChunkSize||Number.MAX_SAFE_INTEGER}h.prototype.read=function(I,w,m,p,F){var D=p+m,E=D-this.buffer.length,f=E>0?E:m;this.buffer.copy(I,w,p,D),setImmediate(function(){F(null,f)})},h.prototype.write=function(I,w,m,p,F){I.copy(this.buffer,p,w,w+m),setImmediate(function(){F(null,m,I)})},h.prototype.createReadStream=function(I){I=I||{};var w=new C(I);w.destroyed=!1,w.start=I.start||0,w.endOffset=I.end,w.pos=w.endOffset||this.buffer.length;for(var m=this.buffer.slice(w.start,w.pos),p=0;;){var F=p+this.maxChunkSize;if(F>=m.length){p<m.length&&w.write(m.slice(p,m.length));break}w.write(m.slice(p,F)),p=F}return w.end(),w.destroy=function(){w.destroyed=!0},w},h.prototype.createWriteStream=function(I){var w=this;I=I||{};var m=new y(I);return m.start=I.start||0,m.endOffset=I.end==null?this.buffer.length:+I.end,m.bytesWritten=0,m.pos=m.start,m.destroyed=!1,m._write=function(p,F,D){if(!m.destroyed){var E=m.pos+p.length;if(E>m.endOffset){var f=new Error("maximum file length exceeded");f.code="ETOOBIG",m.destroyed=!0,D(f);return}p.copy(w.buffer,m.pos,0,p.length),m.bytesWritten+=p.length,m.pos=E,m.emit("progress"),D()}},m.destroy=function(){m.destroyed=!0},m},h.prototype.ref=function(){this.refCount+=1},h.prototype.unref=function(){if(this.refCount-=1,this.refCount<0)throw new Error("invalid unref")};function z(I,w){return new h(I,w)}function k(I,w){return new B(I,w)}return Re}var rt,Ut;function Sr(){if(Ut)return rt;Ut=1;var u=zt().Buffer,S=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];typeof Int32Array<"u"&&(S=new Int32Array(S));function O(g){if(u.isBuffer(g))return g;var a=typeof u.alloc=="function"&&typeof u.from=="function";if(typeof g=="number")return a?u.alloc(g):new u(g);if(typeof g=="string")return a?u.from(g):new u(g);throw new Error("input must be buffer, number, or string, received "+typeof g)}function s(g){var a=O(4);return a.writeInt32BE(g,0),a}function y(g,a){g=O(g),u.isBuffer(a)&&(a=a.readUInt32BE(0));for(var B=~~a^-1,o=0;o<g.length;o++)B=S[(B^g[o])&255]^B>>>8;return B^-1}function C(){return s(y.apply(null,arguments))}return C.signed=function(){return y.apply(null,arguments)},C.unsigned=function(){return y.apply(null,arguments)>>>0},rt=C,rt}var Mt;function Cr(){if(Mt)return ge;Mt=1;var u=_e,S=_e,O=br(),s=Sr(),y=_e,C=$t().EventEmitter,g=_e.Transform,a=_e.PassThrough,B=_e.Writable;ge.open=o,ge.fromFd=T,ge.fromBuffer=h,ge.fromRandomAccessReader=z,ge.dosDateTimeToDate=p,ge.validateFileName=F,ge.ZipFile=k,ge.Entry=m,ge.RandomAccessReader=f;function o(c,d,R){typeof d=="function"&&(R=d,d=null),d==null&&(d={}),d.autoClose==null&&(d.autoClose=!0),d.lazyEntries==null&&(d.lazyEntries=!1),d.decodeStrings==null&&(d.decodeStrings=!0),d.validateEntrySizes==null&&(d.validateEntrySizes=!0),d.strictFileNames==null&&(d.strictFileNames=!1),R==null&&(R=$),u.open(c,"r",function(v,M){if(v)return R(v);T(M,d,function(q,j){q&&u.close(M,$),R(q,j)})})}function T(c,d,R){typeof d=="function"&&(R=d,d=null),d==null&&(d={}),d.autoClose==null&&(d.autoClose=!1),d.lazyEntries==null&&(d.lazyEntries=!1),d.decodeStrings==null&&(d.decodeStrings=!0),d.validateEntrySizes==null&&(d.validateEntrySizes=!0),d.strictFileNames==null&&(d.strictFileNames=!1),R==null&&(R=$),u.fstat(c,function(v,M){if(v)return R(v);var q=O.createFromFd(c,{autoClose:!0});z(q,M.size,d,R)})}function h(c,d,R){typeof d=="function"&&(R=d,d=null),d==null&&(d={}),d.autoClose=!1,d.lazyEntries==null&&(d.lazyEntries=!1),d.decodeStrings==null&&(d.decodeStrings=!0),d.validateEntrySizes==null&&(d.validateEntrySizes=!0),d.strictFileNames==null&&(d.strictFileNames=!1);var v=O.createFromBuffer(c,{maxChunkSize:65536});z(v,c.length,d,R)}function z(c,d,R,v){typeof R=="function"&&(v=R,R=null),R==null&&(R={}),R.autoClose==null&&(R.autoClose=!0),R.lazyEntries==null&&(R.lazyEntries=!1),R.decodeStrings==null&&(R.decodeStrings=!0);var M=!!R.decodeStrings;if(R.validateEntrySizes==null&&(R.validateEntrySizes=!0),R.strictFileNames==null&&(R.strictFileNames=!1),v==null&&(v=$),typeof d!="number")throw new Error("expected totalSize parameter to be a number");if(d>Number.MAX_SAFE_INTEGER)throw new Error("zip file too large. only file sizes up to 2^52 are supported due to JavaScript's Number type being an IEEE 754 double.");c.ref();var q=22,j=65535,K=Math.min(q+j,d),G=P(K),V=d-G.length;D(c,G,0,K,V,function(X){if(X)return v(X);for(var se=K-q;se>=0;se-=1)if(G.readUInt32LE(se)===101010256){var ae=G.slice(se),fe=ae.readUInt16LE(4);if(fe!==0)return v(new Error("multi-disk zip files are not supported: found disk number: "+fe));var Ee=ae.readUInt16LE(10),re=ae.readUInt32LE(16),Z=ae.readUInt16LE(20),ne=ae.length-q;if(Z!==ne)return v(new Error("invalid comment length. expected: "+ne+". found: "+Z));var ce=M?U(ae,22,ae.length,!1):ae.slice(22);if(!(Ee===65535||re===4294967295))return v(null,new k(c,re,d,Ee,ce,R.autoClose,R.lazyEntries,M,R.validateEntrySizes,R.strictFileNames));var le=P(20),we=V+se-le.length;D(c,le,0,le.length,we,function(oe){if(oe)return v(oe);if(le.readUInt32LE(0)!==117853008)return v(new Error("invalid zip64 end of central directory locator signature"));var Se=W(le,8),ye=P(56);D(c,ye,0,ye.length,Se,function(r){return r?v(r):ye.readUInt32LE(0)!==101075792?v(new Error("invalid zip64 end of central directory record signature")):(Ee=W(ye,32),re=W(ye,48),v(null,new k(c,re,d,Ee,ce,R.autoClose,R.lazyEntries,M,R.validateEntrySizes,R.strictFileNames)))})});return}v(new Error("end of central directory record signature not found"))})}y.inherits(k,C);function k(c,d,R,v,M,q,j,K,G,V){var X=this;C.call(X),X.reader=c,X.reader.on("error",function(se){w(X,se)}),X.reader.once("close",function(){X.emit("close")}),X.readEntryCursor=d,X.fileSize=R,X.entryCount=v,X.comment=M,X.entriesRead=0,X.autoClose=!!q,X.lazyEntries=!!j,X.decodeStrings=!!K,X.validateEntrySizes=!!G,X.strictFileNames=!!V,X.isOpen=!0,X.emittedError=!1,X.lazyEntries||X._readEntry()}k.prototype.close=function(){this.isOpen&&(this.isOpen=!1,this.reader.unref())};function I(c,d){c.autoClose&&c.close(),w(c,d)}function w(c,d){c.emittedError||(c.emittedError=!0,c.emit("error",d))}k.prototype.readEntry=function(){if(!this.lazyEntries)throw new Error("readEntry() called without lazyEntries:true");this._readEntry()},k.prototype._readEntry=function(){var c=this;if(c.entryCount===c.entriesRead){setImmediate(function(){c.autoClose&&c.close(),!c.emittedError&&c.emit("end")});return}if(!c.emittedError){var d=P(46);D(c.reader,d,0,d.length,c.readEntryCursor,function(R){if(R)return I(c,R);if(!c.emittedError){var v=new m,M=d.readUInt32LE(0);if(M!==33639248)return I(c,new Error("invalid central directory file header signature: 0x"+M.toString(16)));if(v.versionMadeBy=d.readUInt16LE(4),v.versionNeededToExtract=d.readUInt16LE(6),v.generalPurposeBitFlag=d.readUInt16LE(8),v.compressionMethod=d.readUInt16LE(10),v.lastModFileTime=d.readUInt16LE(12),v.lastModFileDate=d.readUInt16LE(14),v.crc32=d.readUInt32LE(16),v.compressedSize=d.readUInt32LE(20),v.uncompressedSize=d.readUInt32LE(24),v.fileNameLength=d.readUInt16LE(28),v.extraFieldLength=d.readUInt16LE(30),v.fileCommentLength=d.readUInt16LE(32),v.internalFileAttributes=d.readUInt16LE(36),v.externalFileAttributes=d.readUInt32LE(38),v.relativeOffsetOfLocalHeader=d.readUInt32LE(42),v.generalPurposeBitFlag&64)return I(c,new Error("strong encryption is not supported"));c.readEntryCursor+=46,d=P(v.fileNameLength+v.extraFieldLength+v.fileCommentLength),D(c.reader,d,0,d.length,c.readEntryCursor,function(q){if(q)return I(c,q);if(!c.emittedError){var j=(v.generalPurposeBitFlag&2048)!==0;v.fileName=c.decodeStrings?U(d,0,v.fileNameLength,j):d.slice(0,v.fileNameLength);var K=v.fileNameLength+v.extraFieldLength,G=d.slice(v.fileNameLength,K);v.extraFields=[];for(var V=0;V<G.length-3;){var X=G.readUInt16LE(V+0),se=G.readUInt16LE(V+2),ae=V+4,fe=ae+se;if(fe>G.length)return I(c,new Error("extra field length exceeds extra field buffer size"));var Ee=P(se);G.copy(Ee,0,ae,fe),v.extraFields.push({id:X,data:Ee}),V=fe}if(v.fileComment=c.decodeStrings?U(d,K,K+v.fileCommentLength,j):d.slice(K,K+v.fileCommentLength),v.comment=v.fileComment,c.readEntryCursor+=d.length,c.entriesRead+=1,v.uncompressedSize===4294967295||v.compressedSize===4294967295||v.relativeOffsetOfLocalHeader===4294967295){for(var re=null,V=0;V<v.extraFields.length;V++){var Z=v.extraFields[V];if(Z.id===1){re=Z.data;break}}if(re==null)return I(c,new Error("expected zip64 extended information extra field"));var ne=0;if(v.uncompressedSize===4294967295){if(ne+8>re.length)return I(c,new Error("zip64 extended information extra field does not include uncompressed size"));v.uncompressedSize=W(re,ne),ne+=8}if(v.compressedSize===4294967295){if(ne+8>re.length)return I(c,new Error("zip64 extended information extra field does not include compressed size"));v.compressedSize=W(re,ne),ne+=8}if(v.relativeOffsetOfLocalHeader===4294967295){if(ne+8>re.length)return I(c,new Error("zip64 extended information extra field does not include relative header offset"));v.relativeOffsetOfLocalHeader=W(re,ne),ne+=8}}if(c.decodeStrings)for(var V=0;V<v.extraFields.length;V++){var Z=v.extraFields[V];if(Z.id===28789){if(Z.data.length<6||Z.data.readUInt8(0)!==1)continue;var ce=Z.data.readUInt32LE(1);if(s.unsigned(d.slice(0,v.fileNameLength))!==ce)continue;v.fileName=U(Z.data,5,Z.data.length,!0);break}}if(c.validateEntrySizes&&v.compressionMethod===0){var le=v.uncompressedSize;if(v.isEncrypted()&&(le+=12),v.compressedSize!==le){var we="compressed/uncompressed size mismatch for stored file: "+v.compressedSize+" != "+v.uncompressedSize;return I(c,new Error(we))}}if(c.decodeStrings){c.strictFileNames||(v.fileName=v.fileName.replace(/\\/g,"/"));var oe=F(v.fileName,c.validateFileNameOptions);if(oe!=null)return I(c,new Error(oe))}c.emit("entry",v),c.lazyEntries||c._readEntry()}})}})}},k.prototype.openReadStream=function(c,d,R){var v=this,M=0,q=c.compressedSize;if(R==null)R=d,d={};else{if(d.decrypt!=null){if(!c.isEncrypted())throw new Error("options.decrypt can only be specified for encrypted entries");if(d.decrypt!==!1)throw new Error("invalid options.decrypt value: "+d.decrypt);if(c.isCompressed()&&d.decompress!==!1)throw new Error("entry is encrypted and compressed, and options.decompress !== false")}if(d.decompress!=null){if(!c.isCompressed())throw new Error("options.decompress can only be specified for compressed entries");if(!(d.decompress===!1||d.decompress===!0))throw new Error("invalid options.decompress value: "+d.decompress)}if(d.start!=null||d.end!=null){if(c.isCompressed()&&d.decompress!==!1)throw new Error("start/end range not allowed for compressed entry without options.decompress === false");if(c.isEncrypted()&&d.decrypt!==!1)throw new Error("start/end range not allowed for encrypted entry without options.decrypt === false")}if(d.start!=null){if(M=d.start,M<0)throw new Error("options.start < 0");if(M>c.compressedSize)throw new Error("options.start > entry.compressedSize")}if(d.end!=null){if(q=d.end,q<0)throw new Error("options.end < 0");if(q>c.compressedSize)throw new Error("options.end > entry.compressedSize");if(q<M)throw new Error("options.end < options.start")}}if(!v.isOpen)return R(new Error("closed"));if(c.isEncrypted()&&d.decrypt!==!1)return R(new Error("entry is encrypted, and options.decrypt !== false"));v.reader.ref();var j=P(30);D(v.reader,j,0,j.length,c.relativeOffsetOfLocalHeader,function(K){try{if(K)return R(K);var G=j.readUInt32LE(0);if(G!==67324752)return R(new Error("invalid local file header signature: 0x"+G.toString(16)));var V=j.readUInt16LE(26),X=j.readUInt16LE(28),se=c.relativeOffsetOfLocalHeader+j.length+V+X,ae;if(c.compressionMethod===0)ae=!1;else if(c.compressionMethod===8)ae=d.decompress!=null?d.decompress:!0;else return R(new Error("unsupported compression method: "+c.compressionMethod));var fe=se,Ee=fe+c.compressedSize;if(c.compressedSize!==0&&Ee>v.fileSize)return R(new Error("file data overflows file bounds: "+fe+" + "+c.compressedSize+" > "+v.fileSize));var re=v.reader.createReadStream({start:fe+M,end:fe+q}),Z=re;if(ae){var ne=!1,ce=S.createInflateRaw();re.on("error",function(le){setImmediate(function(){ne||ce.emit("error",le)})}),re.pipe(ce),v.validateEntrySizes?(Z=new E(c.uncompressedSize),ce.on("error",function(le){setImmediate(function(){ne||Z.emit("error",le)})}),ce.pipe(Z)):Z=ce,Z.destroy=function(){ne=!0,ce!==Z&&ce.unpipe(Z),re.unpipe(ce),re.destroy()}}R(null,Z)}finally{v.reader.unref()}})};function m(){}m.prototype.getLastModDate=function(){return p(this.lastModFileDate,this.lastModFileTime)},m.prototype.isEncrypted=function(){return(this.generalPurposeBitFlag&1)!==0},m.prototype.isCompressed=function(){return this.compressionMethod===8};function p(c,d){var R=c&31,v=(c>>5&15)-1,M=(c>>9&127)+1980,q=0,j=(d&31)*2,K=d>>5&63,G=d>>11&31;return new Date(M,v,R,G,K,j,q)}function F(c){return c.indexOf("\\")!==-1?"invalid characters in fileName: "+c:/^[a-zA-Z]:/.test(c)||/^\//.test(c)?"absolute path: "+c:c.split("/").indexOf("..")!==-1?"invalid relative path: "+c:null}function D(c,d,R,v,M,q){if(v===0)return setImmediate(function(){q(null,P(0))});c.read(d,R,v,M,function(j,K){if(j)return q(j);if(K<v)return q(new Error("unexpected EOF"));q()})}y.inherits(E,g);function E(c){g.call(this),this.actualByteCount=0,this.expectedByteCount=c}E.prototype._transform=function(c,d,R){if(this.actualByteCount+=c.length,this.actualByteCount>this.expectedByteCount){var v="too many bytes in the stream. expected "+this.expectedByteCount+". got at least "+this.actualByteCount;return R(new Error(v))}R(null,c)},E.prototype._flush=function(c){if(this.actualByteCount<this.expectedByteCount){var d="not enough bytes in the stream. expected "+this.expectedByteCount+". got only "+this.actualByteCount;return c(new Error(d))}c()},y.inherits(f,C);function f(){C.call(this),this.refCount=0}f.prototype.ref=function(){this.refCount+=1},f.prototype.unref=function(){var c=this;if(c.refCount-=1,c.refCount>0)return;if(c.refCount<0)throw new Error("invalid unref");c.close(d);function d(R){if(R)return c.emit("error",R);c.emit("close")}},f.prototype.createReadStream=function(c){var d=c.start,R=c.end;if(d===R){var v=new a;return setImmediate(function(){v.end()}),v}var M=this._readStreamForRange(d,R),q=!1,j=new b(this);M.on("error",function(G){setImmediate(function(){q||j.emit("error",G)})}),j.destroy=function(){M.unpipe(j),j.unref(),M.destroy()};var K=new E(R-d);return j.on("error",function(G){setImmediate(function(){q||K.emit("error",G)})}),K.destroy=function(){q=!0,j.unpipe(K),j.destroy()},M.pipe(j).pipe(K)},f.prototype._readStreamForRange=function(c,d){throw new Error("not implemented")},f.prototype.read=function(c,d,R,v,M){var q=this.createReadStream({start:v,end:v+R}),j=new B,K=0;j._write=function(G,V,X){G.copy(c,d+K,0,G.length),K+=G.length,X()},j.on("finish",M),q.on("error",function(G){M(G)}),q.pipe(j)},f.prototype.close=function(c){setImmediate(c)},y.inherits(b,a);function b(c){a.call(this),this.context=c,this.context.ref(),this.unreffedYet=!1}b.prototype._flush=function(c){this.unref(),c()},b.prototype.unref=function(c){this.unreffedYet||(this.unreffedYet=!0,this.context.unref())};var L="\0☺☻♥♦♣♠•◘○◙♂♀♪♫☼►◄↕‼¶§▬↨↑↓→←∟↔▲▼ !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~⌂ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ";function U(c,d,R,v){if(v)return c.toString("utf8",d,R);for(var M="",q=d;q<R;q++)M+=L[c[q]];return M}function W(c,d){var R=c.readUInt32LE(d),v=c.readUInt32LE(d+4);return v*4294967296+R}var P;typeof Buffer.allocUnsafe=="function"?P=function(c){return Buffer.allocUnsafe(c)}:P=function(c){return new Buffer(c)};function $(c){if(c)throw c}return ge}var it,Ot;function Xt(){if(Ot)return it;Ot=1;class u{constructor(O){this._buffer=O}open(){return Promise.resolve()}close(){return Promise.resolve()}read(O,s,y,C){return this._buffer.copy(O,s,C,C+y),Promise.resolve(O)}buffer(){return this._buffer}static isBufferReader(O){return O instanceof u}}return it=u,it}var nt,Pt;function jt(){if(Pt)return nt;Pt=1;const u=_e;class S{constructor(s){this._filename=s}open(){return new Promise((s,y)=>{u.open(this._filename,"r",438,(C,g)=>{if(C)return y(C);this._fd=g,s()})})}close(){return new Promise((s,y)=>{this._fd?u.close(this._fd,C=>{if(C)return y(C);delete this._fd,s()}):s()})}read(s,y,C,g){return new Promise((a,B)=>{if(!this._fd)return B(new Error("file not open"));u.read(this._fd,s,y,C,g,(o,T,h)=>{if(o)return B(o);a(h)})})}fd(){return this._fd}static isFileReader(s){return s instanceof S}}return nt=S,nt}var st,kt;function Fr(){if(kt)return st;kt=1;const u=_e,S=yr(),O=Cr(),s=Xt(),y=jt(),C=Wt();function g(B,o,T){return T===o.length?Promise.resolve():Promise.resolve(B(o[T++])).then(()=>g(B,o,T))}class a{constructor(){this._streamTypes={"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":!0,"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":!0,"application/vnd.openxmlformats-officedocument.wordprocessingml.commentsExtended+xml":!0,"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":!0,"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":!0,"application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml":!0,"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":!0,"application/vnd.openxmlformats-package.relationships+xml":!0},this._headerTypes={"http://schemas.openxmlformats.org/officeDocument/2006/relationships/header":!0,"http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer":!0},this._actions={},this._defaults={}}shouldProcess(o){if(this._actions[o])return!0;const T=u.posix.extname(o).replace(/^\./,"");if(!T)return!1;const h=this._defaults[T];return!!(h&&this._streamTypes[h])}openArchive(o){if(s.isBufferReader(o))return new Promise((T,h)=>{O.fromBuffer(o.buffer(),{lazyEntries:!0},function(z,k){if(z)return h(z);T(k)})});if(y.isFileReader(o))return new Promise((T,h)=>{O.fromFd(o.fd(),{lazyEntries:!0,autoClose:!1},function(z,k){if(z)return h(z);T(k)})});throw new Error("Unexpected reader type: "+o.constructor.name)}processEntries(o){let T={},h=[];return new Promise((z,k)=>{o.readEntry(),o.on("error",k),o.on("entry",I=>{const w=I.fileName;T[w]=I,h.push(w),o.readEntry()}),o.on("end",()=>z(this._document))}).then(()=>{const z=h.indexOf("[Content_Types].xml");if(z===-1)throw new Error("Invalid Open Office XML: missing content types");return h.splice(z,1),h.unshift("[Content_Types].xml"),this._actions["[Content_Types].xml"]=!0,g(k=>{if(this.shouldProcess(k))return this.handleEntry(o,T[k])},h,0)})}extract(o){let T=this.openArchive(o);return this._document=new C,this._relationships={},this._entryTable={},this._entries=[],T.then(h=>this.processEntries(h)).then(()=>{let h=this._document;return h._textboxes&&h._textboxes.length>0&&(h._textboxes=h._textboxes+`
`),h._headerTextboxes&&h._headerTextboxes.length>0&&(h._headerTextboxes=h._headerTextboxes+`
`),h})}handleOpenTag(o){if(o.name==="Override"){const T=this._streamTypes[o.attributes.ContentType];if(T){const h=o.attributes.PartName.replace(/^[/]+/,""),z={action:T,type:o.attributes.ContentType};this._actions[h]=z}}else if(o.name==="Default"){const T=o.attributes.Extension,h=o.attributes.ContentType;this._defaults[T]=h}else if(o.name==="Relationship")this._relationships[o.attributes.Id]={type:o.attributes.Type,target:o.attributes.Target};else if(o.name==="w:document"||o.name==="w:footnotes"||o.name==="w:endnotes"||o.name==="w:comments")this._context=["content","body"],this._pieces=[];else if(o.name==="w:hdr"||o.name==="w:ftr")this._context=["content","header"],this._pieces=[];else if(o.name==="w:endnote"||o.name==="w:footnote"){const T=o.attributes["w:type"]||this._context[0];this._context.unshift(T)}else o.name==="w:tab"&&this._context[0]==="content"?this._pieces.push("	"):o.name==="w:br"&&this._context[0]==="content"?(o.attributes["w:type"]||"")==="page"?this._pieces.push(`
`):this._pieces.push(`
`):o.name==="w:del"||o.name==="w:instrText"?this._context.unshift("deleted"):o.name==="w:tabs"?this._context.unshift("tabs"):o.name==="w:tc"?this._context.unshift("cell"):o.name==="w:drawing"?this._context.unshift("drawing"):o.name==="w:txbxContent"&&(this._context.unshift(this._pieces),this._context.unshift("textbox"),this._pieces=[])}handleCloseTag(o){if(o.name==="w:document")this._context=null,this._document._body=this._pieces.join("");else if(o.name==="w:footnote"||o.name==="w:endnote")this._context.shift();else if(o.name==="w:footnotes")this._context=null,this._document._footnotes=this._pieces.join("");else if(o.name==="w:endnotes")this._context=null,this._document._endnotes=this._pieces.join("");else if(o.name==="w:comments")this._context=null,this._document._annotations=this._pieces.join("");else if(o.name==="w:hdr")this._context=null,this._document._headers=this._document._headers+this._pieces.join("");else if(o.name==="w:ftr")this._context=null,this._document._footers=this._document._footers+this._pieces.join("");else if(o.name==="w:p")(this._context[0]==="content"||this._context[0]==="cell"||this._context[0]==="textbox")&&this._pieces.push(`
`);else if(o.name==="w:del"||o.name==="w:instrText")this._context.shift();else if(o.name==="w:tabs")this._context.shift();else if(o.name==="w:tc")this._pieces.pop(),this._pieces.push("	"),this._context.shift();else if(o.name==="w:tr")this._pieces.push(`
`);else if(o.name==="w:drawing")this._context.shift();else if(o.name==="w:txbxContent"){const T=this._pieces.join("");if(this._context.shift()!=="textbox")throw new Error("Invalid textbox context");if(this._pieces=this._context.shift(),this._context[0]==="drawing"||T.length==0)return;const k=this._context.includes("header")?"_headerTextboxes":"_textboxes";this._document[k]?this._document[k]=this._document[k]+`
`+T:this._document[k]=T}}createXmlParser(){const o=new S.SaxesParser;return o.on("opentag",T=>{try{this.handleOpenTag(T)}catch(h){o.fail(h.message)}}),o.on("closetag",T=>{try{this.handleCloseTag(T)}catch(h){o.fail(h.message)}}),o.on("text",T=>{try{if(!this._context)return;(this._context[0]==="content"||this._context[0]==="cell"||this._context[0]==="textbox")&&this._pieces.push(T)}catch(h){o.fail(h.message)}}),o}handleEntry(o,T){return new Promise((h,z)=>{o.openReadStream(T,(k,I)=>{if(k)return z(k);this._source=T.fileName;const w=this.createXmlParser();w.on("error",m=>{I.destroy(m),z(m)}),w.on("end",()=>h()),I.on("end",()=>w.close()),I.on("error",m=>z(m)),I.on("readable",()=>{for(;;){const m=I.read(4096);if(m===null)return;w.write(m)}})})})}}return st=a,st}var at,Ht;function Tr(){if(Ht)return at;Ht=1;const{Buffer:u}=zt(),S=Er(),O=Fr(),s=Xt(),y=jt();class C{constructor(){}extract(a){let B=null;u.isBuffer(a)?B=new s(a):typeof a=="string"&&(B=new y(a));const o=u.alloc(512);return B.open().then(()=>B.read(o,0,512,0)).then(T=>{let h=null;if(T.readUInt16BE(0)===53455)h=S;else if(T.readUInt16BE(0)===20555){const z=T.readUInt16BE(2);(z===772||z===1286||z===1800)&&(h=O)}if(!h)throw new Error("Unable to read this type of file");return new h().extract(B)}).finally(()=>B.close())}}return at=C,at}var Vt=Tr();const Ar=sr(Vt),Rr=or({__proto__:null,default:Ar},[Vt]);export{Rr as w};

import{r as o,j as d,c as T,e as F}from"./main-Dj4JwwOF.js";import{c as m}from"./index-_TRYHs0w.js";import{c as z}from"./button-D6FdW_Gn.js";import{c as N}from"./createLucideIcon-C4-NQwlF.js";function ue(e,t){const a=o.createContext(t),n=r=>{const{children:c,...s}=r,u=o.useMemo(()=>s,Object.values(s));return d.jsx(a.Provider,{value:u,children:c})};n.displayName=e+"Provider";function i(r){const c=o.useContext(a);if(c)return c;if(t!==void 0)return t;throw new Error(`\`${r}\` must be used within \`${e}\``)}return[n,i]}function D(e,t=[]){let a=[];function n(r,c){const s=o.createContext(c),u=a.length;a=[...a,c];const l=f=>{const{scope:h,children:S,...p}=f,M=h?.[e]?.[u]||s,O=o.useMemo(()=>p,Object.values(p));return d.jsx(M.Provider,{value:O,children:S})};l.displayName=r+"Provider";function v(f,h){const S=h?.[e]?.[u]||s,p=o.useContext(S);if(p)return p;if(c!==void 0)return c;throw new Error(`\`${f}\` must be used within \`${r}\``)}return[l,v]}const i=()=>{const r=a.map(c=>o.createContext(c));return function(s){const u=s?.[e]||r;return o.useMemo(()=>({[`__scope${e}`]:{...s,[e]:u}}),[s,u])}};return i.scopeName=e,[n,H(i,...t)]}function H(...e){const t=e[0];if(e.length===1)return t;const a=()=>{const n=e.map(i=>({useScope:i(),scopeName:i.scopeName}));return function(r){const c=n.reduce((s,{useScope:u,scopeName:l})=>{const f=u(r)[`__scope${l}`];return{...s,...f}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return a.scopeName=t.scopeName,a}function U(e){const t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...a)=>t.current?.(...a),[])}var g=globalThis?.document?o.useLayoutEffect:()=>{},q=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],x=q.reduce((e,t)=>{const a=z(`Primitive.${t}`),n=o.forwardRef((i,r)=>{const{asChild:c,...s}=i,u=c?a:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),d.jsx(u,{...s,ref:r})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function de(e,t){e&&T.flushSync(()=>e.dispatchEvent(t))}var B=F();function V(){return B.useSyncExternalStore(G,()=>!0,()=>!1)}function G(){return()=>{}}var w="Avatar",[K,le]=D(w),[W,E]=K(w),C=o.forwardRef((e,t)=>{const{__scopeAvatar:a,...n}=e,[i,r]=o.useState("idle");return d.jsx(W,{scope:a,imageLoadingStatus:i,onImageLoadingStatusChange:r,children:d.jsx(x.span,{...n,ref:t})})});C.displayName=w;var L="AvatarImage",R=o.forwardRef((e,t)=>{const{__scopeAvatar:a,src:n,onLoadingStatusChange:i=()=>{},...r}=e,c=E(L,a),s=J(n,r),u=U(l=>{i(l),c.onImageLoadingStatusChange(l)});return g(()=>{s!=="idle"&&u(s)},[s,u]),s==="loaded"?d.jsx(x.img,{...r,ref:t,src:n}):null});R.displayName=L;var b="AvatarFallback",_=o.forwardRef((e,t)=>{const{__scopeAvatar:a,delayMs:n,...i}=e,r=E(b,a),[c,s]=o.useState(n===void 0);return o.useEffect(()=>{if(n!==void 0){const u=window.setTimeout(()=>s(!0),n);return()=>window.clearTimeout(u)}},[n]),c&&r.imageLoadingStatus!=="loaded"?d.jsx(x.span,{...i,ref:t}):null});_.displayName=b;function y(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function J(e,{referrerPolicy:t,crossOrigin:a}){const n=V(),i=o.useRef(null),r=n?(i.current||(i.current=new window.Image),i.current):null,[c,s]=o.useState(()=>y(r,e));return g(()=>{s(y(r,e))},[r,e]),g(()=>{const u=f=>()=>{s(f)};if(!r)return;const l=u("loaded"),v=u("error");return r.addEventListener("load",l),r.addEventListener("error",v),t&&(r.referrerPolicy=t),typeof a=="string"&&(r.crossOrigin=a),()=>{r.removeEventListener("load",l),r.removeEventListener("error",v)}},[r,a,t]),c}var P=C,j=R,I=_;const Q=o.forwardRef(({className:e,...t},a)=>d.jsx(P,{ref:a,className:m("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));Q.displayName=P.displayName;const X=o.forwardRef(({className:e,...t},a)=>d.jsx(j,{ref:a,className:m("aspect-square h-full w-full",e),...t}));X.displayName=j.displayName;const Y=o.forwardRef(({className:e,...t},a)=>d.jsx(I,{ref:a,className:m("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));Y.displayName=I.displayName;var Z="Separator",A="horizontal",ee=["horizontal","vertical"],$=o.forwardRef((e,t)=>{const{decorative:a,orientation:n=A,...i}=e,r=te(n)?n:A,s=a?{role:"none"}:{"aria-orientation":r==="vertical"?r:void 0,role:"separator"};return d.jsx(x.div,{"data-orientation":r,...s,...i,ref:t})});$.displayName=Z;function te(e){return ee.includes(e)}var k=$;const re=o.forwardRef(({className:e,orientation:t="horizontal",decorative:a=!0,...n},i)=>d.jsx(k,{ref:i,decorative:a,orientation:t,className:m("shrink-0 bg-border",t==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...n}));re.displayName=k.displayName;/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],fe=N("loader-circle",ae);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],ve=N("user",oe);export{Q as A,fe as L,x as P,re as S,ve as U,Y as a,U as b,D as c,de as d,ue as e,X as f,g as u};

import { createServerFileRoute } from '@tanstack/react-start/server'
import { json } from '@tanstack/react-start'

/**
 * GetAuthToken API - 获取手机号认证授权Token
 * 调用阿里云GetAuthToken接口获取业务鉴权accessToken和API鉴权jwtToken
 */
export const ServerRoute = createServerFileRoute('/api/auth/get-auth-token')
  .methods({
    POST: async ({ request }) => {
      try {
        const body = await request.json()
        const { url, origin, sceneCode, bizType = 1 } = body

        console.log('GetAuthToken请求参数:', { url, origin, sceneCode, bizType })

        // 验证必需参数
        if (!url || !origin) {
          return json(
            { error: 'Missing required parameters: url and origin are required' },
            { status: 400 }
          )
        }

        // 阿里云API配置
        const accessKeyId = process.env.VITE_ALIYUN_ACCESS_KEY_ID
        const accessKeySecret = process.env.VITE_ALIYUN_ACCESS_KEY_SECRET

        console.log('阿里云配置检查:', {
          hasAccessKeyId: !!accessKeyId,
          hasAccessKeySecret: !!accessKeySecret,
          accessKeyId: accessKeyId?.substring(0, 8) + '...' // 只显示前8位用于调试
        })

        if (!accessKeyId || !accessKeySecret || accessKeyId === 'your_access_key_id') {
          return json(
            {
              error: 'Aliyun credentials not configured',
              message: '请在.env文件中配置正确的阿里云AccessKey'
            },
            { status: 500 }
          )
        }

        // 动态导入阿里云SDK
        const RPCClient = (await import('@alicloud/pop-core')).default

        // 创建阿里云客户端
        const client = new RPCClient({
          accessKeyId: accessKeyId,
          accessKeySecret: accessKeySecret,
          endpoint: 'https://dypns.cn-hangzhou.aliyuncs.com',
          apiVersion: '2017-05-25'
        })

        // 构建请求参数
        const params = {
          Url: url,
          Origin: origin,
          BizType: bizType,
          ...(sceneCode && { SceneCode: sceneCode })
        }

        console.log('调用阿里云GetAuthToken API，参数:', params)

        // 调用阿里云API
        const result = await client.request('GetAuthToken', params, {
          method: 'POST'
        })

        console.log('阿里云API响应:', result)

        // 返回成功响应
        return json({
          code: (result as any).Code,
          message: (result as any).Message,
          requestId: (result as any).RequestId,
          tokenInfo: (result as any).TokenInfo
        })

      } catch (error) {
        console.error('GetAuthToken API代理错误:', error)
        return json(
          {
            error: 'Internal Server Error',
            message: error instanceof Error ? error.message : '服务器内部错误',
            details: error instanceof Error ? error.stack : undefined
          },
          { status: 500 }
        )
      }
    },

    // 处理预检请求
    OPTIONS: async () => {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      })
    }
  })

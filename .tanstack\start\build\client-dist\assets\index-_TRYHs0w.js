function we(e){var t,o,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(t=0;t<a;t++)e[t]&&(o=we(e[t]))&&(r&&(r+=" "),r+=o)}else for(o in e)e[o]&&(r&&(r+=" "),r+=o);return r}function ke(){for(var e,t,o=0,r="",a=arguments.length;o<a;o++)(e=arguments[o])&&(t=we(e))&&(r&&(r+=" "),r+=t);return r}const ne="-",Ne=e=>{const t=Ve(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:l=>{const p=l.split(ne);return p[0]===""&&p.length!==1&&p.shift(),ve(p,t)||Te(l)},getConflictingClassGroupIds:(l,p)=>{const u=o[l]||[];return p&&r[l]?[...u,...r[l]]:u}}},ve=(e,t)=>{if(e.length===0)return t.classGroupId;const o=e[0],r=t.nextPart.get(o),a=r?ve(e.slice(1),r):void 0;if(a)return a;if(t.validators.length===0)return;const c=e.join(ne);return t.validators.find(({validator:l})=>l(c))?.classGroupId},fe=/^\[(.+)\]$/,Te=e=>{if(fe.test(e)){const t=fe.exec(e)[1],o=t?.substring(0,t.indexOf(":"));if(o)return"arbitrary.."+o}},Ve=e=>{const{theme:t,classGroups:o}=e,r={nextPart:new Map,validators:[]};for(const a in o)re(o[a],r,a,t);return r},re=(e,t,o,r)=>{e.forEach(a=>{if(typeof a=="string"){const c=a===""?t:be(t,a);c.classGroupId=o;return}if(typeof a=="function"){if(Ee(a)){re(a(r),t,o,r);return}t.validators.push({validator:a,classGroupId:o});return}Object.entries(a).forEach(([c,l])=>{re(l,be(t,c),o,r)})})},be=(e,t)=>{let o=e;return t.split(ne).forEach(r=>{o.nextPart.has(r)||o.nextPart.set(r,{nextPart:new Map,validators:[]}),o=o.nextPart.get(r)}),o},Ee=e=>e.isThemeGetter,je=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,o=new Map,r=new Map;const a=(c,l)=>{o.set(c,l),t++,t>e&&(t=0,r=o,o=new Map)};return{get(c){let l=o.get(c);if(l!==void 0)return l;if((l=r.get(c))!==void 0)return a(c,l),l},set(c,l){o.has(c)?o.set(c,l):a(c,l)}}},te="!",se=":",Le=se.length,Oe=e=>{const{prefix:t,experimentalParseClassName:o}=e;let r=a=>{const c=[];let l=0,p=0,u=0,f;for(let w=0;w<a.length;w++){let k=a[w];if(l===0&&p===0){if(k===se){c.push(a.slice(u,w)),u=w+Le;continue}if(k==="/"){f=w;continue}}k==="["?l++:k==="]"?l--:k==="("?p++:k===")"&&p--}const b=c.length===0?a:a.substring(u),y=_e(b),z=y!==b,I=f&&f>u?f-u:void 0;return{modifiers:c,hasImportantModifier:z,baseClassName:y,maybePostfixModifierPosition:I}};if(t){const a=t+se,c=r;r=l=>l.startsWith(a)?c(l.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:l,maybePostfixModifierPosition:void 0}}if(o){const a=r;r=c=>o({className:c,parseClassName:a})}return r},_e=e=>e.endsWith(te)?e.substring(0,e.length-1):e.startsWith(te)?e.substring(1):e,Fe=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const a=[];let c=[];return r.forEach(l=>{l[0]==="["||t[l]?(a.push(...c.sort(),l),c=[]):c.push(l)}),a.push(...c.sort()),a}},Be=e=>({cache:je(e.cacheSize),parseClassName:Oe(e),sortModifiers:Fe(e),...Ne(e)}),We=/\s+/,$e=(e,t)=>{const{parseClassName:o,getClassGroupId:r,getConflictingClassGroupIds:a,sortModifiers:c}=t,l=[],p=e.trim().split(We);let u="";for(let f=p.length-1;f>=0;f-=1){const b=p[f],{isExternal:y,modifiers:z,hasImportantModifier:I,baseClassName:w,maybePostfixModifierPosition:k}=o(b);if(y){u=b+(u.length>0?" "+u:u);continue}let C=!!k,R=r(C?w.substring(0,k):w);if(!R){if(!C){u=b+(u.length>0?" "+u:u);continue}if(R=r(w),!R){u=b+(u.length>0?" "+u:u);continue}C=!1}const $=c(z).join(":"),F=I?$+te:$,V=F+R;if(l.includes(V))continue;l.push(V);const E=a(R,C);for(let G=0;G<E.length;++G){const B=E[G];l.push(F+B)}u=b+(u.length>0?" "+u:u)}return u};function Ue(){let e=0,t,o,r="";for(;e<arguments.length;)(t=arguments[e++])&&(o=ze(t))&&(r&&(r+=" "),r+=o);return r}const ze=e=>{if(typeof e=="string")return e;let t,o="";for(let r=0;r<e.length;r++)e[r]&&(t=ze(e[r]))&&(o&&(o+=" "),o+=t);return o};function qe(e,...t){let o,r,a,c=l;function l(u){const f=t.reduce((b,y)=>y(b),e());return o=Be(f),r=o.cache.get,a=o.cache.set,c=p,p(u)}function p(u){const f=r(u);if(f)return f;const b=$e(u,o);return a(u,b),b}return function(){return c(Ue.apply(null,arguments))}}const g=e=>{const t=o=>o[e]||[];return t.isThemeGetter=!0,t},Ce=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Ae=/^\((?:(\w[\w-]*):)?(.+)\)$/i,He=/^\d+\/\d+$/,Je=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Ke=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Xe=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,De=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Qe=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,L=e=>He.test(e),m=e=>!!e&&!Number.isNaN(Number(e)),P=e=>!!e&&Number.isInteger(Number(e)),ee=e=>e.endsWith("%")&&m(e.slice(0,-1)),S=e=>Je.test(e),Ye=()=>!0,Ze=e=>Ke.test(e)&&!Xe.test(e),Me=()=>!1,eo=e=>De.test(e),oo=e=>Qe.test(e),ro=e=>!s(e)&&!n(e),to=e=>O(e,Ie,Me),s=e=>Ce.test(e),T=e=>O(e,Re,Ze),oe=e=>O(e,lo,m),ge=e=>O(e,Se,Me),so=e=>O(e,Pe,oo),X=e=>O(e,Ge,eo),n=e=>Ae.test(e),W=e=>_(e,Re),no=e=>_(e,co),he=e=>_(e,Se),ao=e=>_(e,Ie),io=e=>_(e,Pe),D=e=>_(e,Ge,!0),O=(e,t,o)=>{const r=Ce.exec(e);return r?r[1]?t(r[1]):o(r[2]):!1},_=(e,t,o=!1)=>{const r=Ae.exec(e);return r?r[1]?t(r[1]):o:!1},Se=e=>e==="position"||e==="percentage",Pe=e=>e==="image"||e==="url",Ie=e=>e==="length"||e==="size"||e==="bg-size",Re=e=>e==="length",lo=e=>e==="number",co=e=>e==="family-name",Ge=e=>e==="shadow",mo=()=>{const e=g("color"),t=g("font"),o=g("text"),r=g("font-weight"),a=g("tracking"),c=g("leading"),l=g("breakpoint"),p=g("container"),u=g("spacing"),f=g("radius"),b=g("shadow"),y=g("inset-shadow"),z=g("text-shadow"),I=g("drop-shadow"),w=g("blur"),k=g("perspective"),C=g("aspect"),R=g("ease"),$=g("animate"),F=()=>["auto","avoid","all","avoid-page","page","left","right","column"],V=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...V(),n,s],G=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto","contain","none"],d=()=>[n,s,u],A=()=>[L,"full","auto",...d()],ae=()=>[P,"none","subgrid",n,s],ie=()=>["auto",{span:["full",P,n,s]},P,n,s],U=()=>[P,"auto",n,s],le=()=>["auto","min","max","fr",n,s],Q=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],j=()=>["start","end","center","stretch","center-safe","end-safe"],M=()=>["auto",...d()],N=()=>[L,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...d()],i=()=>[e,n,s],ce=()=>[...V(),he,ge,{position:[n,s]}],de=()=>["no-repeat",{repeat:["","x","y","space","round"]}],me=()=>["auto","cover","contain",ao,to,{size:[n,s]}],Y=()=>[ee,W,T],x=()=>["","none","full",f,n,s],v=()=>["",m,W,T],q=()=>["solid","dashed","dotted","double"],ue=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],h=()=>[m,ee,he,ge],pe=()=>["","none",w,n,s],H=()=>["none",m,n,s],J=()=>["none",m,n,s],Z=()=>[m,n,s],K=()=>[L,"full",...d()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[S],breakpoint:[S],color:[Ye],container:[S],"drop-shadow":[S],ease:["in","out","in-out"],font:[ro],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[S],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[S],shadow:[S],spacing:["px",m],text:[S],"text-shadow":[S],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",L,s,n,C]}],container:["container"],columns:[{columns:[m,s,n,p]}],"break-after":[{"break-after":F()}],"break-before":[{"break-before":F()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:G()}],"overflow-x":[{"overflow-x":G()}],"overflow-y":[{"overflow-y":G()}],overscroll:[{overscroll:B()}],"overscroll-x":[{"overscroll-x":B()}],"overscroll-y":[{"overscroll-y":B()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:A()}],"inset-x":[{"inset-x":A()}],"inset-y":[{"inset-y":A()}],start:[{start:A()}],end:[{end:A()}],top:[{top:A()}],right:[{right:A()}],bottom:[{bottom:A()}],left:[{left:A()}],visibility:["visible","invisible","collapse"],z:[{z:[P,"auto",n,s]}],basis:[{basis:[L,"full","auto",p,...d()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[m,L,"auto","initial","none",s]}],grow:[{grow:["",m,n,s]}],shrink:[{shrink:["",m,n,s]}],order:[{order:[P,"first","last","none",n,s]}],"grid-cols":[{"grid-cols":ae()}],"col-start-end":[{col:ie()}],"col-start":[{"col-start":U()}],"col-end":[{"col-end":U()}],"grid-rows":[{"grid-rows":ae()}],"row-start-end":[{row:ie()}],"row-start":[{"row-start":U()}],"row-end":[{"row-end":U()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":le()}],"auto-rows":[{"auto-rows":le()}],gap:[{gap:d()}],"gap-x":[{"gap-x":d()}],"gap-y":[{"gap-y":d()}],"justify-content":[{justify:[...Q(),"normal"]}],"justify-items":[{"justify-items":[...j(),"normal"]}],"justify-self":[{"justify-self":["auto",...j()]}],"align-content":[{content:["normal",...Q()]}],"align-items":[{items:[...j(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...j(),{baseline:["","last"]}]}],"place-content":[{"place-content":Q()}],"place-items":[{"place-items":[...j(),"baseline"]}],"place-self":[{"place-self":["auto",...j()]}],p:[{p:d()}],px:[{px:d()}],py:[{py:d()}],ps:[{ps:d()}],pe:[{pe:d()}],pt:[{pt:d()}],pr:[{pr:d()}],pb:[{pb:d()}],pl:[{pl:d()}],m:[{m:M()}],mx:[{mx:M()}],my:[{my:M()}],ms:[{ms:M()}],me:[{me:M()}],mt:[{mt:M()}],mr:[{mr:M()}],mb:[{mb:M()}],ml:[{ml:M()}],"space-x":[{"space-x":d()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":d()}],"space-y-reverse":["space-y-reverse"],size:[{size:N()}],w:[{w:[p,"screen",...N()]}],"min-w":[{"min-w":[p,"screen","none",...N()]}],"max-w":[{"max-w":[p,"screen","none","prose",{screen:[l]},...N()]}],h:[{h:["screen","lh",...N()]}],"min-h":[{"min-h":["screen","lh","none",...N()]}],"max-h":[{"max-h":["screen","lh",...N()]}],"font-size":[{text:["base",o,W,T]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,n,oe]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ee,s]}],"font-family":[{font:[no,s,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,n,s]}],"line-clamp":[{"line-clamp":[m,"none",n,oe]}],leading:[{leading:[c,...d()]}],"list-image":[{"list-image":["none",n,s]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",n,s]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:i()}],"text-color":[{text:i()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:[m,"from-font","auto",n,T]}],"text-decoration-color":[{decoration:i()}],"underline-offset":[{"underline-offset":[m,"auto",n,s]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:d()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",n,s]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",n,s]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ce()}],"bg-repeat":[{bg:de()}],"bg-size":[{bg:me()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},P,n,s],radial:["",n,s],conic:[P,n,s]},io,so]}],"bg-color":[{bg:i()}],"gradient-from-pos":[{from:Y()}],"gradient-via-pos":[{via:Y()}],"gradient-to-pos":[{to:Y()}],"gradient-from":[{from:i()}],"gradient-via":[{via:i()}],"gradient-to":[{to:i()}],rounded:[{rounded:x()}],"rounded-s":[{"rounded-s":x()}],"rounded-e":[{"rounded-e":x()}],"rounded-t":[{"rounded-t":x()}],"rounded-r":[{"rounded-r":x()}],"rounded-b":[{"rounded-b":x()}],"rounded-l":[{"rounded-l":x()}],"rounded-ss":[{"rounded-ss":x()}],"rounded-se":[{"rounded-se":x()}],"rounded-ee":[{"rounded-ee":x()}],"rounded-es":[{"rounded-es":x()}],"rounded-tl":[{"rounded-tl":x()}],"rounded-tr":[{"rounded-tr":x()}],"rounded-br":[{"rounded-br":x()}],"rounded-bl":[{"rounded-bl":x()}],"border-w":[{border:v()}],"border-w-x":[{"border-x":v()}],"border-w-y":[{"border-y":v()}],"border-w-s":[{"border-s":v()}],"border-w-e":[{"border-e":v()}],"border-w-t":[{"border-t":v()}],"border-w-r":[{"border-r":v()}],"border-w-b":[{"border-b":v()}],"border-w-l":[{"border-l":v()}],"divide-x":[{"divide-x":v()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":v()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...q(),"hidden","none"]}],"divide-style":[{divide:[...q(),"hidden","none"]}],"border-color":[{border:i()}],"border-color-x":[{"border-x":i()}],"border-color-y":[{"border-y":i()}],"border-color-s":[{"border-s":i()}],"border-color-e":[{"border-e":i()}],"border-color-t":[{"border-t":i()}],"border-color-r":[{"border-r":i()}],"border-color-b":[{"border-b":i()}],"border-color-l":[{"border-l":i()}],"divide-color":[{divide:i()}],"outline-style":[{outline:[...q(),"none","hidden"]}],"outline-offset":[{"outline-offset":[m,n,s]}],"outline-w":[{outline:["",m,W,T]}],"outline-color":[{outline:i()}],shadow:[{shadow:["","none",b,D,X]}],"shadow-color":[{shadow:i()}],"inset-shadow":[{"inset-shadow":["none",y,D,X]}],"inset-shadow-color":[{"inset-shadow":i()}],"ring-w":[{ring:v()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:i()}],"ring-offset-w":[{"ring-offset":[m,T]}],"ring-offset-color":[{"ring-offset":i()}],"inset-ring-w":[{"inset-ring":v()}],"inset-ring-color":[{"inset-ring":i()}],"text-shadow":[{"text-shadow":["none",z,D,X]}],"text-shadow-color":[{"text-shadow":i()}],opacity:[{opacity:[m,n,s]}],"mix-blend":[{"mix-blend":[...ue(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ue()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[m]}],"mask-image-linear-from-pos":[{"mask-linear-from":h()}],"mask-image-linear-to-pos":[{"mask-linear-to":h()}],"mask-image-linear-from-color":[{"mask-linear-from":i()}],"mask-image-linear-to-color":[{"mask-linear-to":i()}],"mask-image-t-from-pos":[{"mask-t-from":h()}],"mask-image-t-to-pos":[{"mask-t-to":h()}],"mask-image-t-from-color":[{"mask-t-from":i()}],"mask-image-t-to-color":[{"mask-t-to":i()}],"mask-image-r-from-pos":[{"mask-r-from":h()}],"mask-image-r-to-pos":[{"mask-r-to":h()}],"mask-image-r-from-color":[{"mask-r-from":i()}],"mask-image-r-to-color":[{"mask-r-to":i()}],"mask-image-b-from-pos":[{"mask-b-from":h()}],"mask-image-b-to-pos":[{"mask-b-to":h()}],"mask-image-b-from-color":[{"mask-b-from":i()}],"mask-image-b-to-color":[{"mask-b-to":i()}],"mask-image-l-from-pos":[{"mask-l-from":h()}],"mask-image-l-to-pos":[{"mask-l-to":h()}],"mask-image-l-from-color":[{"mask-l-from":i()}],"mask-image-l-to-color":[{"mask-l-to":i()}],"mask-image-x-from-pos":[{"mask-x-from":h()}],"mask-image-x-to-pos":[{"mask-x-to":h()}],"mask-image-x-from-color":[{"mask-x-from":i()}],"mask-image-x-to-color":[{"mask-x-to":i()}],"mask-image-y-from-pos":[{"mask-y-from":h()}],"mask-image-y-to-pos":[{"mask-y-to":h()}],"mask-image-y-from-color":[{"mask-y-from":i()}],"mask-image-y-to-color":[{"mask-y-to":i()}],"mask-image-radial":[{"mask-radial":[n,s]}],"mask-image-radial-from-pos":[{"mask-radial-from":h()}],"mask-image-radial-to-pos":[{"mask-radial-to":h()}],"mask-image-radial-from-color":[{"mask-radial-from":i()}],"mask-image-radial-to-color":[{"mask-radial-to":i()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":V()}],"mask-image-conic-pos":[{"mask-conic":[m]}],"mask-image-conic-from-pos":[{"mask-conic-from":h()}],"mask-image-conic-to-pos":[{"mask-conic-to":h()}],"mask-image-conic-from-color":[{"mask-conic-from":i()}],"mask-image-conic-to-color":[{"mask-conic-to":i()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ce()}],"mask-repeat":[{mask:de()}],"mask-size":[{mask:me()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",n,s]}],filter:[{filter:["","none",n,s]}],blur:[{blur:pe()}],brightness:[{brightness:[m,n,s]}],contrast:[{contrast:[m,n,s]}],"drop-shadow":[{"drop-shadow":["","none",I,D,X]}],"drop-shadow-color":[{"drop-shadow":i()}],grayscale:[{grayscale:["",m,n,s]}],"hue-rotate":[{"hue-rotate":[m,n,s]}],invert:[{invert:["",m,n,s]}],saturate:[{saturate:[m,n,s]}],sepia:[{sepia:["",m,n,s]}],"backdrop-filter":[{"backdrop-filter":["","none",n,s]}],"backdrop-blur":[{"backdrop-blur":pe()}],"backdrop-brightness":[{"backdrop-brightness":[m,n,s]}],"backdrop-contrast":[{"backdrop-contrast":[m,n,s]}],"backdrop-grayscale":[{"backdrop-grayscale":["",m,n,s]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[m,n,s]}],"backdrop-invert":[{"backdrop-invert":["",m,n,s]}],"backdrop-opacity":[{"backdrop-opacity":[m,n,s]}],"backdrop-saturate":[{"backdrop-saturate":[m,n,s]}],"backdrop-sepia":[{"backdrop-sepia":["",m,n,s]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":d()}],"border-spacing-x":[{"border-spacing-x":d()}],"border-spacing-y":[{"border-spacing-y":d()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",n,s]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[m,"initial",n,s]}],ease:[{ease:["linear","initial",R,n,s]}],delay:[{delay:[m,n,s]}],animate:[{animate:["none",$,n,s]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[k,n,s]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:H()}],"rotate-x":[{"rotate-x":H()}],"rotate-y":[{"rotate-y":H()}],"rotate-z":[{"rotate-z":H()}],scale:[{scale:J()}],"scale-x":[{"scale-x":J()}],"scale-y":[{"scale-y":J()}],"scale-z":[{"scale-z":J()}],"scale-3d":["scale-3d"],skew:[{skew:Z()}],"skew-x":[{"skew-x":Z()}],"skew-y":[{"skew-y":Z()}],transform:[{transform:[n,s,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:K()}],"translate-x":[{"translate-x":K()}],"translate-y":[{"translate-y":K()}],"translate-z":[{"translate-z":K()}],"translate-none":["translate-none"],accent:[{accent:i()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:i()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",n,s]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":d()}],"scroll-mx":[{"scroll-mx":d()}],"scroll-my":[{"scroll-my":d()}],"scroll-ms":[{"scroll-ms":d()}],"scroll-me":[{"scroll-me":d()}],"scroll-mt":[{"scroll-mt":d()}],"scroll-mr":[{"scroll-mr":d()}],"scroll-mb":[{"scroll-mb":d()}],"scroll-ml":[{"scroll-ml":d()}],"scroll-p":[{"scroll-p":d()}],"scroll-px":[{"scroll-px":d()}],"scroll-py":[{"scroll-py":d()}],"scroll-ps":[{"scroll-ps":d()}],"scroll-pe":[{"scroll-pe":d()}],"scroll-pt":[{"scroll-pt":d()}],"scroll-pr":[{"scroll-pr":d()}],"scroll-pb":[{"scroll-pb":d()}],"scroll-pl":[{"scroll-pl":d()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",n,s]}],fill:[{fill:["none",...i()]}],"stroke-w":[{stroke:[m,W,T,oe]}],stroke:[{stroke:["none",...i()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},uo=qe(mo);function po(...e){return uo(ke(e))}const xe=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,ye=ke,fo=(e,t)=>o=>{var r;if(t?.variants==null)return ye(e,o?.class,o?.className);const{variants:a,defaultVariants:c}=t,l=Object.keys(a).map(f=>{const b=o?.[f],y=c?.[f];if(b===null)return null;const z=xe(b)||xe(y);return a[f][z]}),p=o&&Object.entries(o).reduce((f,b)=>{let[y,z]=b;return z===void 0||(f[y]=z),f},{}),u=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((f,b)=>{let{class:y,className:z,...I}=b;return Object.entries(I).every(w=>{let[k,C]=w;return Array.isArray(C)?C.includes({...c,...p}[k]):{...c,...p}[k]===C})?[...f,y,z]:f},[]);return ye(e,l,u,o?.class,o?.className)};export{fo as a,ke as b,po as c,uo as t};

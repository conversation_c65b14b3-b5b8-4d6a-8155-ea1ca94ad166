"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { LogIn, Smartphone } from "lucide-react";
import { useRouter } from "@tanstack/react-router";
import { useAuth } from "@/hooks/use-auth";
import { useUserStore } from "@/stores/user-store";

// 声明全局PhoneNumberServer类型
declare global {
  interface Window {
    PhoneNumberServer: any;
  }
}

/**
 * 简洁的登录组件
 * 支持钉钉登录和手机号一键登录
 */
const SignIn = () => {
	const router = useRouter();
	const { login } = useAuth();
	const { setUser } = useUserStore();
	const [isLoading, setIsLoading] = useState(false);
	const [isPhoneLoading, setIsPhoneLoading] = useState(false);
	const [error, setError] = useState("");
	const [phoneNumberServer, setPhoneNumberServer] = useState<any>(null);

	// 初始化手机号认证SDK
	useEffect(() => {
		const initPhoneSDK = async () => {
			try {
				// 动态导入阿里云手机号认证SDK
				const { PhoneNumberServer } = await import('aliyun_numberauthsdk_web');
				const server = new PhoneNumberServer();
				setPhoneNumberServer(server);
				console.log('手机号认证SDK初始化成功');
			} catch (error) {
				console.error('手机号认证SDK初始化失败:', error);
			}
		};

		initPhoneSDK();
	}, []);



	/**
	 * 获取认证Token
	 */
	const getAuthToken = async () => {
		try {
			const response = await fetch('/api/auth/get-auth-token', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					url: window.location.origin + '/',
					origin: window.location.origin,
					sceneCode: import.meta.env.VITE_ALIYUN_SCENE_CODE,
					bizType: 1
				})
			});

			const data = await response.json();
			if (!response.ok) {
				throw new Error(data.message || '获取认证Token失败');
			}

			return data.tokenInfo;
		} catch (error) {
			console.error('获取认证Token失败:', error);
			throw error;
		}
	};

	/**
	 * 处理手机号一键登录
	 */
	const handlePhoneLogin = async () => {
		if (!phoneNumberServer) {
			setError('手机号认证SDK未初始化');
			return;
		}

		setIsPhoneLoading(true);
		setError('');

		try {
			// 1. 获取认证Token
			const tokenInfo = await getAuthToken();
			console.log('获取到认证Token:', tokenInfo);

			// 2. 检查登录可用性
			phoneNumberServer.checkLoginAvailable({
				accessToken: tokenInfo.AccessToken,
				jwtToken: tokenInfo.JwtToken,
				success: (res: any) => {
					console.log('鉴权成功:', res);
					if (res.code === 600000) {
						// 3. 获取登录Token
						phoneNumberServer.getLoginToken({
							authPageOption: {
								navText: "中化兴海",
								subtitle: "手机号一键登录",
								isHideLogo: false,
								btnText: "立即登录",
								agreeSymbol: "、",
								privacyOne: ["《用户服务协议》", "/policy/service"],
								privacyTwo: ["《隐私政策》", "/policy/privacy"],
								privacyBefore: "我已阅读并同意",
								privacyEnd: "",
								vendorPrivacyPrefix: "《",
								vendorPrivacySuffix: "》",
								privacyVenderIndex: 2,
								isDialog: true,
								manualClose: true,
								isPrePageType: false,
								isShowProtocolDesc: false,
							},
							success: async (res: any) => {
								console.log('获取登录Token成功:', res);
								if (res.code === 600000) {
									try {
										// 4. 通过spToken获取手机号
										const phoneResponse = await fetch('/api/auth/get-phone-with-token', {
											method: 'POST',
											headers: {
												'Content-Type': 'application/json',
											},
											body: JSON.stringify({
												spToken: res.spToken
											})
										});

										const phoneData = await phoneResponse.json();
										if (!phoneResponse.ok) {
											throw new Error(phoneData.message || '获取手机号失败');
										}

										console.log('获取手机号成功:', phoneData);

										// 5. 创建用户信息并登录
										const userData = {
											id: Date.now(), // 临时ID，实际应该从后端获取
											name: `用户${phoneData.data.Mobile.substring(7)}`,
											dingTalkUnionId: null,
											dingTalkUserId: null,
											mobile: phoneData.data.Mobile,
											isAdmin: false,
											token: 1000,
											requestTimes: 0,
											createdAt: new Date().toISOString(),
											updatedAt: new Date().toISOString()
										};

										const authToken = `phone_${phoneData.data.Mobile}_${Date.now()}`;

										// 登录用户
										login(userData, authToken);
										setUser(userData);

										// 关闭授权页面
										phoneNumberServer.closeLoginPage();

										// 跳转到主页
										router.navigate({ to: '/' });
										setIsPhoneLoading(false);

									} catch (error) {
										console.error('手机号登录失败:', error);
										setError(error instanceof Error ? error.message : '手机号登录失败');
										phoneNumberServer.closeLoginPage();
									}
								}
							},
							error: (res: any) => {
								console.error('获取登录Token失败:', res);
								setError('获取登录Token失败，请重试');
								setIsPhoneLoading(false);
							}
						});
					}
				},
				error: (res: any) => {
					console.error('鉴权失败:', res);
					setError('手机号登录鉴权失败，请确保已开启移动数据网络');
					setIsPhoneLoading(false);
				}
			});
		} catch (error) {
			console.error('手机号登录失败:', error);
			setError(error instanceof Error ? error.message : '手机号登录失败');
			setIsPhoneLoading(false);
		}
	};

	/**
	 * 处理钉钉登录
	 */
	const handleDingTalkLogin = () => {
		// 钉钉应用配置
		const clientId = import.meta.env.VITE_DINGTALK_CLIENT_ID || "your_client_id";
		const redirectUri = encodeURIComponent(
			window.location.origin + "/auth/dingtalk/callback"
		);
		const state = "dingtalk_login";

		if (!clientId || clientId === "your_client_id") {
			alert("钉钉应用配置不完整，请检查环境变量 VITE_DINGTALK_CLIENT_ID");
			return;
		}

		// 构建钉钉OAuth2.0授权URL
		const authUrl = `https://login.dingtalk.com/oauth2/auth?response_type=code&client_id=${clientId}&redirect_uri=${redirectUri}&state=${state}&scope=openid%20corpid&prompt=consent`;

		// 跳转到钉钉授权页面
		window.location.href = authUrl;
	};

	return (
		<div className="min-h-screen w-full flex items-center justify-center bg-white rounded-xl z-1">
			<div className="w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl shadow-opacity-10 p-8 flex flex-col items-center border border-blue-100 text-black">
				{/* 登录图标 */}
				<div className="flex items-center justify-center w-14 h-14 rounded-2xl bg-white mb-6 shadow-lg shadow-opacity-5">
					<LogIn className="w-7 h-7 text-black" />
				</div>

				{/* 标题和描述 */}
				<h2 className="text-2xl font-semibold mb-2 text-center">用户登录</h2>
				<p className="text-gray-500 text-sm mb-6 text-center">
					使用钉钉账号或手机号一键登录
				</p>

				{/* Error message */}
				{error && (
					<div className="w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm">
						{error}
					</div>
				)}

				{/* 手机号一键登录 */}
				<button
					onClick={handlePhoneLogin}
					disabled={isPhoneLoading || !phoneNumberServer}
					className="w-full bg-gradient-to-b from-green-500 to-green-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition disabled:opacity-50 disabled:cursor-not-allowed mb-4 flex items-center justify-center gap-2"
				>
					<Smartphone className="w-5 h-5" />
					{isPhoneLoading ? "登录中..." : "手机号一键登录"}
				</button>

				{/* 钉钉登录 */}
				<button
					onClick={handleDingTalkLogin}
					disabled={isLoading}
					className="w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition disabled:opacity-50 disabled:cursor-not-allowed mb-6 flex items-center justify-center gap-2"
				>
					<svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
						<path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
					</svg>
					{isLoading ? "登录中..." : "使用钉钉登录"}
				</button>

				{/* 提示信息 */}
				<div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-700 mb-4">
					<div className="flex items-center gap-2 mb-2">
						<LogIn className="w-4 h-4" />
						<span className="font-medium">登录说明</span>
					</div>
					<p className="text-xs">仅限中化兴海内部成员使用</p>
					<p className="text-xs mt-1">• 手机号一键登录需开启移动数据网络</p>
					<p className="text-xs">• 钉钉登录适用于企业内部用户</p>
				</div>

				{/* 帮助信息 */}
				<div className="text-center text-sm text-gray-500 mt-4">
					登录即表示您同意我们的{" "}
					<a
						href="/terms"
						className="text-blue-600 hover:underline font-medium"
					>
						服务条款
					</a>{" "}
					和{" "}
					<a
						href="/privacy"
						className="text-blue-600 hover:underline font-medium"
					>
						隐私政策
					</a>
				</div>
			</div>
		</div>
	);
};

export { SignIn };

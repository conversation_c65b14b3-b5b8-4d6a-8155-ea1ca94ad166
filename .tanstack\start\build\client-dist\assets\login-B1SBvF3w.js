import{f as d,r,j as e}from"./main-Dj4JwwOF.js";import{a as m,u as x}from"./use-auth-BMRtWsEy.js";import{c as h}from"./createLucideIcon-C4-NQwlF.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u=[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]],o=h("log-in",u),b=()=>{d(),m();const{setUser:n}=x(),[t,p]=r.useState(!1),[s,g]=r.useState(""),a=()=>{const c="dingaalizs3sqczrirqd",i=encodeURIComponent(window.location.origin+"/auth/dingtalk/callback"),l=`https://login.dingtalk.com/oauth2/auth?response_type=code&client_id=${c}&redirect_uri=${i}&state=dingtalk_login&scope=openid%20corpid&prompt=consent`;window.location.href=l};return e.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-white rounded-xl z-1",children:e.jsxs("div",{className:"w-full max-w-md bg-gradient-to-b from-sky-50/50 to-white rounded-3xl shadow-xl shadow-opacity-10 p-8 flex flex-col items-center border border-blue-100 text-black",children:[e.jsx("div",{className:"flex items-center justify-center w-14 h-14 rounded-2xl bg-white mb-6 shadow-lg shadow-opacity-5",children:e.jsx(o,{className:"w-7 h-7 text-black"})}),e.jsx("h2",{className:"text-2xl font-semibold mb-2 text-center",children:"用户登录"}),e.jsx("p",{className:"text-gray-500 text-sm mb-6 text-center",children:"使用钉钉账号登录"}),s&&e.jsx("div",{className:"w-full mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm",children:s}),e.jsxs("button",{onClick:a,disabled:t,className:"w-full bg-gradient-to-b from-blue-500 to-blue-600 text-white font-medium py-3 rounded-xl shadow hover:brightness-105 cursor-pointer transition disabled:opacity-50 disabled:cursor-not-allowed mb-6 flex items-center justify-center gap-2",children:[e.jsx("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"currentColor",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"})}),t?"登录中...":"使用钉钉登录"]}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-700 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(o,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:"登录说明"})]}),e.jsx("p",{className:"text-xs",children:"仅限中化兴海内部成员使用钉钉账号登录"})]}),e.jsxs("div",{className:"text-center text-sm text-gray-500 mt-4",children:["登录即表示您同意我们的"," ",e.jsx("a",{href:"/terms",className:"text-blue-600 hover:underline font-medium",children:"服务条款"})," ","和"," ",e.jsx("a",{href:"/privacy",className:"text-blue-600 hover:underline font-medium",children:"隐私政策"})]})]})})},N=function(){return e.jsx(b,{})};export{N as component};

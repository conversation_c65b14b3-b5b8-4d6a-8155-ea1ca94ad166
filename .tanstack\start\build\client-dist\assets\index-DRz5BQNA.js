var u={},A;function m(){if(A)return u;A=1,u.byteLength=y,u.toByteArray=i,u.fromByteArray=B;for(var f=[],o=[],l=typeof Uint8Array<"u"?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",F=0,p=s.length;F<p;++F)f[F]=s[F],o[s.charCodeAt(F)]=F;o[45]=62,o[95]=63;function x(r){var e=r.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var a=r.indexOf("=");a===-1&&(a=e);var n=a===e?0:4-a%4;return[a,n]}function y(r){var e=x(r),a=e[0],n=e[1];return(a+n)*3/4-n}function C(r,e,a){return(e+a)*3/4-a}function i(r){var e,a=x(r),n=a[0],v=a[1],t=new l(C(r,n,v)),h=0,d=v>0?n-4:n,c;for(c=0;c<d;c+=4)e=o[r.charCodeAt(c)]<<18|o[r.charCodeAt(c+1)]<<12|o[r.charCodeAt(c+2)]<<6|o[r.charCodeAt(c+3)],t[h++]=e>>16&255,t[h++]=e>>8&255,t[h++]=e&255;return v===2&&(e=o[r.charCodeAt(c)]<<2|o[r.charCodeAt(c+1)]>>4,t[h++]=e&255),v===1&&(e=o[r.charCodeAt(c)]<<10|o[r.charCodeAt(c+1)]<<4|o[r.charCodeAt(c+2)]>>2,t[h++]=e>>8&255,t[h++]=e&255),t}function L(r){return f[r>>18&63]+f[r>>12&63]+f[r>>6&63]+f[r&63]}function g(r,e,a){for(var n,v=[],t=e;t<a;t+=3)n=(r[t]<<16&16711680)+(r[t+1]<<8&65280)+(r[t+2]&255),v.push(L(n));return v.join("")}function B(r){for(var e,a=r.length,n=a%3,v=[],t=16383,h=0,d=a-n;h<d;h+=t)v.push(g(r,h,h+t>d?d:h+t));return n===1?(e=r[a-1],v.push(f[e>>2]+f[e<<4&63]+"==")):n===2&&(e=(r[a-2]<<8)+r[a-1],v.push(f[e>>10]+f[e>>4&63]+f[e<<2&63]+"=")),v.join("")}return u}export{m as r};
